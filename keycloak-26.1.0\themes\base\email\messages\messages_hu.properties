emailVerificationSubject=E-mail cím megerősítése
emailVerificationBody=Ezzel az e-mail címmel valaki létrehozott egy {2} tartomány felhasználói fiókot. Amennyiben a fiókot Ön hozta létre, k<PERSON>rem, kattintson a lenti hivatkozásra, hogy megerősítse fiókját és ezt az e-mail címet.\n\n{0}\n\nA hivatkozás érvényét veszti {3} múlva.\n\nHa nem ön hozta létre a felhasználói fiókot, akkor kérem, hagyja figyelmen kívül ezt az üzenetet.
emailVerificationBodyHtml=<p>Ezzel az e-mail címmel valaki létrehozott egy {2} tartomány felhasználói fiókot. Amennyiben a fiókot Ön hozta létre, k<PERSON>rem, kattintson a lenti hivatkozásra, hogy megerősítse fiókját és ezt az e-mail címet.</p><p><a href="{0}">Hivatkozás a fiók és az e-mail cím megerősítéséhez</a></p><p>A hivatkozás érvényét veszti {3} múlva.</p><p>Ha nem ön hozta létre a felhasználói fiókot, akkor kérem, hagyja figyelmen kívül ezt az üzenetet.</p>
emailUpdateConfirmationSubject=Új e-mail cím megerősítése
emailUpdateConfirmationBody=Valaki módosítani szeretné az Ön "{2}" tartományi fiókjának e-mail címét. Amennyiben az e-mail cím módosítását Ön kezdeményezte, kérem, kattintson a lenti hivatkozásra a jóváhagyáshoz\n\n{0}\n\nA hivatkozás érvényét veszti {3} múlva.\n\nHa nem ön kérte az e-mail cím megváltoztatását, akkor kérem, hagyja figyelmen kívül ezt az üzenetet, az e-mail címe nem fog megváltozni.
***emailUpdateConfirmationBodyHtml=<p>Valaki módosítani szeretné az Ön <b>{2}</b> tartományi fiókjának e-mail címét. Amennyiben az e-mail cím módosítását Ön kezdeményezte, kérem, kattintson a lenti hivatkozásra a jóváhagyáshoz</p><p><a href="{0}">{0}</a></p><p>A hivatkozás érvényét veszti {3} múlva.</p><p>Ha nem ön kérte az e-mail cím megváltoztatását, akkor kérem, hagyja figyelmen kívül ezt az üzenetet, az e-mail címe nem fog megváltozni.</p>
emailTestSubject=[KEYCLOAK] - SMTP teszt üzenet
emailTestBody=Ez egy KEYCLOAK teszt üzenet.
emailTestBodyHtml=<p>Ez egy KEYCLOAK teszt üzenet.</p>
identityProviderLinkSubject={0} összekötés
identityProviderLinkBody=Valaki össze kívánja kötni az Ön "{1}" tartományi fiókját a(z) "{0}" személyazonosság-kezelő {2} felhasználói fiókjával. Amennyiben az összekötést Ön kezdeményezte kérem, kattintson a lenti hivatkozásra, hogy összekösse fiókjait.\n\n{3}\n\nA hivatkozás érvényét veszti {5} múlva.\n\nHa nem ön kezdeményezte a felhasználói fiókok összekötését, akkor kérem, hagyja figyelmen kívül ezt az üzenetet.\n\nHa összeköti a fiókjait, akkor beléphet a(z) {1} tartományba a(z) {0} szolgáltatón keresztül.
identityProviderLinkBodyHtml=<p>Valaki össze kívánja kötni az Ön <b>{1}</b> tartomány fiókját a(z) <b>{0}</b> személyazonosság-kezelő {2} felhasználói fiókjával. Amennyiben az összekötést Ön kezdeményezte kérem, kattintson a lenti hivatkozásra, hogy összekösse fiókjait.</p><p><a href="{3}">Hivatkozás a fiók összekötés megerősítéshez</a></p><p>A hivatkozás érvényét veszti {5} múlva.</p><p>Ha nem ön kezdeményezte a felhasználói fiókok összekötését, akkor kérem, hagyja figyelmen kívül ezt az üzenetet.</p><p>Ha összeköti a fiókjait, akkor beléphet a(z) {1} tartományba a(z) {0} szolgáltatón keresztül.</p>
passwordResetSubject=Jelszó visszaállítás
passwordResetBody=Valaki vissza kívánja állítani az Ön "{2}" tartományi fiókjának jelszavát. Amennyiben a jelszó visszaállítást Ön kezdeményezte, kérem, kattintson a lenti hivatkozásra a jelszava megváltoztatásához.\n\n{0}\n\nA hivatkozás érvényét veszti {3} múlva.\n\nHa nem ön kérte a jelszó visszaállítást, akkor kérem, hagyja figyelmen kívül ezt az üzenetet, a jelszava nem fog megváltozni.
passwordResetBodyHtml=<p>Valaki vissza kívánja állítani az Ön "{2}" tartományi fiókjának jelszavát. Amennyiben a jelszó visszaállítást Ön kezdeményezte, kérem, kattintson a lenti hivatkozásra a jelszava megváltoztatásához.</p><p><a href="{0}">Hivatkozás a jelszó visszaállításhoz</a></p><p>A hivatkozás érvényét veszti {3} múlva.</p><p>Ha nem ön kérte a jelszó visszaállítást, akkor kérem, hagyja figyelmen kívül ezt az üzenetet, a jelszava nem fog megváltozni.</p>
executeActionsSubject=Felhasználói fiók adatok módosítása
executeActionsBody=Az alkalmazás adminisztrátora kezdeményezte az Ön "{2}" tartományi felhasználói fiók adatainak módosítását a következő műveletekkel: {3}. Kérem, kattintson a lenti hivatkozásra, hogy megkezdhesse a kért módosításokat.\n\n{0}\n\nA hivatkozás érvényét veszti {4} múlva.\n\nHa nincs tudomása arról, hogy az adminisztrátora módosításokat kért Öntől, akkor kérem, hagyja figyelmen kívül ezt az üzenetet, az adatai nem fognak megváltozni.
executeActionsBodyHtml=<p>Az alkalmazás adminisztrátora kezdeményezte az Ön "{2}" tartományi felhasználói fiók adatainak módosítását a következő műveletekkel: {3}. Kérem, kattintson a lenti hivatkozásra, hogy megkezdhesse a kért módosításokat.</p><p><a href="{0}">Hivatkozás a felhasználói fiók adatok módosításához</a></p><p>A hivatkozás érvényét veszti {4} múlva.</p><p>Ha nincs tudomása arról, hogy az adminisztrátora módosításokat kért Öntől, akkor kérem, hagyja figyelmen kívül ezt az üzenetet, az adatai nem fognak megváltozni.</p>
eventLoginErrorSubject=Belépési hiba
eventLoginErrorBody=Sikertelen belépési kísérlet történt {0} időpontban a(z) {1} címről. Kérem, lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön próbált meg belépni.
eventLoginErrorBodyHtml=<p>Sikertelen belépési kísérlet történt {0} időpontban a(z) {1} címről. Kérem, lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön próbált meg belépni.</p>
eventRemoveTotpSubject=Egyszer használatos jelszó (OTP) eltávolítása
eventRemoveTotpBody=Az egyszer használatos jelszó (OTP) funkciót {0} időpontban a(z) {1} címről érkező kérés értelmében eltávolítottuk a fiókjáról. Kérem, haladéktalanul lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön igényelte az OTP eltávolítását.
eventRemoveTotpBodyHtml=<p>Az egyszer használatos jelszó (OTP) funkciót {0} időpontban a(z) {1} címről érkező kérés értelmében eltávolítottuk a fiókjáról. Kérem, haladéktalanul lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön igényelte az OTP eltávolítását.</p>
eventUpdatePasswordSubject=Jelszó csere
eventUpdatePasswordBody=Jelszavát {0} időpontban a(z) {1} címről érkező kérés értelmében lecseréltük. Kérem, haladéktalanul lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön igényelte a jelszó cserét.
eventUpdatePasswordBodyHtml=<p>Jelszavát {0} időpontban a(z) {1} címről érkező kérés értelmében lecseréltük. Kérem, haladéktalanul lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön igényelte a jelszó cserét.</p>
eventUpdateTotpSubject=Egyszer használatos jelszó (OTP) csere
eventUpdateTotpBody=Az egyszer használatos jelszó (OTP) beállításait {0} időpontban a(z) {1} címről érkező kérés értelmében módosítottuk a fiókján. Kérem, haladéktalanul lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön igényelte az OTP beállítások módosítását.
eventUpdateTotpBodyHtml=<p>Az egyszer használatos jelszó (OTP) beállításait {0} időpontban a(z) {1} címről érkező kérés értelmében módosítottuk a fiókján. Kérem, haladéktalanul lépjen kapcsolatba az alkalmazás adminisztrátorával amennyiben nem ön igényelte az OTP beállítások módosítását.</p>

requiredAction.CONFIGURE_TOTP=Egyszer használatos jelszó (OTP) beállítása
requiredAction.TERMS_AND_CONDITIONS=Felhasználási feltételek
requiredAction.UPDATE_PASSWORD=Jelszó csere
requiredAction.UPDATE_PROFILE=Fiók adatok módosítása
requiredAction.VERIFY_EMAIL=E-mail cím megerősítése
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Biztonsági kódok generálása

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds=másodperc
linkExpirationFormatter.timePeriodUnit.minutes=perc
linkExpirationFormatter.timePeriodUnit.hours=óra
linkExpirationFormatter.timePeriodUnit.days=nap

emailVerificationBodyCode=Kérem, erősítse meg az e-mail címét a következő kód megadásával.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Kérem, erősítse meg az e-mail címét a következő kód megadásával.</p><p><b>{0}</b></p>

