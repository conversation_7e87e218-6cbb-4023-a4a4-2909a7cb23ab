/* Two-column layout */
.two-column-layout {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-evenly;
}

.image-column {
    flex: 1 1 0%; /* Image column takes 40% of the width */
    max-width: 50%;
}

.image-column img {
    width: 100%;
    height: auto;
    border-radius: 8px; /* Optional: Add rounded corners */
}


.form-column {
    display: flex;
    justify-content: center; /* Centrer horizontalement */
    align-items: center; /* Centrer verticalement */
    height: 100%; /* Prendre toute la hauteur disponible */
    max-width: 50%;
    flex-direction: column;
    row-gap: 25px; /* Espacement vertical entre les éléments */
}

#kc-form,#kc-form-OTP  {
    width: 100%; /* Prendre toute la largeur disponible */
    max-width: 400px; /* Limiter la largeur pour une meilleure lisibilité */
    align-items: center; /* Centrer verticalement */

}

#kc-form-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#kc-info {
    max-width: 400px; /* Limiter la largeur pour une meilleure lisibilité */
    align-items: center; /* Centrer verticalement */
}


/* Responsive adjustments */
@media (max-width: 768px) {
    .two-column-layout {
        flex-direction: column;
        justify-content: center; /* Center vertically */
        align-items: center; /* Center horizontally */
        height: 100vh; /* Full viewport height to center vertically */
    }

    .image-column {
        display: none; /* Hide the image on small screens */
    }

    .form-column {
        flex: 1 1 100%; /* Form column takes full width */
        max-width: 100%;
        display: flex;
        justify-content: center; /* Center horizontally */
        align-items: center; /* Center vertically */
    }

    .form-column form {
        width: 100%; /* Full width of the form column */
        max-width: 400px; /* Limit form width for better readability */
        padding: 20px; /* Add some padding for better spacing */
    }
}










.login-pf body {
    background: #F8F9FE !important;  /* Fond très légèrement violet */
    height: 100%;
}


.card-pf {
    border: none !important;
    box-shadow: none !important;
    margin: 10 !important;
    padding: 90 !important;
    font-family: inherit !important;
    height: 100%;
    width: 80.66% !important;
    position: absolute;
    right: 10%;
    top: 50%;
    transform: translateY(-50%);
    background: #FFFFFF !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 24px rgba(149, 157, 165, 0.1) !important;
}



#title, #update-password-title {
    color: #4F46E5 !important; /* Indigo moderne */
    font-weight: bold;
    font-size: 24px;
    font-family: inter;
    margin-bottom: 20px;
}



#background {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    border-radius: 10px 0 0 10px;
    filter: blur(0.5px); /* Blur effect */
    z-index: 1; /* Place it below the second image */
}

#back {
    position: absolute;
    top: 10%; /* Adjust this to position it closer to the top */
    margin-bottom: 10%;
    left: 0%; /* Move to the left side */
    width: 50%;
    height: 80%;
    z-index: 2; /* Make sure it is above the blurred background */
}

#username, #password, #password-new, #password-confirm, #kc-otp-input {
    border: 1px solid #E5E7EB !important;
    background: #F9FAFB !important;
    transition: all 0.3s ease !important;
}

#username:focus, #password:focus, #password-new:focus, #password-confirm:focus, #kc-otp-input:focus {
    border-color: #4F46E5 !important;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
    outline: none !important;
}

#kc-otp-login-form{
    width: 100%;}

#kc-login {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%) !important;
    width: 100%;
    font-size: 18px;
    padding: 15px;
    color: #fff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

#kc-login:disabled {
background-color: #ccc;
}

#kc-login:hover:enabled {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(79, 70, 229, 0.3);
}
.MotOublié {
    margin-bottom: 0; /* Reset any margin to align properly */
    justify-content: space-between;
    align-self: center; /* Vertically center the "Mot de passe oublié" link */

}

.pass {
    display: flex;
    align-items: center;                /* Centrer verticalement */
    position: relative;
    width: 100%;
}

/* Style pour l'input du mot de passe */
#password, #password-new, #password-confirm {
    height: 45px;
    padding: 15px 15px 15px 15px;       /* Ajout de padding à gauche et à droite */
    font-size: 16px;
    width: 100%;
    padding-right: 40px;                 /* Espace à droite pour l'icône */
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
    margin-bottom: 10px;
    box-sizing: border-box;              /* Inclure padding dans la largeur totale */
}

/* Style pour l'icône */
.eye {
    top: 5px;
    position: absolute;
    right: 15px;                        /* Espacement à droite de l'input */
    font-size: 18px;
    color: gray;
    background-color: transparent;
    border: none;
    cursor: pointer;
    outline: none;
}

/* Ajouter un style pour le conteneur qui englobe l'input et l'icône */
.pass {
    position: relative;
    width: 100%;
}

.kc-form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.kc-form-options .checkbox {
    margin-right: 130px;
}




/**/
.subtitle {
    text-align: right;
    margin-top: 30px;
    color: #909090;
}


/*steps*/

/* password*/



#kc-totp-secret-qr-code {
    max-width:150px;
    max-height:150px;

}



/* Conteneur pour centrer les boutons */


a#mode-manual{
    color: #EF9129;
}

a#mode-barcode{color: #EF9129;
}

#saveTOTPBtn {
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    min-width: 120px;
    text-align: center;
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
    transition: all 0.3s ease;
}
#cancelTOTPBtn1 {
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    min-width: 120px;
    text-align: center;
    background: linear-gradient(135deg, #DC2626 0%, #EF4444 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
    transition: all 0.3s ease;
}



#cancelTOTPBtn {
    color: #EF9129;
    background-color : #EF9129;
}
span.required{
    color: #EF4444;}
/* Pour le message d'erreur */
#input-error-otp-label {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    text-align: center;
}

/* Style des labels */
.control-label {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.userLabel {
    font-size: 14px;
    color: #555;
}



#kc-otp-input {
    height: 100%!important;
    width: 100% !important;
    padding: 15px !important;
    font-size: 16px !important;
    border: 1px solid #ddd !important;
    border-radius: 5px !important;
    background: #f9f9f9 !important;
    margin-bottom: 10px !important;
    box-sizing: border-box !important;
}

#kc-login-submit {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%) !important;
    width: 100% !important;
    font-size: 18px !important;
    padding: 15px !important;
    color: #fff !important;
    border: none !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2) !important;
}
    
#kc-login-submit:disabled {
    background-color: #ccc !important;
}

#kc-login-submit:hover:enabled {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(79, 70, 229, 0.3) !important;
}


#kc-username {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.98);
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.1);
    z-index: 1000;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(79, 70, 229, 0.1);
}

#kc-username label#kc-attempted-username {
    font-size: 14px;
    color: #4F46E5;
    font-weight: 600;
}

#kc-username i.pficon.pficon-arrow.fa {
    color: #7C3AED;
    margin-left: 8px;
}

.kc-login-tooltip {
    display: inline-flex;
    align-items: center;
    background: rgba(79, 70, 229, 0.05);
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.kc-login-tooltip:hover {
    background: rgba(79, 70, 229, 0.1);
}
