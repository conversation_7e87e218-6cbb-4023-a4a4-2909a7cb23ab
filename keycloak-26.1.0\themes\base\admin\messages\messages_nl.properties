invalidPasswordMinLengthMessage=Ongeldig wachtwoord: de minimale lengte is {0} karakters.
invalidPasswordMinLowerCaseCharsMessage=Ongeldig wachtwoord: het moet minstens {0} kleine letters bevatten.
invalidPasswordMinDigitsMessage=Ongeldig wachtwoord: het moet minstens {0} getallen bevatten.
invalidPasswordMinUpperCaseCharsMessage=Ongeldig wachtwoord: het moet minstens {0} hoofdletters bevatten.
invalidPasswordMinSpecialCharsMessage=Ongeldig wachtwoord: het moet minstens {0} speciale karakters bevatten.
invalidPasswordNotUsernameMessage=Ongeldig wachtwoord: het mag niet overeenkomen met de gebruikersnaam.
invalidPasswordRegexPatternMessage=Ongeldig wachtwoord: het voldoet niet aan het door de beheerder ingestelde patroon.
invalidPasswordHistoryMessage=Ongeldig wachtwoord: het mag niet overeen komen met een van de laatste {0} wachtwoorden.
invalidPasswordGenericMessage=Ongeldig wachtwoord: het nieuwe wachtwoord voldoet niet aan het wachtwoordbeleid.

ldapErrorInvalidCustomFilter=LDAP filter met aangepaste configuratie start niet met "(" of eindigt niet met ")".
ldapErrorConnectionTimeoutNotNumber=Verbindingstimeout moet een getal zijn
ldapErrorReadTimeoutNotNumber=Lees-timeout moet een getal zijn
ldapErrorMissingClientId=Client ID moet ingesteld zijn als Realm Roles Mapping niet gebruikt wordt.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Kan groepsovererving niet behouden bij UID-lidmaatschapstype.
ldapErrorCantWriteOnlyForReadOnlyLdap=Alleen-schrijven niet mogelijk als LDAP provider mode niet WRITABLE is
ldapErrorCantWriteOnlyAndReadOnly=Alleen-schrijven en alleen-lezen mogen niet tegelijk ingesteld zijn

clientRedirectURIsFragmentError=Redirect URIs mogen geen URI fragment bevatten
clientRootURLFragmentError=Root URL mag geen URL fragment bevatten

pairwiseMalformedClientRedirectURI=Client heeft een ongeldige redirect URI.
pairwiseClientRedirectURIsMissingHost=Client redirect URIs moeten een geldige host-component bevatten.
pairwiseClientRedirectURIsMultipleHosts=Zonder een geconfigureerde Sector Identifier URI mogen client redirect URIs niet meerdere host componenten hebben.
pairwiseMalformedSectorIdentifierURI=Onjuist notatie in Sector Identifier URI.
pairwiseFailedToGetRedirectURIs=Kon geen redirect URIs verkrijgen van de Sector Identifier URI.
pairwiseRedirectURIsMismatch=Client redirect URIs komen niet overeen met redict URIs ontvangen van de Sector Identifier URI.
