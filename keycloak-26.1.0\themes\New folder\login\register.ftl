<#import "template.ftl" as layout>
<#import "user-profile-commons.ftl" as userProfileCommons>
<#import "register-commons.ftl" as registerCommons>
<@layout.registrationLayout displayMessage=messagesPerField.exists('global') displayRequiredFields=true; section>
    <#if section = "header">
        <#if messageHeader??>
            ${kcSanitize(msg("${messageHeader}"))?no_esc}
        <#else>
            ${msg("registerTitle")}
        </#if>
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form">
                <div id="kc-form-wrapper">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("registerTitle")}</h1>
                        <p class="form-subtitle">${msg("registerSubtitle")}</p>
                    </div>

                    <form id="kc-register-form" class="${properties.kcFormClass!}" action="${url.registrationAction}" method="post">
                        <@userProfileCommons.userProfileFormFields; callback, attribute>
                            <#if callback = "afterField">
                                <#if passwordRequired?? && (attribute.name == 'username' || (attribute.name == 'email' && realm.registrationEmailAsUsername))>
                                    <div class="form-floating">
                                        <input type="password" 
                                               id="password" 
                                               class="form-control ${messagesPerField.existsError('password','password-confirm')?then('is-invalid', '')}"
                                               name="password"
                                               autocomplete="new-password"
                                               placeholder=" "
                                               aria-invalid="<#if messagesPerField.existsError('password','password-confirm')>true</#if>"
                                        />
                                        <span class="form-icon password-icon" aria-hidden="true"></span>
                                        <label for="password">${msg("password")} *</label>
                                        <button class="password-toggle" 
                                                type="button" 
                                                aria-label="${msg('showPassword')}"
                                                aria-controls="password"
                                                data-password-toggle>
                                            <i class="fa fa-eye" aria-hidden="true"></i>
                                        </button>

                                        <#if messagesPerField.existsError('password')>
                                            <span id="input-error-password" class="error-message" aria-live="polite">
                                                ${kcSanitize(messagesPerField.get('password'))?no_esc}
                                            </span>
                                        </#if>
                                    </div>

                                    <div class="form-floating">
                                        <input type="password" 
                                               id="password-confirm" 
                                               class="form-control ${messagesPerField.existsError('password-confirm')?then('is-invalid', '')}"
                                               name="password-confirm"
                                               autocomplete="new-password"
                                               placeholder=" "
                                               aria-invalid="<#if messagesPerField.existsError('password-confirm')>true</#if>"
                                        />
                                        <span class="form-icon password-icon" aria-hidden="true"></span>
                                        <label for="password-confirm">${msg("passwordConfirm")} *</label>
                                        <button class="password-toggle" 
                                                type="button" 
                                                aria-label="${msg('showPassword')}"
                                                aria-controls="password-confirm"
                                                data-password-toggle>
                                            <i class="fa fa-eye" aria-hidden="true"></i>
                                        </button>

                                        <#if messagesPerField.existsError('password-confirm')>
                                            <span id="input-error-password-confirm" class="error-message" aria-live="polite">
                                                ${kcSanitize(messagesPerField.get('password-confirm'))?no_esc}
                                            </span>
                                        </#if>
                                    </div>
                                </#if>
                            </#if>
                        </@userProfileCommons.userProfileFormFields>

                        <@registerCommons.termsAcceptance/>

                        <#if recaptchaRequired?? && (recaptchaVisible!false)>
                            <div class="form-group">
                                <div class="g-recaptcha" data-size="compact" data-sitekey="${recaptchaSiteKey}" data-action="${recaptchaAction}"></div>
                            </div>
                        </#if>

                        <div class="form-group">
                            <div id="kc-form-options">
                                <div class="form-links">
                                    <a href="${url.loginUrl}" class="form-link">${kcSanitize(msg("backToLogin"))?no_esc}</a>
                                </div>
                            </div>

                            <#if recaptchaRequired?? && !(recaptchaVisible!false)>
                                <script>
                                    function onSubmitRecaptcha(token) {
                                        document.getElementById("kc-register-form").requestSubmit();
                                    }
                                </script>
                                <div id="kc-form-buttons">
                                    <button class="btn btn-primary btn-block btn-lg g-recaptcha" 
                                        data-sitekey="${recaptchaSiteKey}" 
                                        data-callback='onSubmitRecaptcha' 
                                        data-action='${recaptchaAction}' 
                                        type="submit">
                                        ${msg("doRegister")}
                                    </button>
                                </div>
                            <#else>
                                <div id="kc-form-buttons">
                                    <input class="btn btn-primary btn-block btn-lg" type="submit" value="${msg("doRegister")}"/>
                                </div>
                            </#if>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </#if>
</@layout.registrationLayout>
