emailVerificationSubject=Підтвердити адресу електронної пошти
emailVerificationBody=Хтось створив обліковий запис {2} із цією адресою електронної пошти. Якщо це були Ви, натисніть на посилання нижче, щоб підтвердити свою адресу електронної пошти\n\n{0}\n\nЦе посилання дійсне протягом {3}.\n\nЯкщо Ви не створювали цей обліковий запис, просто проігноруйте цей лист.
emailVerificationBodyHtml=<p>Хтось створив обліковий запис {2} із цією адресою електронної пошти. Якщо це були Ви, натисніть на посилання нижче, щоб підтвердити свою адресу електронної пошти</p><p><a href="{0}">Посилання на підтвердження адресу електронної пошти</a></p><p>Це посилання дійсне протягом {3}.</p><p>Якщо Ви не створювали цей обліковий запис, просто проігноруйте цей лист.</p>
emailUpdateConfirmationSubject=Підтвердіть нову адресу електронної пошти
emailUpdateConfirmationBody=Щоб оновити адресу електронної пошти {1} облікового запису {2}, натисніть на посилання нижче\n\n{0}\n\nЦе посилання дійсне протягом {3}.\n\nЯкщо Ви не бажаєте застосувати ці зміни, просто проігноруйте цей лист.
emailUpdateConfirmationBodyHtml=<p>Щоб оновити адресу електронної пошти {1} облікового запису {2}, натисніть на посилання нижче</p><p><a href="{0}">{0}</a></p ><p>Це посилання дійсне протягом {3}.</p><p>Якщо Ви не бажаєте застосувати ці зміни, просто проігноруйте цей лист.</p>
emailTestSubject=[KEYCLOAK] - тестове повідомлення SMTP
emailTestBody=Це тестове повідомлення
emailTestBodyHtml=<p>Це тестове повідомлення</p>
identityProviderLinkSubject=Посилання {0}
identityProviderLinkBody=Хтось хоче зв''язати Ваш обліковий запис "{1}" з обліковим записом "{0}" користувача {2}. Якщо це були Ви, натисніть на посилання нижче, щоб зв''язати облікові записи\n\n{3}\n\nЦе посилання дійсне протягом {5}.\n\nЯкщо Ви не бажаєте зв''язати облікові записи, просто проігноруйте цей лист. Якщо зв''язати облікові записи, Ви зможете увійти в {1} через {0}.
identityProviderLinkBodyHtml=<p>Хтось хоче зв''язати Ваш обліковий запис <b>{1}</b> з обліковим записом <b>{0}</b> користувача {2}. Якщо це були Ви, натисніть на посилання нижче, щоб зв''язати облікові записи</p><p><a href="{3}">Посилання для підтвердження зв''язування облікових записів</a></p><p>Це посилання дійсне протягом {5}.</p><p>Якщо Ви не бажаєте зв''язати облікові записи, просто проігноруйте цей лист. Якщо зв''язати облікові записи, Ви зможете увійти в {1} через {0}.</p>
passwordResetSubject=Скинути пароль
passwordResetBody=Хтось щойно запросив зміну паролю Вашого облікового запису {2}. Якщо це були Ви, натисніть на посилання нижче, щоб скинути пароль.\n\n{0}\n\nЦе посилання дійсне протягом {3}.\n\nЯкщо Ви не бажаєте скинути пароль, просто проігноруйте цей лист, і нічого не зміниться.
passwordResetBodyHtml=<p>Хтось щойно запросив зміну паролю Вашого облікового запису {2}. Якщо це були Ви, натисніть на посилання нижче, щоб скинути пароль.</p><p><a href="{0}">Посилання для скидання паролю</a></p><p>Це посилання дійсне протягом {3}.</p><p>Якщо Ви не бажаєте скинути пароль, просто проігноруйте цей лист, і нічого не зміниться.</p>
executeActionsSubject=Оновлення Вашого облікового запису
executeActionsBody=Адміністратор просить Вас оновити інформацію Вашого облікового запису {2}. Натисніть на посилання нижче, щоб розпочати цей процес.\n\n{0}\n\nЦе посилання дійсне протягом {4}.\n\nЯкщо у Вас є сумніви щодо того, що адміністратор дійсно подав такий запит, просто проігноруйте цей лист.
executeActionsBodyHtml=<p>Адміністратор просить Вас оновити інформацію Вашого облікового запису {2}. Натисніть на посилання нижче, щоб почати цей процес.</p><p><a href="{0}">Посилання на оновлення інформації облікового запису</a></p><p>Це посилання дійсне протягом {4} .</p><p>Якщо у Вас є сумніви щодо того, що адміністратор дійсно подав такий запит, просто проігноруйте цей лист.</p>
eventLoginErrorSubject=Помилка входу
eventLoginErrorBody=Виявлено невдалу спробу входу до Вашого облікового запису {0} з {1}. Якщо це були не Ви, зверніться до адміністратора.
eventLoginErrorBodyHtml=<p>Виявлено невдалу спробу входу до Вашого облікового запису {0} з {1}. Якщо це були не Ви, зверніться до адміністратора.</p>
eventRemoveTotpSubject=Видалення OTP
eventRemoveTotpBody=OTP було видалено з Вашого облікового запису {0} з {1}. Якщо це були не Ви, зверніться до адміністратора.
eventRemoveTotpBodyHtml=<p>OTP було видалено з Вашого облікового запису {0} з {1}. Якщо це були не Ви, зверніться до адміністратора.</p>
eventUpdatePasswordSubject=Зміна паролю
eventUpdatePasswordBody=Ваш пароль був змінений {0} з {1}. Якщо це були не Ви, зверніться до адміністратора.
eventUpdatePasswordBodyHtml=<p>Ваш пароль був змінений {0} з {1}. Якщо це були не Ви, зверніться до адміністратора.</p>
eventUpdateTotpSubject=Оновлення OTP
eventUpdateTotpBody=OTP було оновлено для Вашого облікового запису {0} від {1}. Якщо це були не Ви, зверніться до адміністратора.
eventUpdateTotpBodyHtml=<p>OTP було оновлено для Вашого облікового запису {0} з {1}. Якщо це були не Ви, зверніться до адміністратора.</p>

requiredAction.CONFIGURE_TOTP=Налаштувати OTP
requiredAction.TERMS_AND_CONDITIONS=Умови
requiredAction.UPDATE_PASSWORD=Змінити пароль
requiredAction.UPDATE_PROFILE=Оновити профіль
requiredAction.VERIFY_EMAIL=Підтвердити адресу електронної пошти
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Генерувати коди відновлення

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#секунд|1#секунди|1<секунд}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#хвилин|1#хвилини|1<хвилин}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#годин|1#години|1<годин}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#днів|1#дня|1<днів}

emailVerificationBodyCode=Будь ласка, підтвердіть свою адресу електронної пошти, ввівши наступний код.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Будь ласка, підтвердіть свою адресу електронної пошти, ввівши наступний код.</p><p><b>{0}</b></p>
