<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('username','password') displayInfo=realm.password && realm.registrationAllowed && !registrationDisabled??; section>
    <#if section = "header">
        <!--${msg("loginAccountTitle")}-->
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form-wrapper">
                <div id="login-container">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo responsive-img">
                        <div class="form-title-group">
                            <h1 class="form-title">${msg("loginAccountTitle")}</h1>
                            <p class="form-subtitle">Entrez vos identifiants pour vous connecter</p>
                        </div>
                    </div>

                    <#if realm.password>
                        <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}" method="post">
                            <#if !usernameHidden??>
                                <div class="form-floating">
                                    <input 
                                        tabindex="2" 
                                        id="username" 
                                        class="form-control ${messagesPerField.existsError('username','password')?then('is-invalid', '')}" 
                                        name="username" 
                                        value="${(login.username!'')}" 
                                        type="text"
                                        autofocus 
                                        autocomplete="username"
                                        placeholder=" "
                                        required
                                        aria-invalid="<#if messagesPerField.existsError('username','password')>true</#if>"
                                    />
                                    <#if !realm.loginWithEmailAllowed>
                                        <span class="form-icon username-icon" aria-hidden="true"></span>
                                    <#elseif !realm.registrationEmailAsUsername>
                                        <span class="form-icon username-icon" aria-hidden="true"></span>
                                    <#else>
                                        <span class="form-icon email-icon" aria-hidden="true"></span>
                                    </#if>
                                    <label for="username" class="form-label">
                                        <#if !realm.loginWithEmailAllowed>
                                            ${msg("username")}
                                        <#elseif !realm.registrationEmailAsUsername>
                                            ${msg("usernameOrEmail")}
                                        <#else>
                                            ${msg("email")}
                                        </#if>
                                    </label>
                                    
                                    <#if messagesPerField.existsError('username','password')>
                                        <span id="input-error-username" class="error-message" aria-live="polite">
                                            ${kcSanitize(messagesPerField.getFirstError('username','password'))?no_esc}
                                        </span>
                                    </#if>
                                </div>
                            </#if>

                            <div class="form-floating">
                                <input 
                                    tabindex="3" 
                                    id="password" 
                                    class="form-control ${messagesPerField.existsError('username','password')?then('is-invalid', '')}"
                                    name="password" 
                                    type="password" 
                                    autocomplete="current-password"
                                    placeholder=" "
                                    required
                                    aria-invalid="<#if messagesPerField.existsError('username','password')>true</#if>"
                                />
                                <span class="form-icon password-icon" aria-hidden="true"></span>
                                <label for="password" class="form-label">
                                    ${msg("password")}
                                </label>
                                <button class="password-toggle" 
                                        type="button" 
                                        aria-label="${msg('showPassword')}"
                                        aria-controls="password" 
                                        data-password-toggle 
                                        tabindex="4">
                                    <i class="fa fa-eye" aria-hidden="true"></i>
                                </button>
                                
                                <#if usernameHidden?? && messagesPerField.existsError('username','password')>
                                    <span id="input-error-password" class="error-message" aria-live="polite">
                                        ${kcSanitize(messagesPerField.getFirstError('username','password'))?no_esc}
                                    </span>
                                </#if>
                            </div>

                            <div class="form-options">
                                <div class="remember-me">
                                    <#if realm.rememberMe && !usernameHidden??>
                                        <div class="checkbox">
                                            <label>
                                                <#if login.rememberMe??>
                                                    <input tabindex="5" id="rememberMe" name="rememberMe" type="checkbox" checked>
                                                <#else>
                                                    <input tabindex="5" id="rememberMe" name="rememberMe" type="checkbox">
                                                </#if>
                                                <span class="checkbox-text">${msg("rememberMe")}</span>
                                            </label>
                                        </div>
                                    </#if>
                                </div>
                                <div class="forgot-password">
                                    <#if realm.resetPasswordAllowed>
                                        <a tabindex="6" href="${url.loginResetCredentialsUrl}">${msg("doForgotPassword")}</a>
                                    </#if>
                                </div>
                            </div>

                            <div class="form-buttons">
                                <input type="hidden" id="id-hidden-input" name="credentialId" <#if auth.selectedCredential?has_content>value="${auth.selectedCredential}"</#if>/>
                                <button tabindex="7" class="login-button" name="login" id="kc-login" type="submit">
                                    <span class="button-text">${msg("doLogIn")}</span>
                                    <span class="button-icon"></span>
                                </button>
                            </div>
                            
                            <#if realm.password && realm.registrationAllowed && !registrationDisabled??>
                                <div class="register-link">
                                    <span>${msg("noAccount")} <a tabindex="8" href="${url.registrationUrl}">${msg("doRegister")}</a></span>
                                </div>
                            </#if>
                        </form>
                    </#if>
                </div>
            </div>
            <div class="login-illustration">
                <img src="${url.resourcesPath}/img/Login.jpg" alt="Login illustration" class="responsive-img">
            </div>
        </div>

    </#if>
</@layout.registrationLayout>