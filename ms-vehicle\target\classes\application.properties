spring.application.name=vehicule

# Server port
server.port=9003

# Eureka Client Configuration
eureka.client.service-url.defaultZone=http://localhost:9102/eureka/

# DataSource Configuration
spring.datasource.url=*****************************************
spring.datasource.username=parcauto
spring.datasource.password=parcauto
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=postgresql

# Springdoc OpenAPI Configuration
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true

# OAuth2 Security Configuration for the REST API
spring.security.oauth2.resourceserver.jwt.issuer-uri: http://localhost:8080/realms/parc-auto
spring.security.oauth2.resourceserver.jwt.jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs
jwt.auth.converter.resource-id= ms_vehicule
jwt.auth.converter.principle-attribute= preferred_username

# OAuth2 Parameters Configuration for Swagger UI
springdoc.swagger-ui.oauth.client-id=ms_vehicule
springdoc.swagger-ui.oauth.client-secret=WMNFDttbGHmkiJQsl7VsPzqHmp5G47yn

# NFS File Storage Path Configuration
storage.path=/app/storage

