<div class="dialog-container">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <i class="fas fa-road"></i>
      Attribuer un badge télépéage à l'astreinte
    </h2>
    <button mat-icon-button (click)="onCancel()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div mat-dialog-content class="dialog-content">
    <div class="astreinte-info">
      <h3>Informations de l'astreinte</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Période:</label>
          <span>{{ data.astreinte.dateDebut | date:'dd/MM/yyyy HH:mm' }} - {{ data.astreinte.dateFin | date:'dd/MM/yyyy HH:mm' }}</span>
        </div>
        <div class="info-item">
          <label>Description:</label>
          <span>{{ data.astreinte.description || 'Aucune description' }}</span>
        </div>
        <div class="info-item">
          <label>Consultants:</label>
          <span>{{ data.astreinte.consultants.length }} consultant(s) assigné(s)</span>
        </div>
      </div>
    </div>

    <form [formGroup]="telepeageForm" (ngSubmit)="onSubmit()" class="telepeage-form">
      <div class="form-section">
        <h3>Sélection du badge télépéage</h3>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Badge télépéage disponible</mat-label>
          <mat-select formControlName="badgeId" [disabled]="loading">
            <mat-option *ngFor="let badge of badgesDisponibles" [value]="badge.id">
              <div class="badge-option">
                <div class="badge-main">
                  <strong>{{ badge.numero }}</strong>
                  <span class="badge-details">{{ badge.operateur }}</span>
                </div>
                <div class="badge-meta">
                  <span class="solde-badge" [ngClass]="{'low-balance': badge.solde < 50}">
                    {{ badge.solde }} DT
                  </span>
                </div>
              </div>
            </mat-option>
          </mat-select>
          <mat-error *ngIf="isFieldInvalid('badgeId')">
            {{ getFieldError('badgeId') }}
          </mat-error>
        </mat-form-field>

        <div class="availability-info" *ngIf="badgesDisponibles.length === 0">
          <div class="no-badges">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Aucun badge télépéage disponible</p>
            <small>Veuillez contacter l'administrateur</small>
          </div>
        </div>

        <div class="availability-info" *ngIf="badgesDisponibles.length > 0">
          <div class="badges-count">
            <i class="fas fa-info-circle"></i>
            <span>{{ badgesDisponibles.length }} badge(s) télépéage disponible(s)</span>
          </div>
        </div>
      </div>

      <div class="form-section">
        <h3>Estimation des coûts</h3>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Montant estimé</mat-label>
          <input 
            matInput 
            type="number" 
            formControlName="montantEstime"
            placeholder="0.00"
            min="0"
            step="0.01"
            [disabled]="loading">
          <span matSuffix>DT</span>
          <mat-error *ngIf="isFieldInvalid('montantEstime')">
            {{ getFieldError('montantEstime') }}
          </mat-error>
        </mat-form-field>

        <div class="estimation-help">
          <i class="fas fa-info-circle"></i>
          <span>Estimation basée sur les trajets prévus pour l'astreinte</span>
        </div>
      </div>

      <div class="form-section">
        <h3>Trajets prévus (optionnel)</h3>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Trajets</mat-label>
          <textarea 
            matInput 
            formControlName="trajets" 
            rows="3"
            placeholder="Ex: Tunis - Sfax, Sfax - Sousse..."
            [disabled]="loading">
          </textarea>
          <mat-hint>Séparez les trajets par des virgules</mat-hint>
        </mat-form-field>

        <div class="trajets-communs">
          <h4>Trajets fréquents :</h4>
          <div class="trajets-buttons">
            <button
              *ngFor="let trajet of getTrajetsCommuns()"
              type="button"
              mat-button
              class="trajet-button"
              (click)="ajouterTrajetCommun(trajet)"
              [disabled]="loading">
              {{ trajet }}
            </button>
          </div>
        </div>
      </div>

      <div class="form-section">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Commentaire (optionnel)</mat-label>
          <textarea 
            matInput 
            formControlName="commentaire" 
            rows="3"
            placeholder="Ajoutez un commentaire sur cette attribution..."
            [disabled]="loading">
          </textarea>
        </mat-form-field>
      </div>
    </form>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button 
      mat-button 
      type="button" 
      (click)="onCancel()"
      [disabled]="loading"
      class="cancel-button">
      Annuler
    </button>
    
    <button 
      mat-raised-button 
      color="primary"
      type="submit"
      (click)="onSubmit()"
      [disabled]="telepeageForm.invalid || loading || badgesDisponibles.length === 0"
      class="submit-button">
      <span *ngIf="!loading">
        <i class="fas fa-check"></i>
        Attribuer le badge
      </span>
      <span *ngIf="loading">
        <i class="fas fa-spinner fa-spin"></i>
        Attribution en cours...
      </span>
    </button>
  </div>
</div>
