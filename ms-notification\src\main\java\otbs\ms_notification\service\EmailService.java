package otbs.ms_notification.service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import otbs.ms_notification.dto.events.NotificationEvent;
import otbs.ms_notification.model.Notification;
import otbs.ms_notification.model.NotificationStatus;
import otbs.ms_notification.model.TypeNotification;
import otbs.ms_notification.client.keycloak.KeycloakUserService;
import otbs.ms_notification.client.keycloak.UserDTO;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;


@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;
    private final SpringTemplateEngine templateEngine;
    private final KeycloakUserService keycloakUserService;

    @Value("${notification.email.enabled:true}")
    private boolean emailEnabled;

    @Value("${notification.email.sender:<EMAIL>}")
    private String emailSender;

    @Value("${notification.email.subject-prefix:[Parc Auto] }")
    private String subjectPrefix;

  
    public void sendNotificationEmail(NotificationEvent event, String userId) {
        log.info("=== DÉBUT ENVOI EMAIL ===");
        log.info("Event: {}, UserId: {}, EmailEnabled: {}", event.getEventType(), userId, emailEnabled);
        
        if (!emailEnabled) {
            log.warn("Envoi d'emails désactivé. Email non envoyé à l'utilisateur {}", userId);
            return;
        }
        try {
            log.info("Récupération email pour utilisateur: {}", userId);
            String emailAddress = getUserEmailFromKeycloak(userId);
            log.info("Email récupéré: {}", emailAddress);
            
            if (emailAddress == null) {
                log.error("ÉCHEC: Impossible de récupérer l'email pour l'utilisateur {} depuis Keycloak. Email non envoyé.", userId);
                return;
            }

            String template = determinerTemplate(event.getEventType(), event.getSource());
            String subject = genererSujetContextuel(event);

            Context context = new Context(Locale.getDefault());
            context.setVariable("title", event.getTitre());
            context.setVariable("message", event.getMessage());
            context.setVariable("subject", subject);
            context.setVariable("urlAction", event.getUrlAction());
            context.setVariable("entiteLieeId", event.getEntiteLieeId());
            context.setVariable("priorite", event.getPriorite());
            context.setVariable("eventType", event.getEventType());
            
            if (event.getSource().equals("ms-incident")) {
                context.setVariable("status", event.getEventType().equals("RESOLVED") ? "RESOLVED" : "NEW");
            }

            // Ajouter les métadonnées si disponibles
            if (event.getMetadata() != null && !event.getMetadata().isEmpty()) {
                context.setVariable("metadata", event.getMetadata());
            } else {
                context.setVariable("metadata", new HashMap<String, Object>());
            }

            log.info("Génération du contenu HTML avec template: {}", template);
            String htmlContent = templateEngine.process(template, context);
            log.info("Contenu HTML généré, taille: {} caractères", htmlContent.length());
            
            log.info("Envoi email SMTP vers: {}", emailAddress);
            sendHtmlEmail(emailAddress, subject, htmlContent);
                    
            
        } catch (Exception e) {       
            e.printStackTrace();
        }
        log.info("=== FIN ENVOI EMAIL ===");
    }


    public void sendNotificationEmail(Notification notification, NotificationStatus status, String userId) {
        
        if (!emailEnabled) {
            log.warn("Envoi d'emails désactivé. Email non envoyé à l'utilisateur {}", userId);
            return;
        }

        try {
            // Récupérer l'email de l'utilisateur depuis Keycloak
            log.info("Récupération email pour utilisateur: {}", userId);
            String emailAddress = getUserEmailFromKeycloak(userId);
            log.info("Email récupéré: {}", emailAddress);
            
            if (emailAddress == null) {
                log.error("ÉCHEC: Impossible de récupérer l'email pour l'utilisateur {} depuis Keycloak. Email non envoyé.", userId);
                return;
            }

            String template = determinerTemplate(notification.getType());
            String subject = genererSujetContextuel(notification);

            Context context = new Context(Locale.getDefault());
            context.setVariable("title", notification.getTitre());
            context.setVariable("message", notification.getMessage());
            context.setVariable("subject", subject);
            context.setVariable("urlAction", notification.getUrlAction());
            context.setVariable("entiteLieeId", notification.getEntiteLieeId());
            context.setVariable("priorite", notification.getPriorite().name());
            context.setVariable("eventType", notification.getType().name());
            
            // Pour les incidents, déterminer le statut
            if (notification.getType() == TypeNotification.INCIDENT_RESOLU) {
                context.setVariable("status", "RESOLVED");
            } else if (notification.getType() == TypeNotification.INCIDENT_NOUVEAU 
                    || notification.getType() == TypeNotification.INCIDENT_CRITIQUE) {
                context.setVariable("status", "NEW");
            }
            
            // Ajouter des métadonnées simulées (normalement récupérées de la base de données)
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("reference", notification.getEntiteLieeId());
            
            if (notification.getEntiteLieeType() != null) {
                metadata.put("type", notification.getEntiteLieeType());
            }
            
            context.setVariable("metadata", metadata);

            log.info("Génération du contenu HTML avec template: {}", template);
            String htmlContent = templateEngine.process(template, context);
            log.info("Contenu HTML généré, taille: {} caractères", htmlContent.length());
            
            log.info("Envoi email SMTP vers: {}", emailAddress);
            sendHtmlEmail(emailAddress, subject, htmlContent);
                    
            
        } catch (Exception e) {
                    
            e.printStackTrace();
        }
    }


    private String getUserEmailFromKeycloak(String userId) {
        try {
            String email = keycloakUserService.getEmailFromUserId(userId);
            
            // Vérifier si c'est un vrai email (contient @) et non pas un fallback
            if (email != null && email.contains("@") && !email.equals(userId)) {
                log.debug("Email récupéré pour l'utilisateur {}: {}", userId, email);
                return email.trim();
            } else {
                log.warn("L'utilisateur {} n'a pas d'adresse email valide dans Keycloak (fallback: {})", userId, email);
                return null;
            }
        } catch (Exception e) {
            log.error("Erreur lors de la récupération de l'email pour l'utilisateur {} depuis Keycloak: {}", 
                    userId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Détermine le template à utiliser en fonction du type d'événement et de la source
     */
    private String determinerTemplate(String eventType, String source) {
        if ("ms-incident".equals(source)) {
            return "email/incident-template";
        } else if ("ms-mission".equals(source)) {
            return "email/mission-template";
        } else if ("ms-astreint".equals(source)) {
            return "email/astreinte";
        } else {
            return "email/notification-template";
        }
    }

    /**
     * Détermine le template à utiliser en fonction du type de notification
     */
    private String determinerTemplate(TypeNotification type) {
        if (type == TypeNotification.INCIDENT_NOUVEAU 
                || type == TypeNotification.INCIDENT_RESOLU 
                || type == TypeNotification.INCIDENT_CRITIQUE) {
            return "email/incident-template";
        } else if (type == TypeNotification.MISSION_NOUVELLE 
                || type == TypeNotification.MISSION_MODIFIEE
                || type == TypeNotification.MISSION_ASSIGNEE
                || type == TypeNotification.MISSION_TERMINEE
                || type == TypeNotification.MISSION_ACCOMPAGNATEUR) {
            return "email/mission-template";
        } else if (type == TypeNotification.ASTREINTE_NOUVELLE 
                || type == TypeNotification.ASTREINTE_MODIFIEE
                || type == TypeNotification.ASTREINTE_ANNULEE
                || type == TypeNotification.ASTREINTE_ASSIGNEE) {
            return "email/astreinte";
        } else {
            return "email/notification-template";
        }
    }


    private String genererSujetContextuel(NotificationEvent event) {
        String prefix = subjectPrefix + " ";
        String contexte = "";
        
        // Déterminer le contexte selon la source et le type d'événement
        switch (event.getSource()) {
            case "ms-incident":
                switch (event.getEventType()) {
                    case "CREATED": contexte = "🚨 Nouvel Incident"; break;
                    case "RESOLVED": contexte = "✅ Incident Résolu"; break;
                    default: contexte = "📋 Incident"; break;
                }
                // Ajouter l'ID si disponible
                if (event.getEntiteLieeId() != null) {
                    contexte += " #" + event.getEntiteLieeId();
                }
                break;
                
            case "ms-mission":
                switch (event.getEventType()) {
                    case "MISSION_CREATED": contexte = "📋 Nouvelle Mission"; break;
                    case "MISSION_ASSIGNED": contexte = "🎯 Mission Assignée"; break;
                    case "ACCOMPANIST_ASSIGNED": contexte = "👥 Accompagnateur Assigné"; break;
                    case "MISSION_COMPLETED": contexte = "✅ Mission Terminée"; break;
                    default: contexte = "📋 Mission"; break;
                }
                // Ajouter l'ID si disponible
                if (event.getEntiteLieeId() != null) {
                    contexte += " #" + event.getEntiteLieeId();
                }
                break;
                
            case "ms-astreint":
                switch (event.getEventType()) {
                    case "ASTREINTE_CREATED": contexte = "📅 Nouvelle Astreinte"; break;
                    case "ASTREINTE_UPDATED": contexte = "🔄 Astreinte Modifiée"; break;
                    case "ASTREINTE_DELETED": contexte = "❌ Astreinte Annulée"; break;
                    default: contexte = "📅 Astreinte"; break;
                }
                // Ajouter l'ID si disponible
                if (event.getEntiteLieeId() != null) {
                    contexte += " #" + event.getEntiteLieeId();
                }
                break;
                
            default:
                contexte = event.getTitre();
                break;
        }
        
        return prefix + contexte;
    }
    
    /**
     * Génère un sujet contextuel pour une notification
     */
    private String genererSujetContextuel(Notification notification) {
        String prefix = subjectPrefix + " ";
        String contexte = "";
        
        // Déterminer le contexte selon le type de notification
        switch (notification.getType()) {
            case INCIDENT_NOUVEAU: contexte = "🚨 Nouvel Incident"; break;
            case INCIDENT_RESOLU: contexte = "✅ Incident Résolu"; break;
            case INCIDENT_CRITIQUE: contexte = "🔥 Incident Critique"; break;
            case MISSION_NOUVELLE: contexte = "📋 Nouvelle Mission"; break;
            case MISSION_ASSIGNEE: contexte = "🎯 Mission Assignée"; break;
            case MISSION_ACCOMPAGNATEUR: contexte = "👥 Accompagnateur Assigné"; break;
            case MISSION_TERMINEE: contexte = "✅ Mission Terminée"; break;
            case MISSION_MODIFIEE: contexte = "📝 Mission Modifiée"; break;
            case ASTREINTE_NOUVELLE: contexte = "📅 Nouvelle Astreinte"; break;
            case ASTREINTE_MODIFIEE: contexte = "🔄 Astreinte Modifiée"; break;
            case ASTREINTE_ANNULEE: contexte = "❌ Astreinte Annulée"; break;
            case ASTREINTE_ASSIGNEE: contexte = "📅 Astreinte Assignée"; break;
            default: contexte = notification.getTitre(); break;
        }
        
        // Ajouter l'ID de l'entité si disponible
        if (notification.getEntiteLieeId() != null) {
            contexte += " #" + notification.getEntiteLieeId();
        }
        
        return prefix + contexte;
    }

    private void sendHtmlEmail(String to, String subject, String htmlContent) throws MessagingException {
        log.info(" Configuration SMTP - From: {}, To: {}", emailSender, to);
        
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        
        helper.setFrom(emailSender);
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(htmlContent, true);
        
        mailSender.send(message);
    }
} 