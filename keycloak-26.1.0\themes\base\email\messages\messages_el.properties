requiredAction.terms_and_conditions=Όροι και Συνθήκες
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Δημιουργία Κωδικών Ανάκτησης
requiredAction.VERIFY_EMAIL=Επιβεβαίωση Email
requiredAction.UPDATE_PROFILE=Ενημέρωση Προφίλ
requiredAction.UPDATE_PASSWORD=Ενημέρωση Κωδικού Πρόσβασης
requiredAction.CONFIGURE_TOTP=Ρύθμιση OTP
executeActionsBodyHtml=<p>Ο διαχειριστής σας ζητάει να ενημερώστε το λογαριασμός σας στο {2} με το να κάνετε την/τις ακόλουθες ενέργεια/ες:{3}. Πατήστε το σύνδεσμο παρακάτω για να ξεκινήσετε τη διαδικασία.</p><p><a href="{0}"><PERSON>ύνδεσμος για ενημέρωση λογαριασμού</a></p><p>Αυτός ο σύνδεσμος θα λήξει σε {4}.</p><p>Αν δεν έχετε ενημερωθεί ότι ο διαχειριστής ζήτησε αυτήν την ενημέρωση, τότε απλώς αγνοήστε αυτό το μήνυμα και δεν θα αλλάξει τίποτα.</p>
executeActionsBody=Ο διαχειριστής σας ζητάει να ενημερώστε το λογαριασμός σας στο {2} με το να κάνετε την/τις ακόλουθες ενέργεια/ες:{3}. Πατήστε το σύνδεσμο παρακάτω για να ξεκινήσετε τη διαδικασία.\n\n{0}\n\nΑυτός ο σύνδεσμος θα λήξει σε {4}.\n\nΑν δεν έχετε ενημερωθεί ότι ο διαχειριστής ζήτησε αυτήν την ενημέρωση, τότε απλώς αγνοήστε αυτό το μήνυμα και δεν θα αλλάξει τίποτα.
passwordResetBodyHtml=<p>Κάποιος ζήτησε να αλλάξει τα διαπιστευτήρια του λογαριασμού σας στο {2}. Αν είσαστε εσείς, πατήστε το σύνδεσμο παρακάτω για να τα αλλάξετε.</p><p><a href="{0}">Σύνδεσμος για να αλλάξετε τα διαπιστευτήρια</a></p><p>Αυτός ο σύνδεσμος και κωδικός θα λήξει σε {3}.</p><p>Αν δεν θέλετε να αλλάξετε τα διαπιστευτήρια σας, τότε αγνοήστε αυτό το μήνυμα και δε θα αλλάξει τίποτα.</p>
passwordResetBody=Κάποιος ζήτησε να αλλάξει τα διαπιστευτήρια του λογαριασμού σας στο {2}. Αν είσαστε εσείς, πατήστε το σύνδεσμο παρακάτω για να τα αλλάξετε.\n\n{0}\n\nΑυτός ο σύνδεσμος και κωδικός θα λήξει σε {3}.\n\nΑν δεν θέλετε να αλλάξετε τα διαπιστευτήρια σας, τότε αγνοήστε αυτό το μήνυμα και δε θα αλλάξει τίποτα.
identityProviderLinkBodyHtml=<p>Κάποιος θέλει να συνδέσει το λογαριασμό σας στο <b>{1}</b> με ένα λογαριασμό του χρήστη {2} στο <b>{0}</b>. Αν είσαστε εσείς, πατήστε το σύνδεσμο παρακάτω για να συνδεθούν οι λογαριασμοί </p><p><a href="{3}">Σύνδεσμος για σύνδεση λογαριασμών</a></p><p>Αυτός ο σύνδεσμος θα λήξει σε {5}.</p><p>Αν δεν θέλετε να συνδέσετε το λογαριασμό, τότε αγνοήστε αυτό το μήνυμα. Αν συνδεθούν οι λογαριασμοί τότε θα μπορεί να γίνει είσοδος στο {1} μέσω του {0}.</p>
identityProviderLinkBody=Κάποιος θέλει να συνδέσει το λογαριασμό σας στο "{1}" με ένα λογαριασμό του χρήστη {2} στο "{0}". Αν είσαστε εσείς, πατήστε το σύνδεσμο παρακάτω για να συνδεθούν οι λογαριασμοί\n\n{3}\n\nΑυτός ο σύνδεσμος θα λήξει σε {5}.\n\nΑν δεν θέλετε να συνδέσετε το λογαριασμό, τότε αγνοήστε αυτό το μήνυμα. Αν συνδεθούν οι λογαριασμοί τότε θα μπορεί να γίνει είσοδος στο {1} μέσω του {0}.
emailUpdateConfirmationBodyHtml=<p>Για να ενημερώσετε το λογαριασμό σας {2} με διεύθυνση email {1}, πατήστε το σύνδεσμο παρακάτω </p><p><a href="{0}">{0}</a></p><p>Ο σύνδεσμος θα λήξει σε {3}.</p><p>Αν δεν θέλετε να προχωρήσετε με αυτή την αλλαγή, τότε αγνοήστε αυτό το μήνυμα.</p>
emailUpdateConfirmationBody=Για να ενημερώσετε το λογαριασμό σας {2} με διεύθυνση email {1}, πατήστε το σύνδεσμο παρακάτω\n\n{0}\n\nΟ σύνδεσμος θα λήξει σε {3}.\n\nΑν δεν θέλετε να προχωρήσετε με αυτή την αλλαγή, τότε αγνοήστε αυτό το μήνυμα.
emailVerificationBodyCode=Παρακαλώ επιβεβαιώστε τη διεύθυνση email σας, εισάγοντας το παρακάτω κωδικό.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Παρακαλώ επιβεβαιώστε τη διεύθυνση email σας, εισάγοντας το παρακάτω κωδικό.</p><p><b>{0}</b></p>

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#δευτερόλεπτα|1#δευτερόλεπτο|1<δευτερόλεπτα}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#λεπτά|1#λεπτό|1<λεπτά}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#ώρες|1#ώρα|1<ώρες}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#μέρες|1#μέρα|1<μέρες}

eventUpdateTotpBodyHtml=<p>Η πρόσβαση μέσω OTP ενημερώθηκε στο λογαριασμό σας στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.</p>
eventUpdateTotpBody=Η πρόσβαση μέσω OTP ενημερώθηκε στο λογαριασμό σας στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.
eventUpdateTotpSubject=Ενημέρωση OTP
eventUpdatePasswordBodyHtml=<p>Ο κωδικός πρόσβασής σας άλλαξε στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.</p>
eventUpdatePasswordBody=Ο κωδικός πρόσβασής σας άλλαξε στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.
eventUpdatePasswordSubject=Ενημέρωση Κωδικού Πρόσβασης
eventRemoveTotpBodyHtml=<p>Η πρόσβαση μέσω OTP αφαιρέθηκε από το λογαριασμό σας στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.</p>
eventRemoveTotpBody=Η πρόσβαση μέσω OTP αφαιρέθηκε από το λογαριασμό σας στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.
eventRemoveTotpSubject=Αφαίρεση OTP
eventLoginErrorBodyHtml=<p>Ανιχνεύθηκε μία αποτυχημένη προσπάθεια εισόδου στο λογαριασμό σας στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.</p>
eventLoginErrorBody=Ανιχνεύθηκε μία αποτυχημένη προσπάθεια εισόδου στο λογαριασμό σας στις {0} από το {1}. Αν δεν είσαστε εσείς, τότε παρακαλώ επικοινωνήστε με ένα διαχειριστή.
eventLoginErrorSubject=Σφάλμα Εισόδου
executeActionsSubject=Ενημέρωση Του Λογαριασμού Σας
passwordResetSubject=Αλλαγή κωδικού πρόσβασης
identityProviderLinkSubject=Σύνδεση {0}
emailTestBodyHtml=<p>Αυτό είναι ένα δοκιμαστικό μήνυμα</p>
emailTestBody=Αυτό είναι ένα δοκιμαστικό μήνυμα
emailTestSubject=[KEYCLOAK] - Δοκιμαστικό μήνυμα SMTP
emailUpdateConfirmationSubject=Επιβεβαίωση Nέου email
emailVerificationBodyHtml=<p>Κάποιος δημιούργησε ένα λογαριασμό {2} με αυτή τη διεύθυνση email. Αν είσαστε εσείς, τότε πατήστε το σύνδεσμο παρακάτω για να επιβεβαιώσετε το email σας</p><p><a href="{0}">Σύνδεσμος για επιβεβαίωση διεύθυνσης email</a></p><p>Αυτός ο σύνδεσμος θα λήξει σε {3}.</p><p>Αν δεν δημιουργήσατε το λογαριασμό τότε απλώς αγνοήστε αυτό το μήνυμα.</p>
emailVerificationBody=Κάποιος δημιούργησε ένα {2} λογαριασμό με αυτή τη διεύθυνση email. Αν είσασταν εσείς, τότε πατήστε το παρακάτω σύνδεσμο για να επιβεβαιώσετε τη διεύθυνση email σας\n\n{0}\n\nΑυτός ο σύνδεσμος θα λήξει μέσα σε {3}.\n\nΑν δεν δημιουργήσατε εσείς αυτό το λογαριασμό, τότε απλώς αγνοήστε αυτό το μήνυμα.
emailVerificationSubject=Επιβεβαίωση email
