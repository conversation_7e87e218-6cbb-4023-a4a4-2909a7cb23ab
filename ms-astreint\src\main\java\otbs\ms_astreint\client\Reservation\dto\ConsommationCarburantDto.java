package otbs.ms_astreint.client.reservation.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO pour représenter une consommation de carburant provenant du microservice ms-reservation.
 * Cette classe contient les informations nécessaires pour représenter une consommation de carburant
 * dans le contexte du microservice ms-astreinte.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConsommationCarburantDto {
    private Long id;
    private Date date;
    private Double montantSortie;
    private Double montantConsommer;
    private String filePath;
    private String typeConsommation;
    private Long reservationId;
    private Long carteCarburantId;
    private Long paiementCarburantCashId;
}
