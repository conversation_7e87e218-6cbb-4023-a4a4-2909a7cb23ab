package otbs.ms_astreint.client.reservation.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConsommationCarburantDto {
    private Long id;
    private Date date;
    private Double montantSortie;
    private Double montantConsommer;
    private String filePath;
    private TypeConsommation typeConsommation;
    private Long reservationId;
    private Long carteCarburantId;
    private Long paiementCarburantCashId;
}
