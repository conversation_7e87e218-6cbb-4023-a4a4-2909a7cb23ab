doLogIn=Oturum aç
doRegister=Kayıt ol
doRegisterSecurityKey=Kayıt ol
doCancel=İptal et
doSubmit=Gönder
doBack=Geri
doYes=Evet
doNo=Hayır
doContinue=Devam et
doIgnore=Yoksay
doAccept=Kabul Et
doDecline=Reddet
doForgotPassword=Parolanızı mı unuttunuz?
doClickHere=Buraya Tıklayın
doImpersonate=Kişiselleştir
doTryAgain=Tekrar deneyin
doTryAnotherWay=Başka bir yol deneyin
doConfirmDelete=Silme işlemini onayla
errorDeletingAccount=Hesabı silerken bir hata oluştu
deletingAccountForbidden=Hesabınızı silmek için yeterli yetkiye sahip değilsiniz, admine danışın.
kerberosNotConfigured=Kerberos Tanımlanmamış
kerberosNotConfiguredTitle=Kerberos Tanımlanmamış
bypassKerberosDetail=Ya Kerberos ile giriş yapmadınız veya tarayıcınız Kerberos giriş için ayarlanmamış. Diğer yollarla giriş yapmak için lütfen devam''a tıklayın
kerberosNotSetUp=Kerberos kurulmadı. Giriş yapamazsın.
registerTitle=Kayıt ol
loginAccountTitle=Hesabınıza giriş yapın
loginTitle={0} adresinde oturum açın
loginTitleHtml={0}
impersonateTitle={0} Kullanıcı kimliğine bürün
impersonateTitleHtml=<strong>{0}</strong> Kullanıcı kimliğine bürün
realmChoice=Realm
unknownUser=Bilinmeyen kullanıcı
loginTotpTitle=Mobil Kimlik Doğrulama Kurulumu
loginProfileTitle=Hesap bilgilerini Güncelle
loginIdpReviewProfileTitle=Hesap Bilgilerini Güncelle
loginTimeout=Giriş yapmak  çok uzun sürdü. Giriş süreci baştan başlayacak.
reauthenticate=Devam etmek için lütfen yeniden kimlik doğrulama yapınız
authenticateStrong=Devam etmek için güçlü kimlik doğrulaması gerekiyor
oauthGrantTitle={0} adresine Erişim Ver
oauthGrantTitleHtml={0}
oauthGrantInformation={0} ın verilerinizi nasıl işleyeceğini öğrenerek {0} a güvendiğinizden emin olun.
oauthGrantReview=Şunu inceleyebilirsiniz:
oauthGrantTos=kullanım şartları.
oauthGrantPolicy=gizlilik sözleşmesi.
errorTitle=Üzgünüz...
errorTitleHtml=<strong>Üzgünüz ...</strong>
emailVerifyTitle=Eposta Doğrulama
emailForgotTitle=Parolanızı mı unuttunuz?
updateEmailTitle=E-posta güncelleme
emailUpdateConfirmationSentTitle= Doğrulama e-postası gönderildi
emailUpdateConfirmationSent=Doğrulama e-postası {0} kişisine gönderildi. E-posta güncellemesini tamamlamak için önceki talimatları izlemelisiniz.
emailUpdatedTitle=E-posta güncellendi
emailUpdated=Hesap e-postası başarıyla {0} olarak güncellendi.?
updatePasswordTitle=Şifre güncelle
codeSuccessTitle=Başarılı işlem kodu
codeErrorTitle=Hatalı işlem kodu\: {0}
displayUnsupported=İstenen gösterim türü desteklenmiyor
browserRequired=Giriş için taryıcı gerekli
browserContinue=Girişe devam etmek için tarayıcı gerekli
browserContinuePrompt=Tarayıcı aç ve girişe devam et? [e/h]:
browserContinueAnswer=e

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Internal
unknown=Bilinmiyor

termsTitle=Şartlar ve koşullar
termsText=<p>Tanımlanacak şartlar ve koşullar</p>
termsPlainText=Tanımlanacak şartlar ve koşullar.
termsAcceptanceRequired=Şartlarımızı ve koşullarımızı kabul etmelisiniz.
acceptTerms=Hükümlere ve koşullara katılıyorum

deleteCredentialTitle=Sil {0}
deleteCredentialMessage={0} silinecek. Devam etmek istediğinize emin misiniz?

recaptchaFailed=Geçersiz Recaptcha
recaptchaNotConfigured=Recaptcha gerekli, ancak yapılandırılmamış
consentDenied=Onay reddedildi.

noAccount=Yeni kullanıcı?
username=Kullanıcı Adı
usernameOrEmail=Kullanıcı adı veya E-Posta
firstName=Ad
givenName=Ad
fullName=Ad Soyad
lastName=Soyad
familyName=Soyad
email=E-Posta
password=Şifre
passwordConfirm=Şifre Doğrulama
passwordNew=Yeni Şifre
passwordNewConfirm=Yeni Şifre Doğrulama
hidePassword=Şifreyi gizle
showPassword=Şifreyi göster
rememberMe=Beni Hatırla
authenticatorCode=Kimlik Doğrulama Kodu
address=Adres
street=Cadde
locality=Semt
region=Bölge
postal_code=Posta Kodu
country=Ülke
emailVerified=E-Posta Doğrulandı
website=Web sitesi
phoneNumber=Telefon nuamrası
phoneNumberVerified=Telefon numarası doğrulandı
gender=Cinsiyet
birthday=Doğum tarihi
zoneinfo=Zaman dilimi
gssDelegationCredential=GSS Yetki Bilgisi
logoutOtherSessions=Diğer cihazlardan çıkış yap

profileScopeConsentText=Kullanıcı profili
emailScopeConsentText=E-Posta Adresi
addressScopeConsentText=Adres
phoneScopeConsentText=Telefon Numarası
offlineAccessScopeConsentText=Çevrimdışı Erişim
samlRoleListScopeConsentText=Rollerim
rolesScopeConsentText=Kullanıcı rolleri
organizationScopeConsentText=Organizasyon

restartLoginTooltip=Giriş işlemini yeniden başlat

loginTotpIntro=Bu hesaba erişmek için bir Tek Kullanımlık Şifre oluşturmalısınız.
loginTotpStep1=Cep telefonunuzda aşağıdaki uygulamalardan birini yükleyin
loginTotpStep2=Uygulamayı açın ve barkodu tarayın
loginTotpStep3=Uygulama tarafından sağlanan tek seferlik kodu girin ve kurulumu tamamlamak için Gönder''i tıklayın.
loginTotpStep3DeviceName=OTP cihazlarınızı yönetmenize yardımcı olacak bir Cihaz Adı ​​sağlayın.
loginTotpManualStep2=Uygulamayı açın ve anahtarı girin
loginTotpManualStep3=Uygulama bunları ayarlamaya izin veriyorsa aşağıdaki yapılandırma değerlerini kullanın.
loginTotpUnableToScan=Taranamıyor?
loginTotpScanBarcode=Barkod tara?
loginCredential=Kimlik
loginOtpOneTime=Tek seferlik kod
loginTotpType=Tip
loginTotpAlgorithm=Algoritma
loginTotpDigits=Basamak
loginTotpInterval=Aralık
loginTotpCounter=Sayaç
loginTotpDeviceName=Cihaz Adı

loginTotp.totp=Zaman Tabanlı
loginTotp.hotp=Sayaç Tabanlı

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=Giriş yöntemini seçiniz

oauthGrantRequest=Bu erişim ayrıcalıkları veriyor musunuz?
inResource=içinde

oauth2DeviceVerificationTitle=Cihaz girişi
verifyOAuth2DeviceUserCode=Cihazınız tarafından sağlanan kodu girinizz ve Kaydet''e tıklayınız
oauth2DeviceInvalidUserCodeMessage=Geçersiz kod, lütfen tekrar deneyiniz.
oauth2DeviceExpiredUserCodeMessage=Kod zaman aşımına uğradı. Lütfen cihazınıza gidiniz ve tekrardan bağlanmayı deneyin.
oauth2DeviceVerificationCompleteHeader=Cihaz Girişi Başarılı
oauth2DeviceVerificationCompleteMessage=Bu tarayıcı penceresini kapatıp cihazınıza geri dönebilirsiniz.
oauth2DeviceVerificationFailedHeader=Cihaz Girişi Başarısız
oauth2DeviceVerificationFailedMessage=Bu tarayıcı penceresini kapatabilir ve cihazınıza geri dönüp yeniden bağlanmayı deneyebilirsiniz.
oauth2DeviceConsentDeniedMessage=Cihazı bağlamak için izin reddedildi.
oauth2DeviceAuthorizationGrantDisabledMessage=Müşterinin OAuth 2.0 Cihaz Yetkilendirme İzni başlatmasına izin verilmiyor. Bu akış müşteri için devre dışı bırakılmıştır.

emailVerifyInstruction1=E-posta adresinizi doğrulamak için talimatları içeren bir e-posta size gönderildi.
emailVerifyInstruction2=E-postanızda doğrulama kodu almadınız mı?
emailVerifyInstruction3=e-postayı yeniden göndermek için.

emailLinkIdpTitle=Bağlantı {0}
emailLinkIdp1={1} hesabını {2} hesabınıza bağlayan talimatları içeren bir e-posta size gönderildi.
emailLinkIdp2=E-postanızda doğrulama kodu almadınız mı?
emailLinkIdp3=e-postayı yeniden göndermek için.
emailLinkIdp4=E-postayı farklı tarayıcıda zaten doğruladıysanız
emailLinkIdp5=devam etmek.

backToLogin=&laquo; Giriş''e geri dön

emailInstruction=Kullanıcı adınızı veya e-posta adresinizi girin ve yeni bir şifre oluşturmaya ilişkin talimatları size göndereceğiz.
emailInstructionUsername=Kullanıcı adınızı girin, size yeni şifreyi nasıl oluşturacağınızla ilgili talimatları göndereceğiz.

copyCodeInstruction=Lütfen bu kodu kopyalayın ve uygulamanıza yapıştırın:

pageExpiredTitle=Sayfanın Süresi Doldu
pageExpiredMsg1=Giriş işlemini yeniden başlatmak için
pageExpiredMsg2=Giriş işlemine devam etmek için

personalInfo=Kişisel bilgi:
role_admin=Admin
role_realm-admin=Realm Admin
role_create-realm=Realm Oluştur
role_create-client=Client oluştur
role_view-realm=Realm görüntüle
role_view-users=Kullanıcı görüntüle
role_view-applications=Uygulamarı gör
role_view-clients=Clientları görüntüle
role_view-events=Eventleri görüntüle
role_view-identity-providers=Kimlik sağlayıcılarını görüntüle
role_manage-realm=Realm Yönet
role_manage-users=Kullanıcıları Yönet
role_manage-applications=Uygulamaları Yönet
role_manage-identity-providers=Kimlik Sağlayıcılarını Yönet
role_manage-clients=Clientları Yönet
role_manage-events=Eventleri Yönet
role_view-profile=Profil görüntüle
role_manage-account=Hesap Yönet
role_manage-account-links=Hesap bağlantılarını yönet
role_read-token=Token oku
role_offline-access=Çevrimdışı erişim
client_account=Hesap
client_account-console=Hesap Konsolu
client_security-admin-console=Güvenlik Yönetici Konsolu
client_admin-cli=Admin CLI
client_realm-management=Realm Yönet
client_broker=Broker

requiredFields=Gerekli alanlar

invalidUserMessage=Geçersiz kullanıcı adı veya şifre.
invalidUsernameMessage=Geçersiz kullanıcı adı.
invalidUsernameOrEmailMessage=Geçersiz kullanıcı adı veya e-posta.
invalidPasswordMessage=Geçersiz şifre.
invalidEmailMessage=Geçersiz e-posta adresi.
accountDisabledMessage=Hesap devre dışı, yönetici ile iletişime geçin.
accountTemporarilyDisabledMessage=Geçersiz kullanıcı adı veya şifre.
accountPermanentlyDisabledMessage=Geçersiz kullanıcı adı veya şifre.
accountTemporarilyDisabledMessageTotp=Geçersiz kimlik doğrulama kodu.
accountPermanentlyDisabledMessageTotp=Geçersiz kimlik doğrulama kodu.
expiredCodeMessage=Oturum zaman aşımına uğradı. Lütfen tekrar giriş yapın.
expiredActionMessage=Eylem süresi doldu. Lütfen şimdi giriş yapmaya devam edin.
expiredActionTokenNoSessionMessage=Eylemin süresi doldu.
expiredActionTokenSessionExistsMessage=Eylem süresi doldu. Lütfen tekrar başlayın.
sessionLimitExceeded=Çok fazla oturum mevcut
identityProviderLogoutFailure=SAML IdP Çıkış Başarısız

missingFirstNameMessage=Lütfen ilk adı belirtin.
missingLastNameMessage=Lütfen soyadı belirtin.
missingEmailMessage=Lütfen e-posta belirtin.
missingUsernameMessage=Lütfen kullanıcı adını belirtin.
missingPasswordMessage=Lütfen şifre belirtin.
missingTotpMessage=Lütfen kimlik doğrulama kodunu belirtin.
missingTotpDeviceNameMessage=Lütfen cihaz adını belirtin.
notMatchPasswordMessage=Şifreler eşleşmiyor.

error-invalid-value=Geçersiz değer.
error-invalid-blank=Lütfen bir değer sağlayın.
error-empty=Lütfen bir değer sağlayın.
error-invalid-length=Uzunluk {1} ile {2} arasında olmalıdır.
error-invalid-length-too-short=Minimum uzunluk {1}.
error-invalid-length-too-long=Maksimum uzunluk {2}.
error-invalid-email=Geçersiz e-posta adresi.
error-invalid-number=Geçersiz numara.
error-number-out-of-range=Numara {1} ile {2} arasında olmalıdır.
error-number-out-of-range-too-small=Numara en az {1} değerinde olmalıdır.
error-number-out-of-range-too-big=Numara en fazla {2} değerinde olmalıdır.
error-pattern-no-match=Geçersiz değer.
error-invalid-uri=Geçersiz URL.
error-invalid-uri-scheme=Geçersiz URL şeması.
error-invalid-uri-fragment=Geçersiz URL parçası.
error-user-attribute-required=Bu alanı belirtiniz.
error-invalid-date=Geçersiz tarih.
error-user-attribute-read-only=Bu alan sadece okunabilir.
error-username-invalid-character=Değer geçersiz karakter içeriyor.
error-person-name-invalid-character=Değer geçersiz karakter içeriyor.
error-reset-otp-missing-id=Lütfen bir OTP yapılandırması seçin.

invalidPasswordExistingMessage=Mevcut şifre geçersiz.
invalidPasswordBlacklistedMessage=Geçersiz şifre: şifre kara listeye alındı.
invalidPasswordConfirmMessage=Şifre onayı eşleşmiyor.
invalidTotpMessage=Geçersiz kimlik doğrulama kodu.

usernameExistsMessage=Kullanıcı adı zaten var.
emailExistsMessage=Bu e-posta zaten var.

federatedIdentityExistsMessage={0} {1} kullanıcı zaten var. Hesabı bağlamak için lütfen hesap yönetimine giriş yapın.
error-invalid-length=Uzunluk {1} ile {2} arasında olmalıdır.
error-invalid-length-too-short=Minimum uzunluk {1}.
error-invalid-length-too-long=Maksimum uzunluk {2}.
error-invalid-email=Geçersiz e-posta adresi.
error-invalid-number=Geçersiz numara.
error-number-out-of-range=Numara {1} ile {2} arasında olmalıdır.
error-number-out-of-range-too-small=Numara en az {1} değerinde olmalıdır.
error-number-out-of-range-too-big=Numara en fazla {2} değerinde olmalıdır.
error-pattern-no-match=Geçersiz değer.
error-invalid-uri=Geçersiz URL.
error-invalid-uri-scheme=Geçersiz URL şeması.
error-invalid-uri-fragment=Geçersiz URL parçası.
error-user-attribute-required=Bu alanı belirtiniz.
error-invalid-date=Geçersiz tarih.
error-user-attribute-read-only=Bu alan sadece okunabilir.
error-username-invalid-character=Değer geçersiz karakter içeriyor.
error-person-name-invalid-character=Değer geçersiz karakter içeriyor.
error-reset-otp-missing-id=Lütfen bir OTP yapılandırması seçin.
confirmLinkIdpTitle=Bu Hesap Zaten Mevcut
confirmOverrideIdpTitle=Aracı bağlantısı zaten mevcut
federatedIdentityConfirmLinkMessage={0} {1} kullanıcı zaten var. Nasıl devam etmek istersin?
federatedIdentityConfirmOverrideMessage={0} hesabınızı {1} {2} hesabı ile bağlamaya çalışıyorsunuz. Ancak hesabınız farklı {3} {4} hesabı ile zaten bağlı. Mevcut bağlantıyı yeni hesapla değiştirmek istediğinizi onaylayabilir misiniz?
federatedIdentityConfirmReauthenticateMessage=Hesabınızı {0} ile bağlamak için kimlik doğrulayın
nestedFirstBrokerFlowMessage={0} kullanıcısı {1} bilinen herhangi bir kullanıcıya bağlı değil.
confirmLinkIdpReviewProfile=Profili gözden geçir
confirmLinkIdpContinue=Mevcut hesaba ekle
confirmOverrideIdpContinue=Evet, mevcut hesapla olan bağlantıyı geçersiz kıl

configureTotpMessage=Hesabınızı etkinleştirmek için Mobil Kimlik Doğrulama''yı ayarlamanız gerekiyor.
configureBackupCodesMessage=Hesabınızı etkinleştirmek için Yedek Kodları ayarlamanız gerekir.
updateProfileMessage=Hesabınızı etkinleştirmek için kullanıcı profilinizi güncellemeniz gerekiyor.
updatePasswordMessage=Hesabınızı etkinleştirmek için şifrenizi değiştirmeniz gerekiyor.
updateEmailMessage=Hesabınızı aktif hale getirmek için e-posta adresinizi güncellemeniz gerekmektedir.
resetPasswordMessage=Şifreni değiştirmelisin.
verifyEmailMessage=Hesabınızı etkinleştirmek için e-posta adresinizi doğrulamanız gerekiyor.
linkIdpMessage=Hesabınızı {0} ile bağlamak için e-posta adresinizi doğrulamanız gerekiyor.

emailSentMessage=Daha fazla talimatla kısa sürede bir e-posta almalısınız.
emailSendErrorMessage=E-posta gönderilemedi, lütfen daha sonra tekrar deneyin.

accountUpdatedMessage=Hesabın güncellendi.
accountPasswordUpdatedMessage=Şifreniz güncellenmiştir.

delegationCompleteHeader=Giriş başarılı
delegationCompleteMessage=Bu tarayıcı penceresini kapatabilir ve konsol uygulamanıza geri dönebilirsiniz.
delegationFailedHeader=Giriş başarısız
delegationFailedMessage=Bu tarayıcı penceresini kapatabilir ve konsol uygulamanıza geri dönüp tekrar giriş yapmayı deneyebilirsiniz..

noAccessMessage=Erişim yok

invalidPasswordMinLengthMessage=Geçersiz Şifre: En az {0} karakter uzunluğunda olmalı.
invalidPasswordMaxLengthMessage=Geçersiz Şifre: En fazla {0} karakter uzunluğunda olabilir.
invalidPasswordMinDigitsMessage=Geçersiz Şifre: En az {0} sayı(lar) içermelidir.
invalidPasswordMinLowerCaseCharsMessage=Geçersiz Şifre \: En az {0} küçük harf içermelidir.
invalidPasswordMinUpperCaseCharsMessage=Geçersiz Şifre: En az {0} büyük harf içermelidir.
invalidPasswordMinSpecialCharsMessage=Geçersiz Şifre: En az {0} özel karakter içermelidir.
invalidPasswordNotUsernameMessage=Geçersiz Şifre: Kullanıcı adıyla aynı olamaz.
invalidPasswordNotContainsUsernameMessage=Geçersiz Şifre: Şifreniz kullanıcı adınızı içeremez.
invalidPasswordNotEmailMessage=Geçersiz Şifre: Şifreniz e-posta adresinizle aynı olamaz.
invalidPasswordRegexPatternMessage=Geçersiz Şifre: Regex Patternine uygun değil.
invalidPasswordHistoryMessage=Geçersiz Şifre: Son {0} şifreden biri olamaz.
invalidPasswordGenericMessage=Geçersiz Şifre: Yeni şifre şifre politikalarıyla eşleşmiyor.

failedToProcessResponseMessage=Yanıt işlenemedi
httpsRequiredMessage=HTTPS zorunlu
realmNotEnabledMessage=Realm aktif değil
invalidRequestMessage=Geçersiz İstek
successLogout=Çıkış yaptınız
failedLogout=Çıkış başarısız
unknownLoginRequesterMessage=Bilinmeyen giriş isteği
loginRequesterNotEnabledMessage=Giriş istemi etkin değil
bearerOnlyMessage=Yalnızca taşıyıcı uygulamaları tarayıcı girişini başlatmaya izinli değil
standardFlowDisabledMessage=İstemcinin verilen yanıt türüyle tarayıcıda oturum açmasına izin verilmiyor. İstemci için standart akış devre dışı bırakıldı.
implicitFlowDisabledMessage=İstemcinin verilen yanıt türüyle tarayıcıda oturum açmasına izin verilmiyor. İstemci için örtülü akış devre dışı bırakıldı.
invalidRedirectUriMessage=Geçersiz yönlendirme url''i
unsupportedNameIdFormatMessage=Desteklenmeyen NameIDFormat
invalidRequesterMessage=Geçersiz istek
registrationNotAllowedMessage=Kayıt yapılamaz
resetCredentialNotAllowedMessage=Sıfırlamasına izin verilmiyor

permissionNotApprovedMessage=İzin onaylanmadı.
noRelayStateInResponseMessage=Kimlik sağlayıcıdan yanıt olarak geçiş durumu yok.
insufficientPermissionMessage=Kimliklerin bağlanması için yetersiz izinler.
couldNotProceedWithAuthenticationRequestMessage=Kimlik sağlayıcıya kimlik doğrulama isteği ile devam edilemedi.
couldNotObtainTokenMessage=Kimlik sağlayıcıdan token alınamadı.
unexpectedErrorRetrievingTokenMessage=Kimlik sağlayıcıdan token alırken beklenmeyen bir hata oluştu.
unexpectedErrorHandlingResponseMessage=Kimlik sağlayıcıdan yanıt alınırken beklenmeyen bir hata oluştu.
identityProviderAuthenticationFailedMessage=Kimlik doğrulama başarısız oldu. Kimlik sağlayıcıyla kimlik doğrulaması yapılamadı.
couldNotSendAuthenticationRequestMessage=Kimlik sağlayıcıya kimlik doğrulama isteği gönderilemedi.
unexpectedErrorHandlingRequestMessage=Kimlik sağlayıcıya kimlik doğrulama isteği işlenirken beklenmeyen bir hata oluştu.
invalidAccessCodeMessage=Geçersiz giriş kodu.
sessionNotActiveMessage=Oturum etkin değil.
invalidCodeMessage=Bir hata oluştu, lütfen başvurunuz aracılığıyla tekrar giriş yapın.
cookieNotFoundMessage=Çerezler bulunamadı. Lütfen tarayıcınızdan çerezlere izin verdiğinizden emin olun.
insufficientLevelOfAuthentication=İstenilen kimlik doğrulama düzeyi karşılanmadı.
identityProviderUnexpectedErrorMessage=Kimlik sağlayıcıyla kimlik doğrulaması yapılırken beklenmeyen bir hata oluştu
identityProviderMissingStateMessage=Kimlik sağlayıcısından gelen yanıtta eksik durum parametresi.
identityProviderMissingCodeOrErrorMessage=Kimlik sağlayıcısından gelen yanıtta eksik kod veya hata parametresi.
identityProviderInvalidResponseMessage=Kimlik sağlayıcısından gelen geçersiz yanıt.
identityProviderInvalidSignatureMessage=Kimlik sağlayıcısından gelen yanıtta geçersiz imza.
identityProviderNotFoundMessage=Tanımlayıcı ile kimlik sağlayıcı bulunamadı.
identityProviderLinkSuccess=E-postanızı başarıyla doğruladınız. Lütfen orijinal tarayıcınıza geri dönün ve giriş yapın.
staleCodeMessage=Bu sayfa artık geçerli değil, lütfen uygulamanıza geri dönün ve tekrar giriş yapın
realmSupportsNoCredentialsMessage=Realm herhangi bir kimlik bilgisi türünü desteklemiyor.
credentialSetupRequired=Giriş yapılamıyor, kimlik bilgisi kurulumu gerekli.
identityProviderNotUniqueMessage=Realm çoklu kimlik sağlayıcılarını destekler. Kimlik doğrulamak için hangi kimlik sağlayıcısının kullanılması gerektiğini belirleyemedi.
emailVerifiedMessage=E-posta adresiniz doğrulandı.
emailVerifiedAlreadyMessage=E-posta adresiniz zaten doğrulandı.
staleEmailVerificationLink=Tıkladığınız bağlantı eski bir bağlantıdır ve artık geçerli değil. Belki de e-postanızı zaten doğruladınız.
identityProviderAlreadyLinkedMessage={0} tarafından sağlanan Federe kimlik başka bir kullanıcıya bağlı.
confirmAccountLinking={1} kimlik sağlayıcısının hesabını {0} hesabınızla ilişkilendirmeyi onaylayın.
confirmEmailAddressVerification={0} e-posta adresinin geçerliliğini onaylayın.
confirmExecutionOfActions=Aşağıdaki eylemleri gerçekleştirin

backToApplication=&laquo; Uygulamaya Dön
missingParameterMessage=Eksik parametreler\: {0}
clientNotFoundMessage=İstemci bulunamadı.
clientDisabledMessage=İstemci devre dışı.
invalidParameterMessage=Geçersiz paremetre\: {0}
alreadyLoggedIn=Zaten giriş yaptınız.
differentUserAuthenticated=Bu oturumda zaten farklı kullanıcı '' {0} '' olarak doğrulanmışsınız. Lütfen önce çıkış yapınız.
brokerLinkingSessionExpired=İstenen broker hesabı bağlanıyor, ancak mevcut oturum artık geçerli değil.
proceedWithAction==&raquo; Devam etmek için buraya tıklayınız
acrNotFulfilled=Doğrulama gereksinizmleri karşılanamadı

requiredAction.CONFIGURE_TOTP=OTP Ayarla
requiredAction.TERMS_AND_CONDITIONS=Şartlar ve Koşullar
requiredAction.UPDATE_PASSWORD=Şifre güncelle
requiredAction.UPDATE_PROFILE=Profili Güncelle
requiredAction.VERIFY_EMAIL=E-Posta''yı doğrula
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Kurtarma Kodu Oluştur
requiredAction.webauthn-register-passwordless=Webauthn Şifresiz Giriş

invalidTokenRequiredActions=Bağlantıda yer alan gerekli işlemler geçerli değil

doX509Login=Olarak giriş yapacaksınız\:
clientCertificate=X509 istemci sertifikası\:
noCertificate=[Sertifika Yok]

pageNotFound=Sayfa Bulunamadı
internalServerError=Bir iç sunucu hatası oluştu

console-username=Kullanıcı adı:
console-password=Parola:
console-otp=Tek seferlik şifre:
console-new-password=Yeni şifre:
console-confirm-password=Şifreyi Onayla:
console-update-password=Şifrenizin güncellenmesi gerekiyor.
console-verify-email=E-posta adresinizi doğrulamanız gerekiyor. Bir doğrulama kodu içeren {0} adresine bir e-posta gönderildi. Lütfen bu kodu aşağıdaki girdiye giriniz.
console-email-code=E-posta Kodu:
console-accept-terms=Şartları kabul et? [e/h]:
console-accept=e

readOnlyUsernameMessage=Yazma korumalı olduğundan kullanıcı adınızı değiştiremezsiniz.

# Openshift messages
openshift.scope.user_info=Kullanıcı bilgileri
openshift.scope.user_check-access=Kullanıcı erişim bilgileri
openshift.scope.user_full=Tam erişim
openshift.scope.list-projects=Projeleri listele

# SAML authentication
saml.post-form.title=Kimlik Doğrulama Yönlendirmesi
saml.post-form.message=Yönlendiriliyor, lütfen bekleyiniz.
saml.post-form.js-disabled=JavaScript aktif değil. Aktifleştirmenizi öneririz. Devam etmek için aşağıdaki butona tıklayınız. 
saml.artifactResolutionServiceInvalidResponse=Artifact çözülemiyor.

#authenticators
otp-display-name=Kimlik Doğrulama Uygulaması
otp-help-text=Kimlik doğrulama uygulamasından bir doğrulama kodu girin.
otp-reset-description=Hangi OTP yapılandırması kaldırılmalı?
password-display-name=Şifre
password-help-text=Şifrenizi girerek oturum açın.
auth-username-form-display-name=Kullanıcı Adı
auth-username-form-help-text=Kullanıcı adınızı girerek oturum açmaya başlayın
auth-username-password-form-display-name=Kullanıcı adı ve şifre
auth-username-password-form-help-text=Kullanıcı adınızı ve şifrenizi girerek oturum açın.
auth-x509-client-username-form-display-name=X509 Sertifikası
auth-x509-client-username-form-help-text=X509 istemci sertifikası ile oturum açın.
auth-recovery-authn-code-form-help-text= Önceden oluşturulmuş listeden seçtiğiniz doğrulama kodunu giriniz.,
auth-recovery-code-info-message= Belirlenmiş kurtarma kodunu giriniz.,
auth-recovery-code-prompt= Kurtarma kodu {0},
auth-recovery-code-header= Kurtarma kimlik doğrulama kodu ile giriş yapın,
recovery-codes-error-invalid= Geçersiz kurtarma kimlik doğrulama kodu,
recovery-code-config-header= Kurtarma kimlik doğrulama kodları,
recovery-code-config-warning-title= Bu sayfadan ayrıldıktan sonra kurtarma kodlarını tekrardan görüntüleyemeyeceksiniz.,
recovery-code-config-warning-message= Şifre yöneticinizi yazdırdığınızdan, indirdiğinizden veya kopyaladığınızdan emin olun. Kurulumu iptal etmek kurtarma kodlarını hesabınızdan kaldıracaktır.,
recovery-codes-print= Yazdır,
recovery-codes-download= İndir,
recovery-codes-copy= Kopyala,
recovery-codes-copied= Kopyalandı,
recovery-codes-confirmation-message= Kodları güvenli bir yere kaydettim,
recovery-codes-action-complete= Kurulumu tamamla,
recovery-codes-action-cancel= Kurulumu iptal et,
recovery-codes-download-file-header= Kurtarma kodlarını güvenli bir yerde tutunuz.,
recovery-codes-download-file-description= Kurtarma kodları doğrulayıcınıza erişiminiz olmadığı zaman giriş yapabilmenizi sağlayan tek kullanımlık geçiş anahtarlarıdır.,
recovery-codes-download-file-date= Bu kodlar şurada oluşturuldu; ,
recovery-codes-label-default= Kurtarma kodları,
webauthn-display-name= Geçiş Anahtarı,
webauthn-help-text= Giriş yapmak için geçiş anahtarınızı kullanın,
webauthn-passwordless-display-name= Geçiş Anahtarı,
webauthn-passwordless-help-text= Şifresiz giriş için Geçiş Anahtarınızı kullanın.,
webauthn-login-title= Geçiş Anahtarıyla Giriş,
webauthn-registration-title= Geçiş Anahtarıyla Kayıt,
webauthn-available-authenticators= Geçerli Geçiş Anahtarları ,
webauthn-unsupported-browser-text= WebAuthn tarayıcınız tarafından desteklenmemektedir. Başka bir tarayıcıdan ulaşmayı deneyin veya yöneticinizle iletişime geçin.,
webauthn-doAuthenticate= Geçiş Anahtarı ile giriş yap,
webauthn-createdAt-label= Oluşturuldu,
webauthn-error-title= Geçiş Anahtarı hatası,
webauthn-error-registration= Geçiş Anahatarınızı kaydederken hata oluştu.<br/> {0},
webauthn-error-api-get= Geçiş Anahtarınızı doğrularken hata oluştu.<br/> {0},
webauthn-error-different-user= İlk doprulanan kullanıcı ile Geçiş Anahtarı işe doğrulanan kullanıcı aynı değil..,
webauthn-error-auth-verification= Geçiş Anahtarı doğrulaması geçersiz.<br/> {0},
webauthn-error-register-verification= Geçiş Anahtarı doğrulaması geçersiz.<br/> {0},
webauthn-error-user-not-found= Bilinmeyen kullanıcı Geçiş Anahtarı ile doğrulandı.,
identity-provider-redirector= Başka bir Kimlik Sağlayıcı ile iletişime geçiniz.,
identity-provider-login-label= Veya giriş yap,
idp-email-verification-display-name= E-posta doğrulama,
idp-email-verification-help-text= E-postanızı doğrulayarak hesabınızı bağlayın.,
idp-username-password-form-display-name= Kullanıcı adı ve şifte,
idp-username-password-form-help-text= Giriş yaparak hesabınızı bağlayın.,
finalDeletionConfirmation= Hesabınızı silerseniz geri kurtarılamaz. Hesabınızı korumak için İptal''e tıklayın.,
irreversibleAction= Bu işlem geri alınamaz,
deleteAccountConfirm= Hesap silme onayı,
deletingImplies= Hesabınızı silmek şunu ifade eder=,
erasingData= Tüm verilerinizi silmek,
loggingOutImmediately= Hemen oturumunuzu kapatmak,
accountUnusable= Bu hesap ile uygulamanın sonraki kullanımları mümkün olmayacaktır,
userDeletedSuccessfully= Kullanıcı başarıyla silindi,
access-denied= Erişim reddedildi,
access-denied-when-idp-auth= {0} ile kimlik doğrulama yaparken erişim reddedildi,
frontchannel-logout.title= Oturum kapatılıyor,
frontchannel-logout.message= Aşağıdaki uygulamalardan oturumunuzu kapatıyorsunuz,
logoutConfirmTitle= Oturum kapatılıyor,
logoutConfirmHeader= Oturumunuzu kapatmak istiyor musunuz?,
doLogout= Oturumu kapat,
readOnlyUsernameMessage= Kullanıcı adınızı güncelleyemezsiniz çünkü sadece okunabilir durumda.,
error-invalid-multivalued-size= {0} özniteliği en az {1} ve en fazla {2} değer(ler)e sahip olmalıdır.,
shouldBeEqual= {0} {1} ile eşit olmalıdır,
shouldBeDifferent= {0} {1} ile farklı olmalıdır,
shouldMatchPattern= Desen şu şekilde olmalıdır= `/{0}/`,
mustBeAnInteger= Tam sayı olmalıdır,
notAValidOption= Geçerli bir seçenek değil,
selectAnOption= Bir seçenek seçin,
remove= Kaldır,
addValue= Değer ekle,
languages= Diller