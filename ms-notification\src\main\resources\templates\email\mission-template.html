<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${subject}">Notification Mission Parc Auto</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333333;
        }
        
        .email-container {
            max-width: 650px;
            margin: 20px auto;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a67d8 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header.assigned {
            background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        }
        
        .header.completed {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .header.accompanist {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .mission-icon {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .mission-id {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 10px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .mission-type-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            margin: 10px 5px;
        }
        
        .mission-type-badge.sitesurvey {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .mission-type-badge.visiteavantvente {
            background: #f3e5f5;
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }
        
        .mission-type-badge.visitemaintenance {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #a5d6a7;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            margin: 10px 5px;
        }
        
        .status-badge.nouvelle {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-badge.assignee {
            background: #ffeaa7;
            color: #856404;
            border: 1px solid #ffd32a;
        }
        
        .status-badge.terminee {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-badge.accompagnateur {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #495057;
        }
        
        .mission-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #6f42c1;
            padding: 25px;
            border-radius: 8px;
            margin: 25px 0;
        }
        
        .mission-summary.assigned {
            border-left-color: #fd7e14;
        }
        
        .mission-summary.completed {
            border-left-color: #28a745;
        }
        
        .mission-summary.accompanist {
            border-left-color: #17a2b8;
        }
        
        .summary-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .summary-text {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
        }
        
        .date-highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .date-highlight h4 {
            color: #856404;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .date-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .date-box {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            flex: 1;
            min-width: 120px;
        }
        
        .date-box h5 {
            margin: 0 0 5px 0;
            color: #6c757d;
            font-size: 12px;
            text-transform: uppercase;
        }
        
        .date-box span {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .metadata {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .metadata h3 {
            color: #495057;
            font-size: 18px;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .metadata-grid {
            display: grid;
            gap: 15px;
        }
        
        .metadata-item {
            display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #f8f9fa;
            align-items: flex-start;
        }
        
        .metadata-item:last-child {
            border-bottom: none;
        }
        
        .metadata-label {
            font-weight: 600;
            color: #6c757d;
            min-width: 160px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .metadata-value {
            color: #333333;
            font-size: 14px;
            flex: 1;
            line-height: 1.5;
        }
        
        .metadata-value.important {
            font-weight: 600;
            color: #dc3545;
        }
        
        .metadata-value.success {
            font-weight: 600;
            color: #28a745;
        }
        
        .action-section {
            text-align: center;
            margin: 30px 0;
        }
        
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #6f42c1 0%, #5a67d8 100%);
            color: white;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.4);
        }
        
        .action-button.assigned {
            background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.4);
        }
        
        .action-button.completed {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }
        
        .action-button.accompanist {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(111, 66, 193, 0.5);
        }
        
        .footer {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
        .footer-content {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .footer p {
            font-size: 12px;
            color: #6c757d;
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .footer-logo {
            font-weight: 700;
            color: #495057;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .header {
                padding: 25px 20px;
            }
            
            .header h1 {
                font-size: 22px;
            }
            
            .content {
                padding: 25px 20px;
            }
            
            .metadata {
                padding: 20px;
            }
            
            .metadata-item {
                flex-direction: column;
                gap: 5px;
            }
            
            .metadata-label {
                min-width: auto;
                font-weight: 700;
            }
            
            .date-info {
                flex-direction: column;
            }
            
            .footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header" 
             th:classappend="${eventType == 'MISSION_ASSIGNED' ? 'assigned' : (eventType == 'MISSION_COMPLETED' ? 'completed' : (eventType == 'ACCOMPANIST_ASSIGNED' ? 'accompanist' : ''))}">
            <div class="header-content">
                <div class="mission-icon">
                    <span th:if="${eventType == 'MISSION_COMPLETED'}">✅</span>
                    <span th:if="${eventType == 'MISSION_ASSIGNED'}">🎯</span>
                    <span th:if="${eventType == 'ACCOMPANIST_ASSIGNED'}">👥</span>
                    <span th:if="${eventType != 'MISSION_COMPLETED' && eventType != 'MISSION_ASSIGNED' && eventType != 'ACCOMPANIST_ASSIGNED'}">📋</span>
                </div>
                <h1 th:text="${title}">Mission Assignée</h1>
                <div class="mission-id" th:if="${entiteLieeId != null}">
                    Mission #<span th:text="${entiteLieeId}">001</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <p class="greeting">Bonjour,</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <div class="status-badge" 
                     th:classappend="${eventType == 'MISSION_COMPLETED' ? 'terminee' : (eventType == 'MISSION_ASSIGNED' ? 'assignee' : (eventType == 'ACCOMPANIST_ASSIGNED' ? 'accompagnateur' : 'nouvelle'))}">
                    <span th:if="${eventType == 'MISSION_COMPLETED'}">✅ Mission Terminée</span>
                    <span th:if="${eventType == 'MISSION_ASSIGNED'}">🎯 Mission Assignée</span>
                    <span th:if="${eventType == 'ACCOMPANIST_ASSIGNED'}">👥 Accompagnateur Assigné</span>
                    <span th:if="${eventType != 'MISSION_COMPLETED' && eventType != 'MISSION_ASSIGNED' && eventType != 'ACCOMPANIST_ASSIGNED'}">📋 Nouvelle Mission</span>
                </div>
                
                <div class="mission-type-badge" 
                     th:if="${metadata != null && metadata.containsKey('typeMission')}"
                     th:classappend="${metadata.typeMission != null ? #strings.toLowerCase(metadata.typeMission) : ''}">
                    <span th:if="${metadata.typeMission == 'SITESURVEY'}">🔍 Site Survey</span>
                    <span th:if="${metadata.typeMission == 'VISITEAVANTVENTE'}">💼 Visite Avant-Vente</span>
                    <span th:if="${metadata.typeMission == 'VISITEMAINTENANCE'}">🔧 Visite Maintenance</span>
                    <span th:if="${metadata.typeMission != null && metadata.typeMission != 'SITESURVEY' && metadata.typeMission != 'VISITEAVANTVENTE' && metadata.typeMission != 'VISITEMAINTENANCE'}" th:text="${metadata.typeMission}">Type Mission</span>
                </div>
            </div>
            
            <div class="mission-summary"
                 th:classappend="${eventType == 'MISSION_ASSIGNED' ? 'assigned' : (eventType == 'MISSION_COMPLETED' ? 'completed' : (eventType == 'ACCOMPANIST_ASSIGNED' ? 'accompanist' : ''))}">
                <div class="summary-title" th:text="${title}">Nouvelle mission créée</div>
                <div class="summary-text" th:text="${message}">
                    Une nouvelle mission a été créée et nécessite votre attention.
                </div>
            </div>
            
            <!-- Dates de mission -->
            <div class="date-highlight" th:if="${metadata != null && (metadata.containsKey('dateDebut') || metadata.containsKey('dateFin'))}">
                <h4>📅 Période de la Mission</h4>
                <div class="date-info">
                                         <div class="date-box" th:if="${metadata.containsKey('dateDebut') && metadata.dateDebut != null}">
                         <h5>Début</h5>
                         <span th:text="${metadata.dateDebut}">01/01/2023 08:00</span>
                     </div>
                     <div class="date-box" th:if="${metadata.containsKey('dateFin') && metadata.dateFin != null}">
                         <h5>Fin</h5>
                         <span th:text="${metadata.dateFin}">01/01/2023 17:00</span>
                     </div>
                </div>
            </div>
            
            <!-- Détails de la mission -->
            <div class="metadata" th:if="${metadata != null && !metadata.isEmpty()}">
                <h3>📋 Détails de la mission</h3>
                <div class="metadata-grid">
                    <!-- Référence -->
                    <div class="metadata-item" th:if="${metadata.containsKey('reference')}">
                        <span class="metadata-label">🔗 Référence:</span>
                        <span class="metadata-value important" th:text="${metadata.reference}">MISSION-001</span>
                    </div>
                    
                    <!-- Type de mission -->
                    <div class="metadata-item" th:if="${metadata.containsKey('typeMission')}">
                        <span class="metadata-label">🏷️ Type:</span>
                        <span class="metadata-value" th:text="${metadata.typeMission}">SITESURVEY</span>
                    </div>
                    
                    <!-- Lieu de mission -->
                    <div class="metadata-item" th:if="${metadata.containsKey('lieu')}">
                        <span class="metadata-label">📍 Lieu:</span>
                        <span class="metadata-value" th:text="${metadata.lieu}">Tunis Centre</span>
                    </div>
                    
                    <!-- Description -->
                    <div class="metadata-item" th:if="${metadata.containsKey('description')}">
                        <span class="metadata-label">📝 Description:</span>
                        <span class="metadata-value" th:text="${metadata.description}">Description détaillée de la mission</span>
                    </div>
                    
                    <!-- Accompagnateur -->
                    <div class="metadata-item" th:if="${metadata.containsKey('accompagnantNom')}">
                        <span class="metadata-label">👤 Accompagnateur:</span>
                        <span class="metadata-value success" th:text="${metadata.accompagnantNom}">Nom de l'accompagnateur</span>
                    </div>
                    
                    <!-- Créée par -->
                    <div class="metadata-item" th:if="${metadata.containsKey('createdBy')}">
                        <span class="metadata-label">✏️ Créée par:</span>
                        <span class="metadata-value" th:text="${metadata.createdBy}">Utilisateur</span>
                    </div>
                </div>
            </div>
            
            <!-- Bouton d'action -->
            <div class="action-section" th:if="${urlAction != null}">
                <a th:href="${urlAction}" 
                   class="action-button"
                   th:classappend="${eventType == 'MISSION_ASSIGNED' ? 'assigned' : (eventType == 'MISSION_COMPLETED' ? 'completed' : (eventType == 'ACCOMPANIST_ASSIGNED' ? 'accompanist' : ''))}">
                    <span th:if="${eventType == 'MISSION_COMPLETED'}">✅ Voir la mission terminée</span>
                    <span th:if="${eventType == 'MISSION_ASSIGNED'}">🎯 Voir ma mission</span>
                    <span th:if="${eventType == 'ACCOMPANIST_ASSIGNED'}">👥 Voir la mission d'accompagnement</span>
                    <span th:if="${eventType != 'MISSION_COMPLETED' && eventType != 'MISSION_ASSIGNED' && eventType != 'ACCOMPANIST_ASSIGNED'}">📋 Voir les détails de la mission</span>
                </a>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-content">
                <div class="footer-logo">OTBS - OneTech Business Solutions</div>
                <p>Ce message a été envoyé automatiquement par la plateforme Parc Auto.</p>
                <p>© 2025 OTBS - OneTech Business Solutions - Tous droits réservés</p>
                <p>🎯 Plateforme de gestion efficace des missions terrain</p>
            </div>
        </div>
    </div>
</body>
</html>