package otbs.ms_notification.client.keycloak;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Configuration  
public class CacheConfig {

    @Value("${spring.cache.caffeine.spec:maximumSize=1000,expireAfterWrite=3600s}")
    private String cacheSpec;

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
        );

        cacheManager.setCacheNames(List.of("users", "userEmails", "userInfo", "userNames", "userSelectors", 
                                           "userContactsByRole", "activeUserContactsByRole"));
        
        return cacheManager;
    }
} 