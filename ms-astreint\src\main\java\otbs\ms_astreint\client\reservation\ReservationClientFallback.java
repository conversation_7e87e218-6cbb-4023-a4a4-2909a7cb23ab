package otbs.ms_astreint.client.reservation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import otbs.ms_astreint.client.reservation.dto.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Implémentation de fallback pour le client Feign ReservationClient.
 * Cette classe fournit des réponses par défaut lorsque le service de réservation est indisponible.
 */
@Component
@Slf4j
public class ReservationClientFallback implements ReservationClient {

    @Override
    public ReservationDto addReservation(ReservationDto reservationDto) {
        log.warn("Fallback activé pour addReservation");
        if (reservationDto != null) {
            return reservationDto;
        }
        return createDefaultReservation();
    }

    @Override
    public FullReservationDto getReservationsByDescription(int page, int size, String description) {
        log.warn("Fallback activé pour getReservationsByDescription avec description: {}", description);
        return createDefaultFullReservation();
    }

    @Override
    public ConsommationCarburantDto addConsommationCarburant(ConsommationCarburantDto dto, MultipartFile file) {
        log.warn("Fallback activé pour addConsommationCarburant");
        if (dto != null) {
            return dto;
        }
        return createDefaultConsommationCarburant();
    }

    @Override
    public ConsommationCarburantDto updateConsommation(Long id, ConsommationCarburantDto dto, MultipartFile file) {
        log.warn("Fallback activé pour updateConsommation avec id: {}", id);
        if (dto != null) {
            dto.setId(id);
            return dto;
        }
        return createDefaultConsommationCarburant();
    }

    @Override
    public ConsommationTelepeageDto addNewConsommationTelepeageByReservation(ConsommationTelepeageDto consommationTelepeageDto) {
        log.warn("Fallback activé pour addNewConsommationTelepeageByReservation");
        if (consommationTelepeageDto != null) {
            return consommationTelepeageDto;
        }
        return createDefaultConsommationTelepeage();
    }

    @Override
    public ConsommationTelepeageDto updateConsommationTelepeageByReservation(ConsommationTelepeageDto consommationTelepeageDto) {
        log.warn("Fallback activé pour updateConsommationTelepeageByReservation");
        if (consommationTelepeageDto != null) {
            return consommationTelepeageDto;
        }
        return createDefaultConsommationTelepeage();
    }

    @Override
    public TicketRestaurantDto addTicket(TicketRestaurantDto dto) {
        log.warn("Fallback activé pour addTicket");
        if (dto != null) {
            return dto;
        }
        return createDefaultTicketRestaurant();
    }

    @Override
    public TicketRestaurantDto updateTicketByReservationId(Long reservationId, TicketRestaurantDto dto) {
        log.warn("Fallback activé pour updateTicketByReservationId avec reservationId: {}", reservationId);
        if (dto != null) {
            dto.setReservationId(reservationId);
            return dto;
        }
        return createDefaultTicketRestaurant();
    }

    @Override
    public List<TelepeageDto> getAllTelepeages() {
        log.warn("Fallback activé pour getAllTelepeages");
        return new ArrayList<>();
    }

    @Override
    public List<VehiculeDto> getAllAvailableCarsByPeriod(LocalDateTime dateDebut, LocalDateTime dateFin) {
        log.warn("Fallback activé pour getAllAvailableCarsByPeriod avec période: {} - {}", dateDebut, dateFin);
        return new ArrayList<>();
    }

    @Override
    public void deleteReservation(Long reservationId) {
        log.warn("Fallback activé pour deleteReservation avec id: {}", reservationId);
        // Rien à faire en mode fallback pour une suppression
    }

    // ========== FALLBACKS POUR LES NOUVEAUX ENDPOINTS ==========

    @Override
    public TelepeageDto getTelepeageByReservation(Long reservationId) {
        log.warn("Fallback activé pour getTelepeageByReservation avec reservationId: {}", reservationId);
        return new TelepeageDto();
    }

    @Override
    public Double getConsommationParReservation(Long reservationId) {
        log.warn("Fallback activé pour getConsommationParReservation avec reservationId: {}", reservationId);
        return 0.0;
    }

    @Override
    public List<PaimentCarburantCashDto> getPaimentsByReservation(Long reservationId) {
        log.warn("Fallback activé pour getPaimentsByReservation avec reservationId: {}", reservationId);
        return new ArrayList<>();
    }

    @Override
    public PaimentCarburantCashDto uploadPaiement(PaimentCarburantCashDto dto) {
        log.warn("Fallback activé pour uploadPaiement");
        return new PaimentCarburantCashDto();
    }

    @Override
    public ConsommationCarburantDto ajouterConsommationCash(ConsommationCarburantDto dto, MultipartFile file) {
        log.warn("Fallback activé pour ajouterConsommationCash");
        return createDefaultConsommationCarburant();
    }

    @Override
    public ConsommationCarburantDto reimburseCash(Long id) {
        log.warn("Fallback activé pour reimburseCash avec id: {}", id);
        return createDefaultConsommationCarburant();
    }

    @Override
    public List<ConsommationCarburantDto> getConsommationsByReservation(Long reservationId) {
        log.warn("Fallback activé pour getConsommationsByReservation avec reservationId: {}", reservationId);
        return new ArrayList<>();
    }

    @Override
    public List<ConsommationCarburantDto> getConsommationsByReservationAndType(Long reservationId, TypeConsommation type) {
        log.warn("Fallback activé pour getConsommationsByReservationAndType avec reservationId: {} et type: {}", reservationId, type);
        return new ArrayList<>();
    }

    @Override
    public CarteCarburantDto getCarteCarburantById(Long id) {
        log.warn("Fallback activé pour getCarteCarburantById avec id: {}", id);
        return new CarteCarburantDto();
    }

    @Override
    public List<CarteCarburantDto> getAllCartesCarburant() {
        log.warn("Fallback activé pour getAllCartesCarburant");
        return new ArrayList<>();
    }

    // Méthodes utilitaires pour créer des objets par défaut

    private ReservationDto createDefaultReservation() {
        ReservationDto reservation = new ReservationDto();
        reservation.setDateDebut(LocalDateTime.now());
        reservation.setDateFin(LocalDateTime.now().plusHours(1));
        reservation.setDescription("Service indisponible");
        return reservation;
    }

    private FullReservationDto createDefaultFullReservation() {
        FullReservationDto reservation = new FullReservationDto();
        reservation.setDateDebut(LocalDateTime.now());
        reservation.setDateFin(LocalDateTime.now().plusHours(1));
        reservation.setDescription("Service indisponible");
        reservation.setMissionIds(new ArrayList<>());
        reservation.setNumerosCartes(new ArrayList<>());
        return reservation;
    }

    private ConsommationCarburantDto createDefaultConsommationCarburant() {
        ConsommationCarburantDto consommation = new ConsommationCarburantDto();
        consommation.setMontantSortie(0.0);
        consommation.setMontantConsommer(0.0);
        consommation.setTypeConsommation(TypeConsommation.CASH);
        return consommation;
    }

    private ConsommationTelepeageDto createDefaultConsommationTelepeage() {
        ConsommationTelepeageDto consommation = new ConsommationTelepeageDto();
        consommation.setMontant(0.0);
        return consommation;
    }

    private TicketRestaurantDto createDefaultTicketRestaurant() {
        TicketRestaurantDto ticket = new TicketRestaurantDto();
        ticket.setNbTicket(0);
        ticket.setMontant(0.0);
        return ticket;
    }
}
