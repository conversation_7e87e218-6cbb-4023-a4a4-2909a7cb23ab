package otbs.vehicule.repository;

import java.sql.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import otbs.vehicule.model.Vidange;


@Repository
public interface VidangeRepo extends JpaRepository<Vidange, Long> {
    
     Optional<Vidange> findByIdVidange(Long idVidange);

     void removeByIdVidange(Long idVidange);

     @Query("SELECT COUNT(v) FROM Vidange v WHERE v.date BETWEEN :startDate AND :endDate")
    long countVidangesInLastMonth(Date startDate, Date endDate);

    @Query("SELECT v FROM Vidange v WHERE YEAR(v.date) = :year")
    List<Vidange> findAllByYear(@Param("year") int year);

    // Pour compter les vidanges dans le mois
    @Query("SELECT COUNT(v) FROM Vidange v WHERE v.date BETWEEN :startDate AND :endDate")
    long countVidangesInMonth(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // Pour compter les vidanges dans le trimestre
    @Query("SELECT COUNT(v) FROM Vidange v WHERE v.date BETWEEN :startDate AND :endDate")
    long countVidangesInTrimester(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // Pour compter les vidanges dans l'année
    @Query("SELECT COUNT(v) FROM Vidange v WHERE v.date BETWEEN :startDate AND :endDate")
    long countVidangesInYear(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // Pour trouver les vidanges dont le kilométrage est inférieur à 1000 km ou 2000 km du kilométrage de la prochaine vidange
    @Query("SELECT v FROM Vidange v WHERE ABS(v.kilometrageProchainVidange - v.kilometrage) <= :kilometrageRange")
    List<Vidange> findVidangesWithKilometrageNearNext(@Param("kilometrageRange") long kilometrageRange);

    @Query("SELECT EXTRACT(MONTH FROM v.date) AS mois, SUM(v.montant) AS montant " +
    "FROM Vidange v WHERE EXTRACT(YEAR FROM v.date) = :year " +
    "GROUP BY EXTRACT(MONTH FROM v.date) ORDER BY mois")
     List<Object[]> getTotalMontantGroupedByMonth(@Param("year") int year);

     @Query("SELECT " +
       "CASE " +
       "WHEN EXTRACT(MONTH FROM v.date) BETWEEN 1 AND 3 THEN 1 " +
       "WHEN EXTRACT(MONTH FROM v.date) BETWEEN 4 AND 6 THEN 2 " +
       "WHEN EXTRACT(MONTH FROM v.date) BETWEEN 7 AND 9 THEN 3 " +
       "ELSE 4 END AS trimestre, " +
       "SUM(v.montant) AS montant " +
       "FROM Vidange v WHERE EXTRACT(YEAR FROM v.date) = :year " +
       "GROUP BY trimestre ORDER BY trimestre")
     List<Object[]> getTotalMontantGroupedByTrimester(@Param("year") int year);

     @Query("SELECT EXTRACT(MONTH FROM v.date) AS mois, EXTRACT(WEEK FROM v.date) AS semaine, SUM(v.montant) AS montant " +
     "FROM Vidange v WHERE EXTRACT(YEAR FROM v.date) = :year " +
     "GROUP BY EXTRACT(MONTH FROM v.date), EXTRACT(WEEK FROM v.date) " +
     "ORDER BY mois, semaine")
     List<Object[]> getTotalMontantGroupedByWeekAndMonth(@Param("year") int year);

}
