emailVerificationSubject=Verificação de endereço de e-mail
emailVerificationBody=Alguém criou uma conta {2} com este endereço de e-mail. Se foi você, clique na ligação abaixo para verificar o seu endereço de e-mail.\n\n{0}\n\nEsta ligação irá expirar dentro de {3}.\n\nSe não foi você quem criou esta conta, basta ignorar esta mensagem.
emailVerificationBodyHtml=<p>Alguém criou uma conta {2} com este endereço de e-mail. Se foi você, clique na ligação abaixo para verificar o seu endereço de email.</p><p><a href="{0}">Ligação para verificação de endereço de e-mail</a></p><p>Esta ligação irá expirar dentro de {3}.</p><p>Se não foi você quem criou esta conta, basta ignorar esta mensagem.</p>
emailUpdateConfirmationSubject=Verificar novo endereço de e-mail
emailUpdateConfirmationBody=Para atualizar a sua conta {2} com o endereço de e-mail {1}, carregue na ligação abaixo\n\n{0}\n\nEsta ligação vai expirar dentro de {3}.\n\nSe não deseja proceder com esta alteração ignore esta mensagem.
emailUpdateConfirmationBodyHtml=<p>Para atualizar a sua conta {2} com o endereço de e-mail {1}, carregue na ligação abaixo</p><p><a href="{0}">{0}</a></p><p>Esta ligação vai expirar dentro de {3}.</p><p>Se não deseja proceder com esta alteração ignore esta mensagem.</p>
emailTestSubject=[KEYCLOAK] - Mensagem de teste SMTP
emailTestBody=Esta é uma mensagem de teste
emailTestBodyHtml=<p>Esta é uma mensagem de teste</p>
identityProviderLinkSubject=Vincular {0}
identityProviderLinkBody=Alguém quer vincular a sua conta "{1}" com a conta "{0}" do utilizador {2} . Se foi você, clique na ligação abaixo para vincular as contas.\n\n{3}\n\nEsta ligação irá expirar em {5}.\n\nSe você não quer vincular a conta, apenas ignore esta mensagem. Se vincular as contas, será capaz de logar em {1} fazendo login em {0}.
identityProviderLinkBodyHtml=<p>Alguém quer vincular a sua conta <b>{1}</b> com a conta <b>{0}</b> do utilizador {2} . Se foi você, clique na ligação abaixo para vincular as contas.</p><p><a href="{3}">Ligação para confirmar vinculação de contas</a></p><p>Esta ligação irá expirar em {5}.</p><p>Se você não quer vincular a conta, apenas ignore esta mensagem. Se vincular as contas, será capaz de logar em {1} fazendo login em {0}.</p>
passwordResetSubject=Redefinição da palavra-passe
passwordResetBody=Alguém solicitou uma alteração da palavra-passe da sua conta {2}. Se foi você, clique na ligação abaixo para redefini-la.\n\n{0}\n\nEsta ligação e código expiram em {3}.\n\nSe não deseja redefinir a sua palavra-passe, apenas ignore esta mensagem e nada será alterado.
passwordResetBodyHtml=<p>Alguém solicitou uma alteração de palavra-passe da sua conta {2}. Se foi você, clique na ligação abaixo para redefini-la.</p><p><a href="{0}">Ligação para redefinir a palavra-passe</a></p><p>Esta ligação irá expirar em {3}.</p><p>Se não deseja redefinir sua palavra-passe, apenas ignore esta mensagem e nada será alterado.</p>
executeActionsSubject=Atualização de conta
executeActionsBody=Um administrador solicitou que atualize a sua conta {2} com a(s) seguinte(s) etapa(s): {3}. Clique na ligação abaixo para iniciar o processo.\n\n{0}\n\nEsta ligação irá expirar em {4}.\n\nSe não tem conhecimento de que o administrador solicitou isso, basta ignorar esta mensagem e nada será alterado.
executeActionsBodyHtml=<p>Um administrador solicitou que atualize sua conta {2} com a(s) seguinte(s) etapa(s): {3}. Clique na ligação abaixo para iniciar o processo.</p><p><a href="{0}">Ligação para atualizar a conta</a></p><p>Esta ligação irá expirar em {4}.</p><p>Se não tem conhecimento de que o administrador solicitou isso, basta ignorar esta mensagem e nada será alterado.</p>
eventLoginErrorSubject=Erro de login
eventLoginErrorBody=Uma tentativa de login sem sucesso da sua conta foi detetada em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.
eventLoginErrorBodyHtml=<p>Uma tentativa de login sem sucesso da sua conta foi detetada em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.</p>
eventRemoveTotpSubject=Remover autenticação de dois fatores
eventRemoveTotpBody=A autenticação de dois fatores foi removida da sua conta em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.
eventRemoveTotpBodyHtml=<p>A autenticação de dois fatores foi removida da sua conta em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.</p>
eventUpdatePasswordSubject=Atualização de palavra-passe
eventUpdatePasswordBody=A sua palavra-passe foi alterada em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.
eventUpdatePasswordBodyHtml=<p>A sua palavra-passe foi alterada em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.</p>
eventUpdateTotpSubject=Atualização de autenticação de dois fatores
eventUpdateTotpBody=A autenticação de dois fatores foi atualizada para a sua conta em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.
eventUpdateTotpBodyHtml=<p>A autenticação de dois fatores foi atualizada para a sua conta em {0} de {1}. Se não foi você, por favor, entre em contacto com um administrador.</p>

requiredAction.CONFIGURE_TOTP=Configurar Autenticação de Dois Fatores
requiredAction.TERMS_AND_CONDITIONS=Termos e Condições
requiredAction.UPDATE_PASSWORD=Atualizar Palavra-passe
requiredAction.UPDATE_PROFILE=Atualizar Perfil
requiredAction.VERIFY_EMAIL=Verificar Endereço de E-mail
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Gerar Códigos de Recuperação

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#segundos|1#segundo|1<segundos}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minutos|1#minuto|1<minutos}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#horas|1#hora|1<horas}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dias|1#dia|1<dias}

emailVerificationBodyCode=Verifique o seu endereço de e-mail inserindo o seguinte código.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Verifique o seu endereço de e-mail inserindo o seguinte código.</p><p><b>{0}</b></p>

