package otbs.ms_mission.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import otbs.ms_mission.dto.MissionTypeStatsDTO;
import otbs.ms_mission.enums.TypeMission;
import otbs.ms_mission.repository.MissionRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service pour les statistiques des missions par type.
 * Gère les types granulaires (PREVENTIVE/CURATIVE) côté backend
 * tout en maintenant la compatibilité avec le frontend unifié.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class MissionTypeStatsService {

    private final MissionRepository missionRepository;

    /**
     * Calcule les statistiques des missions par type pour une période donnée.
     *
     * @param periodeDebut Début de la période
     * @param periodeFin   Fin de la période
     * @return Statistiques par type de mission
     */
    public MissionTypeStatsDTO getMissionTypeStats(LocalDateTime periodeDebut, LocalDateTime periodeFin) {
        log.debug("Calcul des statistiques des missions par type pour la période: {} - {}",
                periodeDebut, periodeFin);

        if (periodeDebut == null || periodeFin == null) {
            throw new IllegalArgumentException("Les dates de début et de fin de période sont obligatoires");
        }

        if (periodeDebut.isAfter(periodeFin)) {
            throw new IllegalArgumentException("La date de début ne peut pas être postérieure à la date de fin");
        }

        try {
            // Récupération des statistiques en une seule requête optimisée
            List<Object[]> results = missionRepository.countAllMissionTypesInPeriod(periodeDebut, periodeFin);

            // Conversion en Map pour faciliter l'accès
            Map<String, Long> typeCountMap = new HashMap<>();
            for (Object[] result : results) {
                String type = result[0].toString();
                Long count = ((Number) result[1]).longValue();
                typeCountMap.put(type, count);
                log.debug("Type {} dans la période: {} missions", type, count);
            }

            // Récupérer les types granulaires
            Long visitepreventive = typeCountMap.getOrDefault("VISITEPREVENTIVE", 0L);
            Long visitecurative = typeCountMap.getOrDefault("VISITECURATIVE", 0L);
            
            // Gérer la compatibilité avec l'ancien type unifié VISITEMAINTENANCE
            Long legacyMaintenance = typeCountMap.getOrDefault("VISITEMAINTENANCE", 0L);
            if (legacyMaintenance > 0) {
                // Répartir équitablement l'ancien type unifié
                visitepreventive += legacyMaintenance / 2;
                visitecurative += legacyMaintenance - (legacyMaintenance / 2); // Reste pour éviter la perte
            }
            
            Long projet = typeCountMap.getOrDefault("PROJET", 0L);
            // Ajouter le compteur de l'ancien type s'il existe encore dans la base
            projet += typeCountMap.getOrDefault("VISITEPROJET", 0L);

            // Construction du DTO avec les valeurs récupérées
            MissionTypeStatsDTO stats = MissionTypeStatsDTO.builder()
                    .sitesurvey(typeCountMap.getOrDefault("SITESURVEY", 0L))
                    .visiteavantvente(typeCountMap.getOrDefault("VISITEAVANTVENTE", 0L))
                    .visitepreventive(visitepreventive)
                    .visitecurative(visitecurative)
                    .projet(projet)
                    .build();

            // Calcul du total
            Long total = stats.calculateTotal();
            stats.setTotal(total);

            log.info("Statistiques par type calculées pour la période: Total={}, SITESURVEY={}, VISITEAVANTVENTE={}, " +
                    "VISITEPREVENTIVE={}, VISITECURATIVE={}, PROJET={}",
                    total, stats.getSitesurvey(), stats.getVisiteavantvente(),
                    stats.getVisitepreventive(), stats.getVisitecurative(), stats.getProjet());

            return stats;

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques par type pour la période", e);

            // Retourner des statistiques vides en cas d'erreur
            return MissionTypeStatsDTO.builder()
                    .sitesurvey(0L)
                    .visiteavantvente(0L)
                    .visitepreventive(0L)
                    .visitecurative(0L)
                    .projet(0L)
                    .total(0L)
                    .build();
        }
    }

    /**
     * Calcule les statistiques des missions par type (toutes les missions).
     * Méthode de compatibilité pour les appels sans période.
     *
     * @return Statistiques par type de mission
     */
    public MissionTypeStatsDTO getMissionTypeStats() {
        log.debug("Calcul des statistiques des missions par type (toutes les missions)");

        try {
            // Récupération des statistiques en une seule requête optimisée
            List<Object[]> results = missionRepository.countAllMissionTypes();

            // Conversion en Map pour faciliter l'accès
            Map<String, Long> typeCountMap = new HashMap<>();
            for (Object[] result : results) {
                String type = result[0].toString();
                Long count = ((Number) result[1]).longValue();
                typeCountMap.put(type, count);
                log.debug("Type {}: {} missions", type, count);
            }

            // Récupérer les types granulaires
            Long visitepreventive = typeCountMap.getOrDefault("VISITEPREVENTIVE", 0L);
            Long visitecurative = typeCountMap.getOrDefault("VISITECURATIVE", 0L);
            
            // Gérer la compatibilité avec l'ancien type unifié VISITEMAINTENANCE
            Long legacyMaintenance = typeCountMap.getOrDefault("VISITEMAINTENANCE", 0L);
            if (legacyMaintenance > 0) {
                // Répartir équitablement l'ancien type unifié
                visitepreventive += legacyMaintenance / 2;
                visitecurative += legacyMaintenance - (legacyMaintenance / 2); // Reste pour éviter la perte
            }
            
            Long projet = typeCountMap.getOrDefault("PROJET", 0L);
            // Ajouter le compteur de l'ancien type s'il existe encore dans la base
            projet += typeCountMap.getOrDefault("VISITEPROJET", 0L);

            // Construction du DTO avec les valeurs récupérées
            MissionTypeStatsDTO stats = MissionTypeStatsDTO.builder()
                    .sitesurvey(typeCountMap.getOrDefault("SITESURVEY", 0L))
                    .visiteavantvente(typeCountMap.getOrDefault("VISITEAVANTVENTE", 0L))
                    .visitepreventive(visitepreventive)
                    .visitecurative(visitecurative)
                    .projet(projet)
                    .build();

            // Calcul du total
            Long total = stats.calculateTotal();
            stats.setTotal(total);

            log.info("Statistiques par type calculées: Total={}, SITESURVEY={}, VISITEAVANTVENTE={}, " +
                    "VISITEPREVENTIVE={}, VISITECURATIVE={}, PROJET={}",
                    total, stats.getSitesurvey(), stats.getVisiteavantvente(),
                    stats.getVisitepreventive(), stats.getVisitecurative(), stats.getProjet());

            return stats;

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques par type", e);

            // Retourner des statistiques vides en cas d'erreur
            return MissionTypeStatsDTO.builder()
                    .sitesurvey(0L)
                    .visiteavantvente(0L)
                    .visitepreventive(0L)
                    .visitecurative(0L)
                    .projet(0L)
                    .total(0L)
                    .build();
        }
    }

    /**
     * Calcule les statistiques par type avec des requêtes individuelles (méthode alternative).
     * Utilisée comme fallback si la requête groupée échoue.
     *
     * @return Statistiques par type de mission
     */
    public MissionTypeStatsDTO getMissionTypeStatsIndividual() {
        log.debug("Calcul des statistiques par type avec requêtes individuelles");

        try {
            Long sitesurvey = missionRepository.countSiteSurveyMissions();
            Long visiteavantvente = missionRepository.countVisiteAvantVenteMissions();
            
            // Compter les missions de maintenance (nouveaux types granulaires + ancien type unifié)
            Long visitepreventive = missionRepository.countVisitePreventiveMissions();
            Long visitecurative = missionRepository.countVisiteCurativeMissions();
            
            // Gérer la compatibilité avec l'ancien type unifié
            Long legacyMaintenance = missionRepository.countVisiteMaintenanceMissions();
            if (legacyMaintenance > 0) {
                visitepreventive += legacyMaintenance / 2;
                visitecurative += legacyMaintenance - (legacyMaintenance / 2);
            }
            
            // Compter les missions projet (nouveaux + anciens types)
            Long projet = missionRepository.countProjetMissions();
            projet += missionRepository.countVisiteProjetMissions();

            MissionTypeStatsDTO stats = MissionTypeStatsDTO.builder()
                    .sitesurvey(sitesurvey != null ? sitesurvey : 0L)
                    .visiteavantvente(visiteavantvente != null ? visiteavantvente : 0L)
                    .visitepreventive(visitepreventive != null ? visitepreventive : 0L)
                    .visitecurative(visitecurative != null ? visitecurative : 0L)
                    .projet(projet != null ? projet : 0L)
                    .build();

            // Calcul du total
            Long total = stats.calculateTotal();
            stats.setTotal(total);

            log.info("Statistiques par type (individuelles) calculées: Total={}", total);

            return stats;

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques individuelles par type", e);

            // Retourner des statistiques vides en cas d'erreur
            return MissionTypeStatsDTO.builder()
                    .sitesurvey(0L)
                    .visiteavantvente(0L)
                    .visitepreventive(0L)
                    .visitecurative(0L)
                    .projet(0L)
                    .total(0L)
                    .build();
        }
    }

    /**
     * Récupère le nombre de missions pour un type spécifique.
     *
     * @param type Type de mission
     * @return Nombre de missions
     */
    public Long getMissionCountByType(TypeMission type) {
        if (type == null) {
            return 0L;
        }

        try {
            switch (type) {
                case SITESURVEY:
                    return missionRepository.countSiteSurveyMissions();
                case VISITEAVANTVENTE:
                    return missionRepository.countVisiteAvantVenteMissions();
                case VISITEPREVENTIVE:
                    Long preventiveCount = missionRepository.countVisitePreventiveMissions();
                    // Ajouter la moitié des anciens types unifiés pour la compatibilité
                    Long legacyMaintenance = missionRepository.countVisiteMaintenanceMissions();
                    preventiveCount += legacyMaintenance / 2;
                    return preventiveCount;
                case VISITECURATIVE:
                    Long curativeCount = missionRepository.countVisiteCurativeMissions();
                    // Ajouter la moitié restante des anciens types unifiés
                    Long legacyMaintenanceCurative = missionRepository.countVisiteMaintenanceMissions();
                    curativeCount += legacyMaintenanceCurative - (legacyMaintenanceCurative / 2);
                    return curativeCount;
                case PROJET:
                    Long projetCount = missionRepository.countProjetMissions();
                    // Ajouter le compteur de l'ancien type pour la période de transition
                    projetCount += missionRepository.countVisiteProjetMissions();
                    return projetCount;
                default:
                    return 0L;
            }
        } catch (Exception e) {
            log.error("Erreur lors du comptage des missions de type {}", type, e);
            return 0L;
        }
    }

    /**
     * Vérifie si des données existent pour tous les types de mission.
     *
     * @return true si des données existent pour au moins un type
     */
    public boolean hasDataForAllTypes() {
        try {
            MissionTypeStatsDTO stats = getMissionTypeStats();
            return stats.getTotal() > 0;
        } catch (Exception e) {
            log.error("Erreur lors de la vérification des données", e);
            return false;
        }
    }

    /**
     * Détermine le type de mission le plus fréquent.
     *
     * @return Type de mission le plus fréquent ou null si aucune donnée
     */
    public String getMostFrequentMissionType() {
        try {
            MissionTypeStatsDTO stats = getMissionTypeStats();
            
            Long maxCount = 0L;
            String mostFrequentType = null;
            
            if (stats.getSitesurvey() > maxCount) {
                maxCount = stats.getSitesurvey();
                mostFrequentType = "SITESURVEY";
            }
            
            if (stats.getVisiteavantvente() > maxCount) {
                maxCount = stats.getVisiteavantvente();
                mostFrequentType = "VISITEAVANTVENTE";
            }
            
            if (stats.getVisitepreventive() > maxCount) {
                maxCount = stats.getVisitepreventive();
                mostFrequentType = "VISITEPREVENTIVE";
            }
            
            if (stats.getVisitecurative() > maxCount) {
                maxCount = stats.getVisitecurative();
                mostFrequentType = "VISITECURATIVE";
            }
            
            if (stats.getProjet() > maxCount) {
                maxCount = stats.getProjet();
                mostFrequentType = "PROJET";
            }
            
            return mostFrequentType;
            
        } catch (Exception e) {
            log.error("Erreur lors de la détermination du type le plus fréquent", e);
            return null;
        }
    }
}
