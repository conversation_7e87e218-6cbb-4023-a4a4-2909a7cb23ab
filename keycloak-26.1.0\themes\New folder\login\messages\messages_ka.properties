emailVerifyTitle=ელფოსტის გადამოწმება
emailForgotTitle=დაგავიწყდათ პაროლი?
emailUpdateConfirmationSent=დადასტურების ელფოსტა გაიგზავნა მისამართზე {0}. ელფოსტის განახლებისთვის ელფოსტაზე გამოგზავნის ინსტრუქციებს მიჰყევით.
codeErrorTitle=შეცდომის კოდი: {0}
acceptTerms=მე ვეთანხმები წესებს და პირობებს
unknown=უცნობი
noAccount=ახალი მომხმარებელი ბრძანდებით?
firstName=სახელი
givenName=დარქმეული სახელი
fullName=სრული სახელი
lastName=გვარი
address=მისამართი
region=შტატი, პროვინცია ან რეგიონი
postal_code=Zip ან საფოსტო კოდი
phoneNumberVerified=ტელეფონის ნომერი გადამოწმებულია
birthday=დაბადების თარიღი
gender=სქესი
gssDelegationCredential=GSS დელეგაციის ავტორიზაციის დეტალები
addressScopeConsentText=მისამართი
offlineAccessScopeConsentText=ინტერნეტგარეშე წვდომა
phoneScopeConsentText=ტელეფონის ნომერი
samlRoleListScopeConsentText=ჩემი როლები
rolesScopeConsentText=მომხმარებლის როლები
organizationScopeConsentText=ორგანიზაცია
restartLoginTooltip=შესვლის თავიდან დაწყება
loginTotpStep1=დააყენეთ ერთ-ერთი ამ აპლიკაციათაგანი თქვენს მობილურზე:
loginTotpStep3DeviceName=შეიყვანეთ მოწყობილობის სახელი, რომ თქვენი OTP მოწყობილობების მართვაში დაგეხმაროთ.
loginTotpManualStep3=გამოიყენეთ შემდეგი კონფიგურაციის მნიშვნელობები, თუ აპლიკაცია საშუალებას გაძლევთ, დააყენოთ ისინი:
oauth2DeviceExpiredUserCodeMessage=კოდის ვადა ამოიწურა. დაბრუნდით თქვენს მოწყობილობაზე და დაკავშირება თავიდან სცადეთ.
oauth2DeviceVerificationFailedMessage=შეგიძლიათ დახუროთ ეს ბრაუზერის ფანჯარა, დაბრუნდეთ თქვენს მოწყობილობაზე და მიერთება თავიდან სცადოთ.
oauth2DeviceAuthorizationGrantDisabledMessage=კლიენტს OAuth 2.0 მოწყობილობის ავტორიზაციის დაწყების უფლება არ აქვს. ეს დინება გათიშულია კლიენტისთვის.
oauth2DeviceVerificationCompleteMessage=შეგიძლიათ დახუროთ ეს ბრაუზერის ფანჯარა და დაბრუნდეთ თქვენს მოწყობილობაზე.
oauth2DeviceVerificationFailedHeader=მოწყობილობის შესვლა ჩავარდა
emailVerifyInstruction2=არ მიგიღიათ გადამოწმების კოდი ელფოსტაზე?
oauth2DeviceConsentDeniedMessage=თანხმობა უარყოფილია მოწყობილობის დასაკავშირებლად.
unexpectedErrorHandlingRequestMessage=მოულოდნელი შეცდომა იდენტიფიკატორის მომწოდებელთან გასაგზავნი ავთენტიკაციის მოთხოვნის დამუშავებისას.
confirmOverrideIdpTitle=ბროკერის ბმული უკვე არსებობს
confirmLinkIdpTitle=ანგარიში უკვე არსებობს
nestedFirstBrokerFlowMessage={0} მომხმარებელი {1} ცნობილ მომხმარებელზე მიბმული არაა.
confirmLinkIdpReviewProfile=პროფილის გადახედვა
confirmOverrideIdpContinue=დიახ, გადაფარე ბმული მიმდინარე ანგარიშით
confirmLinkIdpContinue=დამატება არსებულ ანგარიშზე
updatePasswordMessage=თქვენი ანგარიშის გასააქტიურებლად პაროლის შეცვლა გჭირდებათ.
linkIdpMessage=იმისათვის, რომ თქვენი ანგარიში {0}-ზე მიაბათ, თქვენი ელფოსტის მისამართის გადამოწმებაა საჭირო.
invalidPasswordMinLengthMessage=არასწორი პაროლი: მინიმალური სიგრძეა {0}.
invalidPasswordMaxLengthMessage=არასწორი პაროლი: მაქსიმალური სიგრძეა {0}.
successLogout=თქვენ გახვედით
unknownLoginRequesterMessage=უცნობი შესვლის მომთხოვნი
bearerOnlyMessage=მხოლოდ-მატარებლის ტიპის აპლიკაციებს ბრაუზერით შესვლის დაწყების უფლება არ გააჩნიათ
standardFlowDisabledMessage=კლიენტს უფლება, რომ დაიწყოს ბრაუზერით შესვლა მითითებული პასუხის ტიპით, არ აქვს. კლიენტისთვის სტანდარტული დინება გათიშულია.
emailInstruction=შეიყვანეთ თქვენ მომხმარებლის სახელი ან ელფოსტის მისამართი და გამოგიგზავნით ინსტრუქციებს, როგორ შექმნათ ახალი პაროლი.
role_view-clients=კლიენტების ნახვა
invalidUserMessage=არასწორი მომხმარებელი ან პაროლი.
emailLinkIdp1=გამოგეგზავნათ ელფოსტა ინსტრუქციებით, როგორ მიაბათ {0} ანგარიში {1} თქვენი {2}-ის ანგარიშს.
emailLinkIdp3=ელფოსტის თავიდან გასაგზავნად.
emailLinkIdp5=გასაგრძელებლად.
emailLinkIdp2=არ მიგიღიათ გადამოწმების კოდი ელფოსტაზე?
emailLinkIdp4=თუ უკვე გადაამოწმეთ ელფოსტა სხვა ბრაუზერში
role_view-realm=რეალმის ნახვა
role_view-identity-providers=იდენტიფიკატორის მომწოდებლების ნახვა
emailInstructionUsername=შეიყვანეთ თქვენ მომხმარებლის სახელი და გამოგიგზავნით ინსტრუქციებს, როგორ შექმნათ ახალი პაროლი.
pageExpiredMsg2=შესვლის პროცესის გასაგრძელებლად
role_manage-applications=აპლიკაციების მართვა
role_offline-access=ინტერნეტგარეშე წვდომა
accountTemporarilyDisabledMessage=არასწორი მომხმარებელი ან პაროლი.
accountPermanentlyDisabledMessage=არასწორი მომხმარებელი ან პაროლი.
accountTemporarilyDisabledMessageTotp=არასწორი ავთენტიკატორის კოდი.
accountPermanentlyDisabledMessageTotp=არასწორი ავთენტიკატორის კოდი.
role_create-realm=რეალმის შექმნა
role_create-client=კლიენტის შექმნა
role_view-users=მომხმარებლების ნახვა
role_view-applications=აპლიკაციების ნახვა
role_manage-clients=კლიენტების მართვა
role_view-events=მოვლენების ნახვა
client_account=ანგარიში
role_manage-realm=რეალმის მართვა
role_manage-users=მომხმარებლების მართვა
role_manage-identity-providers=იდენტიფიკატორის მომწოდებლების მართვა
role_manage-events=მოვლენების მართვა
role_read-token=კოდის წაკითხვა
client_account-console=ანგარიშის კონსოლი
invalidRequesterMessage=არასწორი გამომთხოვი
registrationNotAllowedMessage=რეგისტრაცია დაშვებული არაა
role_view-profile=პროფილის ნახვა
role_manage-account-links=ანგარიშის ბმულების მართვა
role_manage-account=ანგარიშის მართვა
client_security-admin-console=უსაფრთხოების ადმინის კონსოლი
resetCredentialNotAllowedMessage=ავტორიზაციის დეტალების ჩამოყრა დაშვებული არაა
permissionNotApprovedMessage=წვდომა არ დადასტურდა.
expiredActionTokenNoSessionMessage=ქმედების ვადა ამოიწურა.
expiredActionTokenSessionExistsMessage=ქმედების ვადა ამოიწურა. თავიდან დაიწყეთ.
notMatchPasswordMessage=პაროლები არ ემთხვევა.
error-invalid-blank=შეიყვანეთ მნიშვნელობა.
expiredActionMessage=ქმედების ვადა ამოიწურა. გააგრძელეთ შესვლა.
missingTotpDeviceNameMessage=შეიყვანეთ მოწყობილობის სახელი.
error-empty=შეიყვანეთ მნიშვნელობა.
error-invalid-value=არასწორი მნიშვნელობა.
error-invalid-length=სიგრძის დაშვებული შუალედია {0}-{1}.
identityProviderInvalidSignatureMessage=არასწორი ხელმოწერა იდენტიფიკატორის მომწოდებლის პასუხში.
identityProviderInvalidResponseMessage=არასწორი პასუხი იდენტიფიკატორის მომწოდებლისგან.
identityProviderNotFoundMessage=იდენტიფიკატორით იდენტიფიკატორის მომწოდებელი ვერ ვიპოვე.
credentialSetupRequired=შესვლა შეუძლებელია. აუცილებელია ავტორიზაციის დეტალების მორგება.
staleCodeMessage=ეს გვერდი უკვე სწორი აღარაა. დაბრუნდით თქვენს აპლიკაციაში და თავიდან შემოდით
identityProviderNotUniqueMessage=რეალმს ერთზე მეტი იდენტიფიკატორის მომწოდებლის მხარდაჭერა გააჩნია. შეუძლებელია დადგენა, რომელი იდენტიფიკატორის მომწოდებელი უნდა იქნეს გამოყენებული ავთენტიკაციისთვის.
realmSupportsNoCredentialsMessage=რეალმს ავტორიზაციის დეტალების არც ერთი ტიპის მხარდაჭერა გააჩნია.
emailVerifiedMessage=თქვენი ელფოსტის მისამართი გადამოწმებულია.
emailVerifiedAlreadyMessage=თქვენი ელფოსტის მისამართი უკვე გადამოწმებულია.
staleEmailVerificationLink=ბმული, რომელზეც დააწკაპუნეთ, ძველი, უმოქმედო ბმულია და აღარ მუშაობს.    შეიძლება, ელფოსტა უკვე გადაამოწმეთ.
identityProviderAlreadyLinkedMessage=ფედერაციული იდენტიფიკატორი, რომელიც {0}-მა დააბრუნა, უკვე მიბმულია სხვა მომხმარებელზე.
confirmAccountLinking=დაადასტურეთ ანგარიშის {0} იდენტიფიკატორის მომწოდებლიდან {1} მიბმა თქვენს ანგარიშზე.
confirmEmailAddressVerification=დაადასტურეთ ელფოსტის მისამართის {0} სისწორე.
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=აღდგენის კოდების გენერაცია
confirmExecutionOfActions=შეასრულეთ შემდეგი ქმედებები
backToApplication=&laquo; აპლიკაციაზე დაბრუნება
brokerLinkingSessionExpired=მოთხოვნილია ბროკერის ანგარიშის მიბმა, მაგრამ მიმდინარე სესია უკვე არასწორია.
differentUserAuthenticated=ავთენტიკაცია უკვე გაიარეთ სხვა მომხმარებლით "{0}" ამ სესიაში. ჯერ უნდა გახვიდეთ.
requiredAction.TERMS_AND_CONDITIONS=წესები და პირობები
acrNotFulfilled=ავთენტიკაციის მოთხოვნები დაკმაყოფილებული არაა
requiredAction.CONFIGURE_TOTP=OTP-ის მორგება
requiredAction.webauthn-register-passwordless=Webauthn-ზე რეგისტრაცია უპაროლოდ
clientCertificate=X509 კლიენტის სერტიფიკატი:
invalidTokenRequiredActions=ამ ბმულში მოთხოვნილი ქმედებები არასწორია
internalServerError=აღმოჩენილია სერვერის შიდა შეცდომა
noCertificate=[სერტიფიკატის გარეშე]


pageNotFound=გვერდი ვერ ვიპოვე
openshift.scope.user_full=სრული წვდომა
console-verify-email=საჭიროა, გადაამოწმოთ თქვენი ელფოსტის მისამართი.   გამოგიგზავნეთ ელფოსტა {0}-ზე, რომელიც გადამოწმების კოდს შეიცავს.   შეიყვანეთ ეს კოდი ქვემოთ ველში.
saml.artifactResolutionServiceInvalidResponse=არტეფაქტის გადაწყვეტა შეუძლებელია.
openshift.scope.user_check-access=მომხმარებლის წვდომის ინფორმაცია
openshift.scope.list-projects=პროექტების სია
otp-help-text=შეიყვანეთ გადამოწმების კოდი ავთენტიკატორი აპლიკაციიდან.
saml.post-form.js-disabled=JavaScript გათიშულია. გირჩევთ, ჩართოთ ის. გასაგრძელებლად დააწექით ღილაკს ქვემოთ. 
password-display-name=პაროლი

#authenticators
otp-display-name=ავთენტიკატორი აპლიკაცია
otp-reset-description=რომელი OTP-ის კონფიგურაციის წაშლა გნებავთ?
auth-recovery-authn-code-form-help-text=შეიყვანეთ აღდგენის ავთენტიკაციის სიიდან, რომელიც ადრე გადმოგეცათ.
recovery-code-config-warning-title=ეს ავთენტიკაციის კოდები აღარასდროს გამოჩნდება, როცა ამ გვერდიდან გახვალთ

# Recovery Codes
auth-recovery-authn-code-form-display-name=აღდგენის ავთენტიკაციის კოდი
auth-recovery-code-info-message=შეიყვანეთ მითითებული აღდგენის კოდი.
recovery-codes-error-invalid=არასწორი აღდგენის ავთენტიკაციის კოდი
auth-recovery-code-prompt=აღდგენის კოდი #{0}
auth-recovery-code-header=შესვლა აღდგენის ავთენტიკაციის კოდით
recovery-code-config-header=აღდგენის ავთენტიკაიციის კოდები
recovery-codes-print=დაბეჭდვა
recovery-codes-copy=კოპირება
recovery-codes-download-file-description=აღდგენის კოდები ერთჯერადი საკვანძო კოდებია, რომელიც საშუალებას გაძლევთ, შეხვიდეთ თქვენს ანგარიშში, თუ ავთენტიკატორთან წვდომა დაკარგეთ.
recovery-codes-label-default=აღდგენის კოდები
recovery-codes-download=გადმოწერა

# WebAuthn
webauthn-display-name=Passkey
webauthn-help-text=გამოიყენეთ თქვენი Passkey შესასვლელად.
recovery-codes-copied=დაკოპირდა
recovery-codes-confirmation-message=მე შევინახე კოდები უსაფრთხო ადგილას
webauthn-passwordless-display-name=Passkey
webauthn-passwordless-help-text=გამოიყენეთ თქვენი Passkey უპაროლოდ შესასვლელად.
webauthn-unsupported-browser-text=ამ ბრაუზერს WebAuthn-ის მხარდაჭერა არ გააჩნია. სცადეთ სხვა ბრაუზერი ან დაუკავშირდით თქვენს სისტემურ ადმინისტრატორს.
webauthn-doAuthenticate=Passkey-ით შესვლა
webauthn-registration-init-label=Passkey (ნაგულისხმევი ჭდე)


# WebAuthn Error
webauthn-error-title=Passkey-ის შეცდომა
webauthn-createdAt-label=შეიქმნა
webauthn-registration-init-label-prompt=შეიყვანეთ თქვენი დარეგისტრირებული Passkey-ის ჭდე
webauthn-error-registration=თქვენი Passkey-ის რეგისტრაცია ჩავარდა.<br/>{0}
passkey-unsupported-browser-text=Passkey-ის მხარდაჭერა ამ ბრაუზერს არ აქვს. სცადეთ სხვა ან დაუკავშირდით თქვენს ადმინისტრატორს.
webauthn-error-api-get=Passkey-ით ავთენტიკაცია ჩავარდა.<br/>{0}
passkey-doAuthenticate=Passkey-ით შესვლა
webauthn-error-different-user=პირველი მომხმარებელი, ვინც ავთენტიკაცია გაიარა, Passkey-ით ავთენტიკაციის მომხმარებელს არ ემთხვევა.
passkey-createdAt-label=შეიქმნა
passkey-autofill-select=აირჩიეთ თქვენი Passkey
idp-email-verification-help-text=მიაბით თქვენი ანგარიში თქვენი ანგარიშის დადასტურებით.
idp-username-password-form-display-name=მომხმარებელი და პაროლი

# Identity provider
identity-provider-redirector=დაკავშირება სხვა იდენტიფიკატორის მომწოდებლით
identity-provider-login-label=ან შედით მეთოდით
idp-username-password-form-help-text=მიაბით თქვენი ანგარიში სისტემაში შესვლით.
frontchannel-logout.message=მიმდინარეობს თქვენი გასვლა შემდეგი აპებიდან
logoutConfirmTitle=მიმდინარეობს გასვლა
finalDeletionConfirmation=თუ თქვენს ანგარიშს წაშლით, მას ვეღარ აღადგენთ. ანგარიშის შესანარჩუნებლად დააწკაპუნეთ ღილაკზე 'გაუქმება'.
deleteAccountConfirm=ანგარიშის ინფორმაციის წაშლა
loggingOutImmediately=დაუყოვნებლივ გაგდებას
userDeletedSuccessfully=მომხმარებელი წარმატებით წაიშალა
logoutConfirmHeader=გნებავთ, გახვიდეთ?
frontchannel-logout.title=მიმდინარეობს გასვლა
readOnlyUsernameMessage=თქვენ ვერ განაახლებთ თქვენს მომხმარებლის სახელს, რადგან ის მხოლოდ-წაკითხვადია.
doLogout=გასვლა
error-invalid-multivalued-size=ატრიბუტი {0} მინიმუმ {1} და მაქსიმუმ {2} მნიშვნელობას უნდა შეიცავდეს.
doLogIn=შესვლა
doRegister=რეგისტრაცია
doRegisterSecurityKey=რეგისტრაცია
doCancel=გაუქმება
doSubmit=გადაცემა
doNo=არა
doContinue=გაგრძელება
doDecline=უარყოფა
doForgotPassword=დაგავიწყდათ პაროლი?
doClickHere=დააწკაპუნეთ აქ
doImpersonate=განსახიერება
doTryAnotherWay=სხვანაირად ცდა
doConfirmDelete=წაშლის დადასტურება
kerberosNotConfigured=Kerberos მორგებული არაა
kerberosNotSetUp=Kerberos მორგებული არა.   ვერ შეხვალთ.
registerTitle=რეგისტრაცია
loginAccountTitle=შედით თქვენს ანგარიშში
loginTitle={0}-ში შესვლა
loginTitleHtml={0}
impersonateTitle={0} მომხმარებლის განსახიერება
realmChoice=რეალმი
unknownUser=უცნობი მომხმარებელი
loginProfileTitle=ანგარიშის ინფორმაციის განახლება
reauthenticate=გასაგრძელებლად საჭიროა, ავთენტიკაცია თავიდან გაიაროთ
authenticateStrong=გასაგრძელებლად საჭიროა ძლიერი ავთენტიკაცია
oauthGrantTitle={0}-სთან წვდომის მიცემა
oauthGrantTitleHtml={0}
oauthGrantInformation=დარწმუნდით, რომ ენდობით {0}-ს ნახვით, როგორ დაამუშავებს {0} თქვენს მონაცემებს.
oauthGrantReview=შეგიძლიათ, გადახედოთ 
oauthGrantTos=გამოყენების პირობებს.
oauthGrantPolicy=კონფიდენციალობის პოლიტიკა.
errorTitle=ვწუხვართ...
errorTitleHtml=<strong>ვწუხვართ</strong>...
updateEmailTitle=ელფოსტის განახლება
emailUpdateConfirmationSentTitle=დადასტურების ელფოსტა გაიგზავნა
emailUpdatedTitle=ელფოსტა განახლდა
emailUpdated=ანგარიშის ელფოსტა წარმატებით განახლდა მნიშვნელობაზე {0}.
updatePasswordTitle=პაროლის განახლება
codeSuccessTitle=წარმატების კოდი
displayUnsupported=მოთხოვნილი ჩვენების ტიპი მხარდაჭერილი არაა
browserRequired=შესასვლელად აუცილებელია ბრაუზერი
browserContinue=შესვლის დასასრულებლად აუცილებელია ბრაუზერი
browserContinuePrompt=გავხსნა ბრაუზერი და გავაგრძელო შესვლა? [y/n]:
browserContinueAnswer=y

# Transports
usb=USB
nfc=NFC
bluetooth=ბლუთუზი
internal=შიდა
termsTitle=წესები და პირობები
termsText=<p>აღსაწერი წესები და პირობები</p>
termsPlainText=აღსაწერი ტერმინები და პირობები.
termsAcceptanceRequired=უნდა დაეთანხმოთ ჩვენს ტერმინებს და პირობებს.
deleteCredentialTitle={0}-ის წაშლა
deleteCredentialMessage=გნებავთ, წაშალოთ {0}?
recaptchaFailed=არასწორი Recaptcha
recaptchaNotConfigured=Recaptcha მოთხოვნილია, მაგრამ მორგებული არაა
consentDenied=თანხმობა უარყოფილია.
username=მომხმარებლის სახელი
usernameOrEmail=მომხმარებლის სახელი ან ელფოსტა
familyName=მეტსახელი
email=ელფოსტა
password=პაროლი
passwordConfirm=გაიმეორეთ პაროლი
passwordNew=ახალი პაროლი
passwordNewConfirm=ახალი პაროლი დადასტურება
hidePassword=პაროლი დამალვა
showPassword=პაროლი ჩვენება
rememberMe=დამიმახსოვრე
authenticatorCode=ერთჯერადი კოდი
street=ქუჩა
locality=ქალაქი ან დაბა
country=ქვეყანა
emailVerified=ელფოსტა გადამოწმებულია
website=ვებგვერდი
phoneNumber=ტელეფონის ნომერი
zoneinfo=დროის სარტყელი
logoutOtherSessions=გასვლა სხვა მოწყობილობებიდან
profileScopeConsentText=მომხმარებლის პროფილი
emailScopeConsentText=ელფოსტის მისამართი
loginTotpIntro=ამ ანგარიშთან წვდომისთვის ერთჯერადი პაროლის გენერატორი უნდა მოირგოთ
loginTotpStep2=გახსენით აპლიკაცია და დაასკანირეთ ბარკოდი:
loginTotpStep3=შეიყვანეთ აპლიკაციის მიერ მოწოდებული ერთჯერადი კოდი და დააწკაპუნეთ გადაცემაზე, რომ მორგება დაასრულოთ.
loginTotpManualStep2=გახსენით აპლიკაცია და შეიყვანეთ კოდი:
loginTotpUnableToScan=ვერ დაასკანერეთ?
loginTotpScanBarcode=დავასკანერო ბარკოდი?
loginCredential=ავტორიზაციის დეტალი
loginOtpOneTime=ერთჯერადი კოდი
loginTotpType=ტიპი
loginTotpAlgorithm=ალგორითმი
loginTotpDigits=ციფრები
loginTotpInterval=ინტერვალი
loginTotpCounter=მთვლელი
loginTotpDeviceName=მოწყობილობის სახელი
loginTotp.totp=დროზე-დაფუძნებული
loginTotp.hotp=მთვლელზე-დაფუძნებული
totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
loginChooseAuthenticator=აირჩიეთ შესვლის მეთოდი
oauthGrantRequest=ანიჭებთ ამ წვდომის უფლებებს?
inResource=სად
oauth2DeviceVerificationTitle=მოწყობილობის შესვლა
verifyOAuth2DeviceUserCode=შეიყვანეთ კოდი, რომელიც თქვენმა მოწყობილობამ მოგაწოდათ და დააწკაპუნეთ ღილაკზე 'გადაცემა'
oauth2DeviceInvalidUserCodeMessage=არასწორი კოდი. თავიდან სცადეთ.
oauth2DeviceVerificationCompleteHeader=მოწყობილობის შესვლა წარმატებულია
emailVerifyInstruction1=ელფოსტა ინსტრუქციებით, რომ გადავამოწმოთ თქვენი ელფოსტის მისამართი, გაიგზავნა მისამართზე {0}.
emailVerifyInstruction3=ელფოსტის თავიდან გასაგზავნად.
emailLinkIdpTitle={0}-ის მიბმა
backToLogin=&laquo; შესვლაზე დაბრუნება
copyCodeInstruction=დააკოპირეთ ეს კოდი და ჩასვით თქვენს აპლიკაციაში:
pageExpiredTitle=გვერდის ვადა ამოიწურა
pageExpiredMsg1=შესვლის პროცესის თავიდან დასაწყებად
personalInfo=პერსონალური ინფორმაცია:
role_admin=ადმინი
role_realm-admin=რეალმის ადმინი
client_admin-cli=ადმინის CLI
client_realm-management=რეალმის მართვა
requiredFields=აუცილებელი ველები
client_broker=ბროკერი
invalidUsernameMessage=არასწორი მომხმარებლის სახელი.
invalidUsernameOrEmailMessage=არასწორი მომხმარებლის სახელი ან ელფოსტა.
invalidPasswordMessage=არასწორი პაროლი.
invalidEmailMessage=არასწორი ელფოსტის მისამართი.
accountDisabledMessage=ანგარიში გათიშულია. დაუკავშირდით თქვენს ადმინისტრატორს.
expiredCodeMessage=შესვლის მოლოდინის ვადა ამოიწურა. თავიდან შედით.
sessionLimitExceeded=არსებობს მეტისმეტად ბევრი სესია
identityProviderLogoutFailure=SAML IdP გასვლა ჩავარდა
missingFirstNameMessage=მიუთითეთ სახელი.
missingLastNameMessage=მიუთითეთ გვარი.
missingEmailMessage=მიუთითეთ ელფოსტა.
missingUsernameMessage=მიუთითეთ მომხმარებლის სახელი.
missingPasswordMessage=მიუთითეთ პაროლი.
missingTotpMessage=მიუთითეთ ავთენტიკატორის კოდი.
error-invalid-length-too-short=მინიმალური სიგრძეა {1}.
error-invalid-length-too-long=მაქსიმალური სიგრძეა {2}.
error-invalid-email=არასწორი ელფოსტის მისამართი.
error-invalid-number=არასწორი რიცხვი.
error-number-out-of-range=რიცხვი უნდა იყოს შუალედიდან {1}-{2}.
error-number-out-of-range-too-big=რიცხვის მაქსიმალური მნიშვნელობაა {2}.
error-number-out-of-range-too-small=რიცხვის მინიმალური მნიშვნელობაა {1}.
error-pattern-no-match=არასწორი მნიშვნელობა.
error-invalid-uri=არასწორი URL.
error-invalid-uri-scheme=არასწორი ბმულის სქემა.
error-invalid-uri-fragment=არასწორი URL-ის ფრაგმენტი.
error-user-attribute-required=მიუთითეთ ეს ველი.
error-invalid-date=არასწორი თარიღი.
error-user-attribute-read-only=ეს ველი მხოლოდ-წაკითხვადია.
error-username-invalid-character=მნიშვნელობა არასწორ სიმბოლოს შეიცავს.
error-person-name-invalid-character=მნიშვნელობა არასწორ სიმბოლოს შეიცავს.
error-reset-otp-missing-id=აირჩიეთ OTP-ის კონფიგურაცია.
invalidPasswordExistingMessage=არასწორი არსებული პაროლი.
invalidPasswordBlacklistedMessage=არასწორი პაროლი: პაროლი შავ სიაშია.
invalidPasswordConfirmMessage=პაროლის დადასტურება არ ემთხვევა.
invalidTotpMessage=არასწორი ავთენტიკატორის კოდი.
usernameExistsMessage=მომხმარებლის სახელი უკვე არსებობს.
emailExistsMessage=ელფოსტა უკვე არსებობს.
federatedIdentityExistsMessage=მომხმარებელი {0}-ით {1} უკვე არსებობს. შედით ანგარიშების მართვაში, რომ ანგარიში მიაბათ.
federatedIdentityUnavailableMessage=მომხმარებელმა {0} ავთენტიკაცია იდენტიფიკატორის მომწოდებლით {1} გაიარა და არ არსებობს. დაუკავშირდით თქვენს ადმინისტრატორ.
federatedIdentityUnmatchedEssentialClaimMessage=ID-ის კოდი, რომელიც იდენტიფიკატორის მომწოდებელმა გამოსცა, არ ემთხვევა მორგებულ ძირითად მოთხოვნას. დაუკავშირდით თქვენს ადმინისტრატორს.
federatedIdentityConfirmLinkMessage=მომხმარებელი {0}-ით {1} უკვე არსებობს. როგორ გნებავთ გაგრძელება?
federatedIdentityConfirmOverrideMessage=ცდილობთ, თქვენი ანგარიში {0} {1}-ით ანგარიშს {2}. მაგრამ თქვენი ანგარიში უკვე მიბმულია სხვა {3} ანგარიშს {4}. ადასტურებთ, რომ გნებავთ, არსებული ბმული ახალი ანგარიშით ჩაანაცვლოთ?
federatedIdentityConfirmReauthenticateMessage=გაიარეთ ავთენტიკაცია, რომ მიაბათ თქვენი ანგარიში {0}-ს
configureTotpMessage=თქვენი ანგარიშის გასააქტიურებლად მობილური ავთენტიკატორი გჭირდებათ.
configureBackupCodesMessage=თქვენი ანგარიშის გასააქტიურებლად მარქაფი კოდები უნდა მოირგოთ.
updateProfileMessage=თქვენი ანგარიშის გასააქტიურებლად თქვენი მომხმარებლის პროფილი უნდა განაახლოთ.
updateEmailMessage=თქვენი ანგარიშის გასააქტიურებლად ელფოსტის ანგარიშის განახლება გჭირდებათ.
resetPasswordMessage=საჭიროა, შეცვალოთ თქვენი პაროლი.
verifyEmailMessage=საჭიროა, გადაამოწმოთ თქვენი ელფოსტის მისამართი, რომ გაააქტიუროთ თქვენი ანგარიში.
emailSentMessage=მალე შემდგომი ინსტრუქციების შემცველ ელფოსტას მიიღებთ.
emailSendErrorMessage=ელფოსტის გაგზავნა ჩავარდა. მოგვიანებით სცადეთ.
accountUpdatedMessage=თქვენი ანგარიში განახლდა.
accountPasswordUpdatedMessage=თქვენი პაროლი განახლდა.
delegationCompleteHeader=შესვლა წარმატებულია
delegationCompleteMessage=შეგიძლიათ დახუროთ ეს ბრაუზერის ფანჯარა და თქვენი კონსოლის აპლიკაციაზე დაბრუნდეთ.
delegationFailedHeader=შესვლა ჩავარდა
delegationFailedMessage=შეგიძლიათ, დახუროთ ეს ბრაუზერის ფანჯარა და თქვენი კონსოლის აპლიკაციაზე დაბრუნდეთ და შესვლა თავიდან სცადოთ.
noAccessMessage=წვდომის გარეშე
invalidPasswordMinDigitsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} ციფრს.
invalidPasswordMinLowerCaseCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} პატარა ასოს.
invalidPasswordMinUpperCaseCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} დიდ ასოს.
invalidPasswordMinSpecialCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} სპეციალურ სიმბოლოს.
invalidPasswordNotUsernameMessage=არასწორი პაროლი: არ უნდა იყოს მომხმარებლის სახელის ტოლი.
invalidPasswordNotContainsUsernameMessage=არასწორი პაროლი: არ შეიძლება, მომხმარებლის სახელს შეიცავდეს.
invalidPasswordNotEmailMessage=არასწორი პაროლი: არ უნდა უდრიდეს ელფოსტას.
invalidPasswordRegexPatternMessage=არასწორი პაროლი: არ ემთხვევა რეგგამოსის ნიმუშ(ებ)-ს.
invalidPasswordHistoryMessage=არასწორი პაროლი: არ უნდა უდრიდეს ბოლო {0} პაროლს.
invalidPasswordGenericMessage=არასწორი პაროლი: ახალი პაროლი არ აკმაყოფილებს პაროლის პოლიტიკებს.
failedToProcessResponseMessage=პასუხის დამუშავება ჩავარდა
httpsRequiredMessage=HTTPS აუცილებელია
realmNotEnabledMessage=რეალმი ჩართული არაა
invalidRequestMessage=არასწორი მოთხოვნა
failedLogout=გასვლა ჩავარდა
loginRequesterNotEnabledMessage=შესვლის მომთხოვნი ჩართული არაა
implicitFlowDisabledMessage=კლიენტს უფლება, რომ დაიწყოს ბრაუზერით შესვლა მითითებული პასუხის ტიპით, არ აქვს. კლიენტისთვის პირდაპირი დინება გათიშულია.
invalidRedirectUriMessage=არასწორი გადამისამართების uri
unsupportedNameIdFormatMessage=მხარდაუჭერელი NameIDFormat
noRelayStateInResponseMessage=იდენტიფიკატორის მომწოდებლის პასუხში რელეს მდგომარეობა აღმოჩენილი არაა.
insufficientPermissionMessage=არასაკმარისი წვდომები იდენტიფიკატორების გადასაბმელად.
couldNotProceedWithAuthenticationRequestMessage=იდენტიფიკატორის მომწოდებელთან ავთენტიკაციის მოთხოვნის გაგრძელება შეუძლებელია.
couldNotObtainTokenMessage=იდენტიფიკატორის მომწოდებლიდან კოდის მიღება შეუძლებელია.
unexpectedErrorRetrievingTokenMessage=მოულოდნელი შეცდომა იდენტიფიკატორის მომწოდებლიდან კოდის მიღებისას.
unexpectedErrorHandlingResponseMessage=მოულოდნელი შეცდომა იდენტიფიკატორის მომწოდებლის პასუხის დამუშავებისას.
identityProviderAuthenticationFailedMessage=ავთენტიკაცია ჩავარდა. იდენტიფიკატორის მომწოდებელთან ავთენტიკაცია ჩავარდა.
couldNotSendAuthenticationRequestMessage=ვერ გავაგზავნე ავთენტიკაციის მოთხოვნა იდენტიფიკატორის მომწოდებელთან.
invalidAccessCodeMessage=არასწორი წვდომის კოდი.
sessionNotActiveMessage=სესია აქტიური არაა.
invalidCodeMessage=აღმოჩენილია შეცდომა. შედით თავიდან თქვენი აპლიკაციით.
cookieNotFoundMessage=ქუქი ვერ ვიპოვე. დარწმუნდით, რომ ბრაუზერში ქუქიები ჩართული გაქვთ.
insufficientLevelOfAuthentication=ავთენტიკაციის მოთხოვნილი დონე არ დაკმაყოფილებულა.
identityProviderUnexpectedErrorMessage=მოულოდნელი შეცდომა იდენტიფიკატორის მომწოდებლით ავთენტიკაციისას
identityProviderMissingStateMessage=იდენტიფიკატორის მომწოდებლის პასუხში მდგომარეობის პარამეტრი მითითებული არაა.
identityProviderMissingCodeOrErrorMessage=ნაკლული კოდი ან შეცდომის პარამეტრი იდენტიფიკატორის მომწოდებლის პასუხში.
identityProviderLinkSuccess=თქვენ წარმატებით გადაამოწმეთ თქვენი ელფოსტა. დაბრუნდით საწყის ბრაუზერში და გააგრძელეთ შესვლა.
missingParameterMessage=ნაკლული პარამეტრები: {0}
clientNotFoundMessage=კლიენტი ვერ ვიპოვე.
clientDisabledMessage=კლიენტი გათიშულია.
invalidParameterMessage=არასწორი პარამეტრი: {0}
alreadyLoggedIn=უკვე შესული ბრძანდებით.
proceedWithAction=&raquo; დააწკაპუნეთ აქ გასაგრძელებლად
requiredAction.UPDATE_PASSWORD=პაროლის განახლება
requiredAction.UPDATE_PROFILE=პროფილის განახლება
requiredAction.VERIFY_EMAIL=ელფოსტის გადამოწმება
doX509Login=შეხვალთ, როგორც:
console-username=მომხმარებლის სახელი:
console-password=პაროლი:
console-otp=ერთჯერადი პაროლი:
console-new-password=ახალი პაროლი:
console-confirm-password=დაადასტურეთ პაროლი:
console-update-password=აუცილებელია თქვენი პაროლის განახლება.
console-email-code=ელფოსტის კოდი:
console-accept-terms=ეთანხმებით წესებს? [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=მომხმარებლის ინფორმაცია

# SAML authentication
saml.post-form.title=ავთენტიკაციის გადამისამართება
saml.post-form.message=მიმდინარეობს გადამისამართება. გთხოვთ, მოითმინოთ.
password-help-text=შესვლა პაროლის შეყვანით.
auth-username-form-display-name=მომხმარებლის სახელი
auth-username-form-help-text=დაიწყეთ შესვლა თქვენი მომხმარებლის სახელის შეყვანით
auth-username-password-form-display-name=მომხმარებელი და პაროლი
auth-username-password-form-help-text=შედით თქვენი მომხმარებლისა და პაროლის შეყვანით.
recovery-code-config-warning-message=დარწმუნდით, რომ დაბეჭდოთ, გადმოწეროთ ან შეინახოთ ისინი პაროლების მმართველში და იქონიეთ ისინი უსაფრთხოდ. ამ პარამეტრების გაუქმება თქვენი ანგარიშიდან ამ კოდებს წაშლის.
recovery-codes-action-complete=მორგების დასრულება
recovery-codes-action-cancel=მორგების გაუქმება
recovery-codes-download-file-header=შეინახეთ ეს კოდები სადმე უსაფრთხო ადგილას.
recovery-codes-download-file-date=ეს კოდები დააგენერირა
webauthn-login-title=Passkey-ით შესვლა
webauthn-registration-title=Passkey-ისის რეგისტრაცია
webauthn-available-authenticators=ხელმისაწვდომი Passkeys
webauthn-error-auth-verification=Passkey-ით ავთენტიკაციის შედეგი არასწორია.<br/>{0}
webauthn-error-register-verification=Passkey-ის რეგისტრაციის შედეგი არასწორია.<br/>{0}
webauthn-error-user-not-found=Passkey-ით ავთენტიკაციაგავლილი მომხმარებელი უცნობია.
irreversibleAction=ეს ქმედება შეუქცევადია
accountUnusable=ამ ანგარიშით ამის შემდეგ აპლიკაციას ვერ გამოიყენებთ

# Passkey
passkey-login-title=Passkey-ით შესვლა
passkey-available-authenticators=ხელმისაწვდომი Passkeys
idp-email-verification-display-name=ელფოსტის გადამოწმება
deletingImplies=თქვენი ანგარიშის წაშლა გამოიწვევს:
errasingData=თქვენი ყველა მონაცემის წაშლას
access-denied=წვდომა აკრძალულია
access-denied-when-idp-auth=წვდომა აკრძალულია {0}-ით ავთენტიკაციისას
doAccept=მიღება
doTryAgain=თავიდან ცდა
errorDeletingAccount=შეცდომა ანგარიშის წაშლისას
kerberosNotConfiguredTitle=Kerberos მორგებული არაა
doBack=უკან
doYes=დიახ
doIgnore=გამოტოვება
impersonateTitleHtml=<strong>{0}</strong> მომხმარებლის განსახიერება
loginTimeout=თქვენი შესვლის მცდელობის მოლოდინის ვადა ამოიწურა.    შესვლა თავიდან დაიწყება.
deletingAccountForbidden=თქვენ არ გაქვთ საკმარისი წვდომები, რომ საკუთარი ანგარიში წაშალოთ. დაუკავშირდით ადმინისტრატორს.
bypassKerberosDetail=ან შესული არ ბრძანდებით Kerberos-ით, ან თქვენი ბრაუზერი Kerberos-ით შესვლაზე არაა მორგებული.   გააგრძელეთ შესვლა, ოღონდ სხვა საშუალებებით
loginTotpTitle=მობილური ავთენტიკატორის მორგება
loginIdpReviewProfileTitle=ანგარიშის ინფორმაციის განახლება
organization.confirm-membership.title=აპირებთ, შეუერთდეთ ორგანიზაციას ${kc.org.name}
organization.confirm-membership=ქვემოთ ბმულზე დაწკაპუნებით თქვენ შეუერთდებით ორგანიზაციას {0}:
organization.member.register.title=შექმენით ანგარიში, რომ შეუერთდეთ ორგანიზაციას ${kc.org.name}
auth-x509-client-username-form-display-name=X509 სერტიფიკატი
auth-x509-client-username-form-help-text=შესვლა X509 კლიენტის სერტიფიკატით.
organization.select=აირჩიეთ ორგანიზაცია გასაგრძელებლად:
requiredAction.webauthn-register=Webauthn-ის რეგისტრაცია
