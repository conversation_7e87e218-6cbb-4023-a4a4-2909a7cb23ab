doSave=Salvar
doCancel=Cancelar
doLogOutAllSessions=Sair <PERSON> to<PERSON> as sessões
doRemove=Remover
doAdd=Adicionar
doSignOut=Sair
doLogIn=Entrar
doLink=Vincular
noAccessMessage=Acesso não permitido


editAccountHtmlTitle=Editar Conta
personalInfoHtmlTitle=Informações Pessoais
federatedIdentitiesHtmlTitle=Identidades Federadas
accountLogHtmlTitle=Histórico da conta
changePasswordHtmlTitle=Alterar senha
deviceActivityHtmlTitle=Atividade de Dispositivos
sessionsHtmlTitle=Sessões
accountManagementTitle=Gerenciamento de Conta
authenticatorTitle=Autenticator
applicationsHtmlTitle=Aplicativos
linkedAccountsHtmlTitle=Contas Vinculadas

accountManagementWelcomeMessage=Bem-vindo ao Gerenciamento de Conta
personalInfoIntroMessage=Gerenciar informações básicas
accountSecurityTitle=Segurança da Conta
accountSecurityIntroMessage=Gerencie sua senha e acesso da conta
applicationsIntroMessage=Acompanhe e gerencie as permissões de app para acesso à sua conta
resourceIntroMessage=Compartilhe seus recursos com membros de equipe
passwordLastUpdateMessage=Sua senha foi atualizada em
updatePasswordTitle=Atualizar Senha
updatePasswordMessageTitle=Certifique-se de que a nova senha é segura
updatePasswordMessage=Uma senha segura contém uma combinação de número, letras e caracteres especiais. Ela deve ser difícil de adivinhar, não pode se assemelhar a uma palavra real e não é utilizada em outros lugares.
personalSubTitle=Suas Informações Pessoais
personalSubMessage=Gerencie as informações básicas: seu primeiro nome, seu sobrenome e seu endereço de e-mail

authenticatorCode=Código autenticador
email=E-mail
firstName=Primeiro nome
givenName=Primeiro nome
fullName=Nome completo
lastName=Sobrenome
familyName=Sobrenome
password=Senha
currentPassword=Senha Atual
passwordConfirm=Confirmação
passwordNew=Nova senha
username=Nome de usuário
address=Endereço
street=Logradouro
locality=Cidade ou Localidade
region=Estado
postal_code=CEP
country=País
emailVerified=E-mail verificado
website=Página da web
phoneNumber=Número de telefone
phoneNumberVerified=Número de telefone verificado
gender=Gênero
birthday=Data de nascimento
zoneinfo=Zona horária
gssDelegationCredential=Delegação de Credenciais GSS

profileScopeConsentText=Perfil de usuário
emailScopeConsentText=Endereço de e-mail
addressScopeConsentText=Endereço
phoneScopeConsentText=Número de telefone
offlineAccessScopeConsentText=Acesso Offline
samlRoleListScopeConsentText=Meus Perfis de Acesso
rolesScopeConsentText=Perfis de acesso de usuário

role_admin=Administrador
role_realm-admin=Administrador de domínio
role_create-realm=Criar domínio
role_view-realm=Visualizar domínio
role_view-users=Visualizar usuários
role_view-applications=Visualizar aplicativos
role_view-clients=Visualizar clientes
role_view-events=Visualizar eventos
role_view-identity-providers=Visualizar provedores de identidade
role_view-consent=Visualizar consentimentos
role_manage-realm=Gerenciar domínio
role_manage-users=Gerenciar usuários
role_manage-applications=Gerenciar aplicativos
role_manage-identity-providers=Gerenciar provedores de identidade
role_manage-clients=Gerenciar clientes
role_manage-events=Gerenciar eventos
role_view-profile=Visualizar perfil
role_manage-account=Gerenciar conta
role_manage-account-links=Gerenciar vinculações de conta
role_manage-consent=Gerenciar consentimentos
role_read-token=Ler token
role_offline-access=Acesso offline
role_uma_authorization=Obter permissões
client_account=Conta
client_account-console=Console de Conta
client_security-admin-console=Console de Administração de Segurança
client_admin-cli=CLI de Administração
client_realm-management=Gerenciamento de Domínio
client_broker=Provedor de Identidade


requiredFields=Campos obrigatórios
allFieldsRequired=Todos os campos são obrigatórios

backToApplication=&laquo; Voltar para aplicação
backTo=Voltar para {0}

date=Data
event=Evento
ip=IP
client=Cliente
clients=Clientes
details=Detalhes
started=Início em
lastAccess=Último acesso
expires=Expira em
applications=Aplicativos

account=Conta
federatedIdentity=Identidade Federada
authenticator=Autenticador
device-activity=Atividade de Dispositivos
sessions=Sessões
log=Histórico

application=Aplicativo
availableRoles=Perfis de Acesso Disponíveis
grantedPermissions=Permissões Concedidas
grantedPersonalInfo=Informações Pessoais Concedidas
additionalGrants=Concessões Adicionais
action=Ação
inResource=em
fullAccess=Acesso Completo
offlineToken=Token Offline
revoke=Revogar Concessão

configureAuthenticators=Autenticadores Configurados
mobile=Móvel
totpStep1=Instale uma das seguintes aplicações no seu celular:
totpStep2=Abra a aplicação e escaneie o código QR:
totpStep3=Insira o código de uso único exibido pela aplicação e clique em Salvar para finalizar a configuração.
totpStep3DeviceName=Forneça um nome de dispositivo para ajudá-lo a gerenciar seus dipositivos de autenticação de dois fatores.

totpManualStep2=Abra a aplicação e insira a chave:
totpManualStep3=Use as seguintes configurações se a aplicação permitir:
totpUnableToScan=Não consegue escanear?
totpScanBarcode=Escanear código QR?

totp.totp=Baseada em tempo
totp.hotp=Baseada em contador

totpType=Tipo
totpAlgorithm=Algoritmo
totpDigits=Dígitos
totpInterval=Intervalo
totpCounter=Contador
totpDeviceName=Nome do Dispositivo

irreversibleAction=Esta ação é irreversível
deletingImplies=Apagar a sua conta implica em:
errasingData=Remover todos os dados
loggingOutImmediately=Finalizar a sessão imediatamente
accountUnusable=Qualquer uso subsquente da aplicação não será mais possível com esta conta

missingUsernameMessage=Por favor, especifique o nome de usuário.
missingFirstNameMessage=Por favor, informe o primeiro nome.
invalidEmailMessage=E-mail inválido.
missingLastNameMessage=Por favor, informe o sobrenome.
missingEmailMessage=Por favor, informe o e-mail.
missingPasswordMessage=Por favor, informe a senha.
notMatchPasswordMessage=As senhas não coincidem.
invalidUserMessage=Usuário inválido
updateReadOnlyAttributesRejectedMessage=Atualização de atributo de apenas leitura não permitida

missingTotpMessage=Por favor, informe o código de uso único.
missingTotpDeviceNameMessage=Por favor, informe o nome do dispositivo.
invalidPasswordExistingMessage=A senha atual é inválida.
invalidPasswordConfirmMessage=A senha de confirmação não coincide.
invalidTotpMessage=Código de uso único inválido.

usernameExistsMessage=Este nome de usuário já existe.
emailExistsMessage=Este endereço de e-mail já existe.

readOnlyUserMessage=Você não pode atualizar sua conta, uma vez que é apenas de leitura.
readOnlyUsernameMessage=Você não pode atualizar o seu nome de usuário, uma vez que é apenas de leitura.
readOnlyPasswordMessage=Você não pode atualizar sua senha, uma vez que sua conta é apenas de leitura.

successTotpMessage=Autenticador móvel configurado.
successTotpRemovedMessage=Autenticador móvel removido.

successGrantRevokedMessage=Concessão revogada com sucesso.

accountUpdatedMessage=Sua conta foi atualizada.
accountPasswordUpdatedMessage=Sua senha foi atualizada.

missingIdentityProviderMessage=Provedor de identidade não especificado.
invalidFederatedIdentityActionMessage=Ação inválida ou ausente.
identityProviderNotFoundMessage=O provedor de identidade especificado não foi encontrado.
federatedIdentityLinkNotActiveMessage=Esta identidade não está mais em atividade.
federatedIdentityRemovingLastProviderMessage=Você não pode remover a última identidade federada, porque você não tem uma senha.
identityProviderRedirectErrorMessage=Falha ao redirecionar para o provedor de identidade.
identityProviderRemovedMessage=Provedor de identidade removido com sucesso.
identityProviderAlreadyLinkedMessage=Identidade federada retornada por {0} já está ligada a outro usuário.
staleCodeAccountMessage=A página expirou. Por favor, tente novamente.
consentDenied=Consentimento negado.

accountDisabledMessage=Conta desativada, por favor, contate um administrador.

accountTemporarilyDisabledMessage=A conta está temporariamente indisponível, contate um administrador ou tente novamente mais tarde.
invalidPasswordMinLengthMessage=Senha inválida\: deve ter pelo menos {0} caracteres.
invalidPasswordMinLowerCaseCharsMessage=Senha inválida\: deve conter pelo menos {0} letra(s) minúscula(s).
invalidPasswordMinDigitsMessage=Senha inválida\: deve conter pelo menos {0} número(s).
invalidPasswordMinUpperCaseCharsMessage=Senha inválida\: deve conter pelo menos {0} letra(s) maiúscula(s).
invalidPasswordMinSpecialCharsMessage=Senha inválida\: deve conter pelo menos {0} caractere(s) especial(is).
invalidPasswordNotUsernameMessage=Senha inválida\: não pode ser igual ao nome de usuário.
invalidPasswordNotContainsUsernameMessage=Senha inválida\: não pode conter o nome de usuário.
invalidPasswordNotEmailMessage=Senha inválida: não pode ser igual ao endereço de e-mail.
invalidPasswordRegexPatternMessage=Senha inválida\: não corresponde ao(s) padrão(ões) da expressão regular.
invalidPasswordHistoryMessage=Senha inválida\: não pode ser igual a qualquer uma da(s) última(s) {0} senha(s).
invalidPasswordBlacklistedMessage=Senha inválida: esta senha está na lista de exclusão.
invalidPasswordGenericMessage=Senha inválida: a nova senha não cumpre as políticas de senha.

# Authorization
myResources=Meus Recursos
myResourcesSub=Meus recursos
doDeny=Negar
doRevoke=Revogar
doApprove=Permitir
doRemoveSharing=Remover Compartilhamento
doRemoveRequest=Remover Solicitação
peopleAccessResource=Pessoas com acesso a este recurso
resourceManagedPolicies=Permissões dando acesso a este recurso
resourceNoPermissionsGrantingAccess=Sem permissões dando acesso a este recurso
anyAction=Qualquer ação
description=Descrição
name=Nome
scopes=Escopo
resource=Recurso
user=Usuário
peopleSharingThisResource=Pessoas compartilhando este recurso
shareWithOthers=Compartilhar com outros
needMyApproval=Requer minha aprovação
requestsWaitingApproval=Solicitações suas aguardando aprovação
icon=Ícone
requestor=Requerente
owner=Dono
resourcesSharedWithMe=Recursos compartilhados comigo
permissionRequestion=Solicitação de Permissão
permission=Permissão
shares=compartilha(m)
notBeingShared=Este recurso não está sendo compartilhado.
notHaveAnyResource=Você não possui recursos
noResourcesSharedWithYou=Não há recursos compartilhados com você
havePermissionRequestsWaitingForApproval=Você tem {0} solicitação(ões) de permissão aguardando aprovação.
clickHereForDetails=Clique aqui para mais detalhes.
resourceIsNotBeingShared=O recurso não é compartilhado

# Applications
applicationName=Nome
applicationType=Tipo de aplicação
applicationInUse=Uso apenas em aplicação
clearAllFilter=Limpar todos os filtros
activeFilters=Filtros ativos
filterByName=Filtrar Por Nome ...
allApps=Todas as aplicações
internalApps=Aplicações internas
thirdpartyApps=Aplicações de terceiros
appResults=Resultados
clientNotFoundMessage=Cliente não encontrado.

# Linked account
authorizedProvider=Provedor Autorizado
authorizedProviderMessage=Provedores Autorizados vinculados à sua conta
identityProvider=Provedor de Identidade
identityProviderMessage=Para vincular a sua conta aos provedores de identidade configurados
socialLogin=Login Social
userDefined=Definido por Usuário
removeAccess=Remover Acesso
removeAccessMessage=Você deverá conceder acesso novamente se quiser usar esta conta de app.

#Authenticator
authenticatorStatusMessage=A autenticação de dois fatores está
authenticatorFinishSetUpTitle=Sua Autenticação de Dois Fatores
authenticatorFinishSetUpMessage=Sempre que entrar na sua conta, você deverá fornecer um código de autenticação de dois fatores.
authenticatorSubTitle=Configurar Autenticação de Dois Fatores
authenticatorSubMessage=Para aumentar a segurança da sua conta, habilite pelo menos um método de autenticação de dois fatores disponível.
authenticatorMobileTitle=Autenticador Móvel
authenticatorMobileMessage=Use um autenticador móvel para obter códigos de verificação para autenticação de dois fatores.
authenticatorMobileFinishSetUpMessage=O autenticador foi vinculado ao seu celular.
authenticatorActionSetup=Configurar
authenticatorSMSTitle=Código SMS
authenticatorSMSMessage=A aplicação irá enviar o código de verificação para o seu celular como autenticação de dois fatores.
authenticatorSMSFinishSetUpMessage=As mensagens de texto serão enviadas para
authenticatorDefaultStatus=Padrão
authenticatorChangePhone=Mudar Número de Celular

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=Configuração do Autenticador Móvel
smscodeIntroMessage=Insira seu número de celular e o código de verificação será enviado para o seu dispositivo.
mobileSetupStep1=Instale um app autenticador no seu celular. As seguintes aplicações são suportadas.
mobileSetupStep2=Abra a aplicação e escaneie o código QR:
mobileSetupStep3=Insira o código autenticador exibido pela aplicação e clique em Salvar para finalizar a configuração.
scanBarCode=Escanear código QR?
enterBarCode=Insira o código autenticador
doCopy=Copiar
doFinish=Finalizar

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=Configuração de Código SMS
chooseYourCountry=Selecione seu país
enterYourPhoneNumber=Insira seu número de telefone
sendVerficationCode=Enviar Código de Verificação
enterYourVerficationCode=Insira o seu código de verificação

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=Configuração de Códigos de Emergência
realmName=Domínio
doDownload=Baixar
doPrint=Imprimir
generateNewBackupCodes=Gerar Novos Códigos de Emergência
backtoAuthenticatorPage=Voltar à Página de Autenticador


#Resources
resources=Recursos
sharedwithMe=Compartilhados Comigo
share=Compartilhar
sharedwith=Compartilhado com
accessPermissions=Permissões de Acesso
permissionRequests=Pedidos de Acesso
approve=Aprovar
approveAll=Aprovar todos
people=pessoas
perPage=por página
currentPage=Página Atual
sharetheResource=Compartilhar recurso
group=Grupo
selectPermission=Selecionar Permissão
addPeople=Adicionar pessoas que compartilhem o recurso
addTeam=Adicionar equipe que compartilhe o recurso
myPermissions=Minhas Permissões
waitingforApproval=Aguardando aprovação
anyPermission=Qualquer Permissão

# Openshift messages
openshift.scope.user_info=Informações do usuário
openshift.scope.user_check-access=Informações de acesso do usuário
openshift.scope.user_full=Acesso Completo
openshift.scope.list-projects=Listar projetos
