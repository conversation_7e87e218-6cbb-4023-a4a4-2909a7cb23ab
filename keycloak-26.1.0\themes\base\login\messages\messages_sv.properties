doLogIn=Logga in
doRegister=Registrera
doRegisterSecurityKey=Registrera
doCancel=Avbryt
doSubmit=Skicka
doYes=Ja
doNo=Nej
doContinue=Fortsätt
doAccept=Acceptera
doDecline=Avböj
doForgotPassword=Glömt lösenord?
doClickHere=Klicka här
doImpersonate=Imitera
kerberosNotConfigured=Kerberos är inte konfigurerat
kerberosNotConfiguredTitle=Kerberos är inte konfigurerat
bypassKerberosDetail=Antingen så är du inte inloggad via Kerberos eller så är inte din webbläsare inställd för Kerberosinloggning. Vänligen klicka på fortsätt för att logga in på annat sätt.
kerberosNotSetUp=Kerberos är inte inställt. Du kan inte logga in.
registerWithTitle=Registrera med {0}
registerWithTitleHtml={0}
loginTitle=Logga in till {0}
loginTitleHtml={0}
impersonateTitle={0} Imitera användare
impersonateTitleHtml=<strong>{0}</strong> Imitera användare
realmChoice=Realm
unknownUser=Okänd användare
loginTotpTitle=Inställning av mobilautentiserare
loginProfileTitle=Uppdatera kontoinformation
loginTimeout=Det tog för lång tid att logga in. Inloggningsprocessen börjar om.
oauthGrantTitle=Bevilja åtkomst
oauthGrantTitleHtml={0}
errorTitle=Vi ber om ursäkt...
errorTitleHtml=Vi ber om <strong>ursäkt</strong> ...
emailVerifyTitle=E-postverifiering
emailForgotTitle=Glömt ditt lösenord?
updatePasswordTitle=Uppdatera lösenord
codeSuccessTitle=Rätt kod
codeErrorTitle=Felkod\: {0}

termsTitle=Användarvillkor
termsTitleHtml=Användarvillkor
termsText=<p>Användarvillkoren har ännu inte definierats</p>

recaptchaFailed=Ogiltig Recaptcha
recaptchaNotConfigured=Recaptcha krävs, men är inte inställd
consentDenied=Samtycke förnekat.

noAccount=Ny användare?
username=Användarnamn
usernameOrEmail=Användarnamn eller e-post
firstName=Förnamn
lastName=Efternamn
email=E-post
password=Lösenord
passwordConfirm=Bekräfta lösenord
passwordNew=Nytt lösenord
passwordNewConfirm=Bekräftelse av nytt lösenord
rememberMe=Kom ihåg mig
authenticatorCode=Engångskod
address=Adress
street=Gata
locality=Postort
region=Stat, Provins eller Region
postal_code=Postnummer
country=Land
emailVerified=E-post verifierad
gssDelegationCredential=GSS Delegation Credential

loginTotpStep1=Installera <a href="https://freeotp.github.io/" target="_blank">FreeOTP</a> eller Google Authenticator på din mobil. Båda applikationerna finns tillgängliga hos <a href="https://play.google.com">Google Play</a> och Apple App Store.
loginTotpStep2=Öppna applikationen och skanna streckkoden eller skriv i nyckeln
loginTotpStep3=Fyll i engångskoden som tillhandahålls av applikationen och klicka på Spara för att avsluta inställningarna
loginOtpOneTime=Engångskod

oauthGrantRequest=Godkänner du tillgång till de här rättigheterna?
inResource=i

emailVerifyInstruction1=Ett e-postmeddelande med instruktioner om hur du verifierar din e-postadress har skickats till dig.
emailVerifyInstruction2=Har du inte fått en verifikationskod i din e-post?
emailVerifyInstruction3=för att skicka e-postmeddelandet igen.

emailLinkIdpTitle=Länkning {0}
emailLinkIdp1=Ett e-postmeddelande med instruktioner om hur du länkar {0} kontot {1} med ditt {2} konto har skickats till dig.
emailLinkIdp2=Har du inte fått en verifikationskod i din e-post?
emailLinkIdp3=för att skicka e-postmeddelandet igen.

backToLogin=&laquo; Tillbaka till inloggningen

emailInstruction=Fyll i ditt användarnamn eller din e-postadress, så kommer vi att skicka instruktioner för hur du skapar ett nytt lösenord.

copyCodeInstruction=Vänligen kopiera den här koden och klistra in den i din applikation:

personalInfo=Personlig information:
role_admin=Administratör
role_realm-admin=Realm-administratör
role_create-realm=Skapa realm
role_create-client=Skapa klient
role_view-realm=Visa realm
role_view-users=Visa användare
role_view-applications=Visa applikationer
role_view-clients=Visa klienter
role_view-events=Visa event
role_view-identity-providers=Visa identitetsleverantörer
role_manage-realm=Hantera realm
role_manage-users=Hantera användare
role_manage-applications=Hantera applikationer
role_manage-identity-providers=Hantera identitetsleverantörer
role_manage-clients=Hantera klienter
role_manage-events=Hantera event
role_view-profile=Visa profil
role_manage-account=Hantera konto
role_read-token=Läs element
role_offline-access=Åtkomst offline
client_account=Konto
client_security-admin-console=Säkerhetsadministratörskonsol
client_admin-cli=Administratörs-CLI
client_realm-management=Realmhantering

invalidUserMessage=Ogiltigt användarnamn eller lösenord.
invalidEmailMessage=Ogiltig e-postadress.
accountDisabledMessage=Kontot är inaktiverat, kontakta administratör.
accountTemporarilyDisabledMessage=Ogiltigt användarnamn eller lösenord.
accountPermanentlyDisabledMessage=Ogiltigt användarnamn eller lösenord.
accountTemporarilyDisabledMessageTotp=Autentiseringskoden är ogiltig.
accountPermanentlyDisabledMessageTotp=Autentiseringskoden är ogiltig.
expiredCodeMessage=Inloggningen nådde en maxtidsgräns. Vänligen försök igen.

missingFirstNameMessage=Vänligen ange förnamn.
missingLastNameMessage=Vänligen ange efternamn.
missingEmailMessage=Vänligen ange e-post.
missingUsernameMessage=Vänligen ange användarnamn.
missingPasswordMessage=Vänligen ange lösenord.
missingTotpMessage=Vänligen ange autentiseringskod.
notMatchPasswordMessage=Lösenorden matchar inte.

invalidPasswordExistingMessage=Det nuvarande lösenordet är ogiltigt.
invalidPasswordConfirmMessage=Lösenordsbekräftelsen matchar inte.
invalidTotpMessage=Autentiseringskoden är ogiltig.

usernameExistsMessage=Användarnamnet finns redan.
emailExistsMessage=E-postadressen finns redan.

federatedIdentityExistsMessage=Användare med {0} {1} finns redan. Vänligen logga in till kontohanteringen för att länka kontot.

confirmLinkIdpTitle=Kontot finns redan
federatedIdentityConfirmLinkMessage=Användare med {0} {1} finns redan, Hur vill du fortsätta?
federatedIdentityConfirmReauthenticateMessage=Autentisera för att länka ditt konto med {0}
confirmLinkIdpReviewProfile=Granska profil
confirmLinkIdpContinue=Lägg till i existerande konto

configureTotpMessage=Du behöver konfigurera mobilautentiseraren för att aktivera ditt konto.
updateProfileMessage=Du behöver uppdatera din användarprofil för att aktivera ditt konto.
updatePasswordMessage=Du behöver byta ditt lösenord för att aktivera ditt konto.
verifyEmailMessage=Du behöver verifiera din e-postadress för att aktivera ditt konto.
linkIdpMessage=Du behöver verifiera din e-postadress för att länka ditt konto med {0}.

emailSentMessage=Du bör inom kort motta ett e-postmeddelande med ytterligare instruktioner.
emailSendErrorMessage=E-postmeddelandet kunde inte skickas, försök igen senare.

accountUpdatedMessage=Ditt konto har uppdaterats.
accountPasswordUpdatedMessage=Ditt lösenord har uppdaterats.

noAccessMessage=Ingen åtkomst

invalidPasswordMinLengthMessage=Ogiltigt lösenord. Minsta längd är {0}.
invalidPasswordMinDigitsMessage=Ogiltigt lösenord: måste innehålla minst {0} siffror.
invalidPasswordMinLowerCaseCharsMessage=Ogiltigt lösenord: måste innehålla minst {0} små bokstäver.
invalidPasswordMinUpperCaseCharsMessage=Ogiltigt lösenord: måste innehålla minst {0} stora bokstäver.
invalidPasswordMinSpecialCharsMessage=Ogiltigt lösenord: måste innehålla minst {0} specialtecken.
invalidPasswordNotUsernameMessage=Ogiltigt lösenord: Får inte vara samma som användarnamnet.
invalidPasswordRegexPatternMessage=Ogiltigt lösenord: matchar inte regex mönstret(en).
invalidPasswordHistoryMessage=Ogiltigt lösenord: Får inte vara samma som de senaste {0} lösenorden.
invalidPasswordGenericMessage=Ogiltigt lösenord: Det nya lösenordet stämmer inte med lösenordspolicyn.

failedToProcessResponseMessage=Misslyckades med att behandla svaret
httpsRequiredMessage=HTTPS krävs
realmNotEnabledMessage=Realm är inte aktiverad
invalidRequestMessage=Ogiltig förfrågan
failedLogout=Utloggning misslyckades
unknownLoginRequesterMessage=Okänd inloggningsförfrågan
loginRequesterNotEnabledMessage=Inloggningsförfrågaren är inte aktiverad
bearerOnlyMessage=Bearer-only-applikationer tillåts inte att initiera inloggning genom webbläsare
standardFlowDisabledMessage=Klienten tillåts inte att initiera inloggning genom webbläsare med det givna response_type. Standardflödet är inaktiverat för klienten.
implicitFlowDisabledMessage=Klienten tillåts inte att initiera inloggning genom webbläsare med det givna response_type. Villkorslöst flöde är inaktiverat för klienten.
invalidRedirectUriMessage=Ogiltig omdirigeringsadress
unsupportedNameIdFormatMessage=NameIDFormat stöds ej
invalidRequesterMessage=Ogiltig förfrågare
registrationNotAllowedMessage=Registrering tillåts ej
resetCredentialNotAllowedMessage=Återställning av uppgifter tillåts ej

permissionNotApprovedMessage=Rättigheten ej godkänd.
noRelayStateInResponseMessage=Inget vidarebefordrat tillstånd i svaret från identitetsleverantör.
insufficientPermissionMessage=Otillräckliga tillstånd för att länka identiteter.
couldNotProceedWithAuthenticationRequestMessage=Kunde inte fortsätta med autentiseringsförfrågan till identitetsleverantör.
couldNotObtainTokenMessage=Kunde inte motta element från identitetsleverantör.
unexpectedErrorRetrievingTokenMessage=Oväntat fel när element hämtas från identitetsleverantör.
unexpectedErrorHandlingResponseMessage=Oväntat fel under hantering av svar från från identitetsleverantör.
identityProviderAuthenticationFailedMessage=Autentiseringen misslyckades. Kunde inte autentisera med identitetsleverantör.
couldNotSendAuthenticationRequestMessage=Kunde inte skicka autentiseringsförfrågan till identitetsleverantör.
unexpectedErrorHandlingRequestMessage=Oväntat fel under hantering av autentiseringsförfrågan till identitetsleverantör.
invalidAccessCodeMessage=Ogiltig tillträdeskod.
sessionNotActiveMessage=Sessionen ej aktiv.
invalidCodeMessage=Ett fel uppstod, vänligen logga in igen genom din applikation.
identityProviderUnexpectedErrorMessage=Oväntat fel under autentiseringen med identitetsleverantör
identityProviderNotFoundMessage=Kunde inte hitta en identitetsleverantör med identifikatorn.
identityProviderLinkSuccess=Ditt konto lyckades med att länka {0} med kontot {1}.
staleCodeMessage=Den här sidan är inte längre giltig, vänligen gå tillbaka till din applikation och logga in igen
realmSupportsNoCredentialsMessage=Realmen stödjer inga inloggningstyper.
identityProviderNotUniqueMessage=Realmen stödjer flera identitetsleverantör. Kunde inte avgöra vilken identitetsleverantör som skall användas för autentisering.
emailVerifiedMessage=Din e-postadress har blivit verifierad.
staleEmailVerificationLink=Länken du klickade på är en gammal, inaktuell länk som inte längre är giltig. Kanske har du redan verifierat din e-post?

backToApplication=&laquo; Tillbaka till applikationen
missingParameterMessage=Parametrar som saknas\: {0}
clientNotFoundMessage=Klienten hittades ej.
clientDisabledMessage=Klienten är inaktiverad.
invalidParameterMessage=Ogiltig parameter\: {0}
alreadyLoggedIn=Du är redan inloggad.
loginAccountTitle=Logga in till ditt konto
