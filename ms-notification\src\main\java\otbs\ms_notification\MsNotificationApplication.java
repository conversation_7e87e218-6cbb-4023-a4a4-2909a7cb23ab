package otbs.ms_notification;

import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
/**
 * Classe principale du microservice de gestion des notifications.
 * Cette classe démarre l'application Spring Boot et active la découverte de service
 * pour permettre l'enregistrement auprès du serveur Eureka.
 *
 * @version 0.0.1-SNAPSHOT
 * <AUTHOR>
 *
 */

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableCaching
@EnableRabbit
@EnableAsync
@EnableConfigurationProperties
public class MsNotificationApplication {

	/**
	 * Point d'entrée principal de l'application.
	 * Lance le microservice de gestion des notifications qui s'enregistre
	 * automatiquement auprès du serveur Eureka pour la découverte de service.
	 *
	 * @param args Arguments de ligne de commande passés à l'application
	 */
	public static void main(String[] args) {
		SpringApplication.run(MsNotificationApplication.class, args);
	}

}
