import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ToastrService } from 'ngx-toastr';
import { AstreinteDto } from '../../models/astreinte.model';

interface BadgeTelepeage {
  id: number;
  numero: string;
  solde: number;
  statut: string;
  operateur: string;
}

@Component({
  selector: 'app-affecter-telepeage-astreinte',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule
  ],
  template: `
    <div class="dialog-header">
      <h2>Attribuer un badge télépéage</h2>
      <button mat-icon-button (click)="onCancel()" [disabled]="loading">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <form [formGroup]="telepeageForm" (ngSubmit)="onSubmit()" class="reservation-form">
      <div class="form-grid">
        <div class="checkbox-group">
          <p class="priority-help-text">Sélectionnez un badge télépéage pour cette astreinte :</p>

          <div class="no-badge-option">
            <mat-checkbox
              formControlName="noBadge"
              color="primary"
              [attr.aria-label]="'Ne pas attribuer de badge télépéage'"
            >
              Aucun badge télépéage
            </mat-checkbox>
          </div>

          <p *ngIf="badgesDisponibles.length === 0" class="priority-help-text error-text">
            Aucun badge télépéage n'est disponible pour cette période
          </p>

          <div *ngIf="badgesDisponibles.length > 0" class="table-container">
            <table class="badge-table">
              <thead>
                <tr>
                  <th class="checkbox-column"></th>
                  <th>Numéro du badge</th>
                  <th>Solde</th>
                  <th>Opérateur</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let badge of badgesDisponibles; trackBy: trackByBadge"
                    class="badge-row"
                    [ngClass]="{'selected-badge': telepeageForm.get('badgeId')?.value === badge.id}">
                  <td class="checkbox-column">
                    <mat-checkbox
                      [checked]="telepeageForm.get('badgeId')?.value === badge.id"
                      (change)="onCheckboxChange(badge.id)"
                      color="primary"
                      [value]="badge.numero"
                      [disabled]="loading || telepeageForm.value.noBadge"
                      [attr.aria-label]="'Sélectionner le badge ' + badge.numero"
                    ></mat-checkbox>
                  </td>
                  <td>{{ badge.numero }}</td>
                  <td>{{ badge.solde | number:'1.2-2' }} DT</td>
                  <td>{{ badge.operateur }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <mat-dialog-actions class="form-actions">
        <button mat-button (click)="onCancel()" [disabled]="loading">Fermer</button>
        <button
          mat-raised-button
          color="primary"
          type="submit"
          [disabled]="loading || (!telepeageForm.value.noBadge && !telepeageForm.value.badgeId)"
        >
          {{ loading ? 'Attribution...' : 'Attribuer' }}
        </button>
      </mat-dialog-actions>
    </form>
  `,
  styleUrls: ['./affecter-telepeage-astreinte.component.scss']
})
export class AffecterTelepeageAstreinteComponent implements OnInit {
  telepeageForm: FormGroup;
  badgesDisponibles: BadgeTelepeage[] = [];
  loading = false;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AffecterTelepeageAstreinteComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { astreinte: AstreinteDto },
    private toastr: ToastrService
  ) {
    this.telepeageForm = this.fb.group({
      badgeId: [''],
      noBadge: [false],
      montantEstime: [''],
      trajets: [''],
      commentaire: ['']
    });
  }

  ngOnInit(): void {
    this.loadBadgesDisponibles();
    this.setupFormValidation();
  }

  setupFormValidation(): void {
    // Gérer l'exclusion mutuelle entre "Aucun badge" et sélection de badge
    this.telepeageForm.get('noBadge')?.valueChanges.subscribe(noBadge => {
      if (noBadge) {
        this.telepeageForm.patchValue({ badgeId: null });
      }
    });

    this.telepeageForm.get('badgeId')?.valueChanges.subscribe(badgeId => {
      if (badgeId) {
        this.telepeageForm.patchValue({ noBadge: false });
      }
    });
  }

  loadBadgesDisponibles(): void {
    // Données mockées pour les badges télépéage
    this.badgesDisponibles = [
      {
        id: 1,
        numero: 'TP001',
        solde: 125.50,
        statut: 'ACTIF',
        operateur: 'Autoroutes de Tunisie'
      },
      {
        id: 2,
        numero: 'TP002',
        solde: 89.25,
        statut: 'ACTIF',
        operateur: 'Autoroutes de Tunisie'
      },
      {
        id: 3,
        numero: 'TP003',
        solde: 200.00,
        statut: 'ACTIF',
        operateur: 'Autoroutes de Tunisie'
      },
      {
        id: 4,
        numero: 'TP004',
        solde: 45.75,
        statut: 'ACTIF',
        operateur: 'Autoroutes de Tunisie'
      },
      {
        id: 5,
        numero: 'TP005',
        solde: 310.00,
        statut: 'ACTIF',
        operateur: 'Autoroutes de Tunisie'
      }
    ];
  }

  onSubmit(): void {
    const formValue = this.telepeageForm.value;

    // Vérifier qu'une option est sélectionnée
    if (!formValue.noBadge && !formValue.badgeId && this.badgesDisponibles.length > 0) {
      this.toastr.warning('Veuillez sélectionner un badge ou cocher "Aucun badge"', 'Sélection requise');
      return;
    }

    this.loading = true;

    // Simulation d'une requête API
    setTimeout(() => {
      this.loading = false;

      if (formValue.noBadge) {
        this.toastr.success('Aucun badge télépéage attribué', 'Configuration enregistrée');
        this.dialogRef.close({
          success: true,
          badge: null,
          noBadge: true
        });
      } else if (formValue.badgeId) {
        const selectedBadge = this.badgesDisponibles.find(b => b.id === formValue.badgeId);
        this.toastr.success(
          `Badge télépéage ${selectedBadge?.numero} attribué avec succès`,
          'Attribution réussie'
        );
        this.dialogRef.close({
          success: true,
          badge: selectedBadge,
          noBadge: false
        });
      } else {
        // Cas où aucun badge n'est disponible
        this.toastr.success('Configuration télépéage enregistrée', 'Configuration enregistrée');
        this.dialogRef.close({
          success: true,
          badge: null,
          noBadge: true
        });
      }
    }, 1000);
  }

  onCheckboxChange(badgeId: number): void {
    const currentValue = this.telepeageForm.get('badgeId')?.value;
    if (currentValue === badgeId) {
      this.telepeageForm.patchValue({ badgeId: null });
    } else {
      this.telepeageForm.patchValue({
        badgeId: badgeId,
        noBadge: false // Désactiver "Aucun badge" si un badge est sélectionné
      });
    }
  }

  trackByBadge(index: number, badge: BadgeTelepeage): number {
    return badge.id;
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.telepeageForm.controls).forEach(key => {
      const control = this.telepeageForm.get(key);
      control?.markAsTouched();
    });
  }

  getBadgeDisplayText(badge: BadgeTelepeage): string {
    return `${badge.numero} - ${badge.operateur} (Solde: ${badge.solde} DT)`;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.telepeageForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.telepeageForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        switch (fieldName) {
          case 'badgeId': return 'Badge télépéage est requis';
          case 'montantEstime': return 'Montant estimé est requis';
          default: return 'Ce champ est requis';
        }
      }
      if (field.errors['min']) {
        return 'Le montant doit être supérieur à 0';
      }
    }
    return '';
  }

  // Trajets prédéfinis pour aider l'utilisateur
  getTrajetsCommuns(): string[] {
    return [
      'Tunis - Sfax',
      'Tunis - Sousse',
      'Tunis - Monastir',
      'Sfax - Sousse',
      'Sousse - Monastir',
      'Tunis - Bizerte',
      'Tunis - Nabeul'
    ];
  }

  ajouterTrajetCommun(trajet: string): void {
    const trajetsActuels = this.telepeageForm.get('trajets')?.value || '';
    const nouveauxTrajets = trajetsActuels ? `${trajetsActuels}, ${trajet}` : trajet;
    this.telepeageForm.patchValue({ trajets: nouveauxTrajets });
  }
}
