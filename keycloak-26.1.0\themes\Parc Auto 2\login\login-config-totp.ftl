<#import "template.ftl" as layout>
<#import "password-commons.ftl" as passwordCommons>
<@layout.registrationLayout displayRequiredFields=false displayMessage=!messagesPerField.existsError('totp','userLabel'); section>

    <#if section = "header">
        
    <#elseif section = "form">

        <div id="kc-form">
            <div id="kc-form-wrapper">
                <img src="${url.resourcesPath}/img/OTBS.png" alt="Onetech Logo" id="logo"><br>
                <span id="title">Configuration de la sécurité renforcée</span>
                
                <ol id="kc-totp-settings">
                    <li>
                        <p>${msg("Install the application")}</p>
                    </li>
                    <#if mode?? && mode = "manual">
                        <li>
                            <p>${msg("loginTotpManualStep2")}</p>
                            <p><span >${totp.totpSecretEncoded}</span></p>
                            <p><a href="${totp.qrUrl}" id="mode-barcode">${msg("loginTotpScanBarcode")}</a></p>
                        </li>
                        <li>
                            <p>${msg("loginTotpManualStep3")}</p>
                            <p>
                            <ul>
                                <div>
                                <li id="kc-totp-type">${msg("loginTotpType")}: ${msg("loginTotp." + totp.policy.type)}</li>
                                <li id="kc-totp-algorithm">${msg("loginTotpAlgorithm")}: ${totp.policy.getAlgorithmKey()}</li>
                                <li id="kc-totp-digits">${msg("loginTotpDigits")}: ${totp.policy.digits}</li>
                                <#if totp.policy.type = "totp">
                                    <li id="kc-totp-period">${msg("loginTotpInterval")}: ${totp.policy.period}</li>
                                <#elseif totp.policy.type = "hotp">
                                    <li id="kc-totp-counter">${msg("loginTotpCounter")}: ${totp.policy.initialCounter}</li>
                                </#if>
                            </ul>
                            </p>
                            </div>
                        </li>
                    <#else>

                        <li>

                            <p>${msg("loginTotpStep2")}</p>
                            <img id="kc-totp-secret-qr-code" src="data:image/png;base64, ${totp.totpSecretQrCode}" alt="Figure: Barcode"><br/>
                            <p><a href="${totp.manualUrl}" id="mode-manual">${msg("loginTotpUnableToScan")}</a></p>
                        </li>
                    </#if>
                    <li>
                        <p>${msg("loginTotpStep3")}</p>
                        <p>${msg("loginTotpStep3DeviceName")}</p>
                    </li>
                </ol>

                <form action="${url.loginAction}" class="${properties.kcFormClass!}" id="kc-totp-settings-form" method="post">
                    <div class="${properties.kcFormGroupClass!}">
                        <div class="${properties.kcInputWrapperClass!}">
                            <label for="totp" class="control-label">Code de vérification</label> <span class="required">*</span>
                            <div class="field-help-text">Entrez le code généré par votre application d'authentification</div>
                        </div>
                        <div class="${properties.kcInputWrapperClass!}">
                            <input type="text" id="totp" name="totp" autocomplete="off" class="${properties.kcInputClass!}"
                                   placeholder="Entrez le code à 6 chiffres"
                                   aria-invalid="<#if messagesPerField.existsError('totp')>true</#if>"
                                   dir="ltr"
                            />

                            <#if messagesPerField.existsError('totp')>
                                <span id="input-error-otp-code" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('totp'))?no_esc}
                                </span>
                            </#if>

                        </div>
                        <input type="hidden" id="totpSecret" name="totpSecret" value="${totp.totpSecret}" />
                        <#if mode??><input type="hidden" id="mode" name="mode" value="${mode}"/></#if>
                    </div>

                    <div class="${properties.kcFormGroupClass!}">
                        <div class="${properties.kcInputWrapperClass!}">
                            <label for="userLabel" class="control-label">Nom de l'appareil</label> <#if totp.otpCredentials?size gte 1><span class="required">*</span></#if>
                            <div class="field-help-text">Donnez un nom à cet appareil pour le reconnaître facilement</div>
                        </div>

                        <div class="${properties.kcInputWrapperClass!}">
                            <input type="text" class="${properties.kcInputClass!}" id="userLabel" name="userLabel" autocomplete="off"
                                   placeholder="Ex: Mon téléphone, Tablette professionnelle..."
                                   aria-invalid="<#if messagesPerField.existsError('userLabel')>true</#if>" dir="ltr"
                            />

                            <#if messagesPerField.existsError('userLabel')>
                                <span id="input-error-otp-label" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('userLabel'))?no_esc}
                                </span>
                            </#if>
                        </div>
                    </div>

                    <div class="button-container">
                        <input type="submit"
                               class="pf-c-button pf-m-primary btn-lg"
                               id="saveTOTPBtn" value="Enregistrer"
                        />
                        <button type="submit"
                                class="pf-c-button pf-m-primary btn-lg"
                                id="cancelTOTPBtn1" name="cancel-aia" value="true">Annuler</button>
                    </div>
                </form>
            </div>
        </div>
    </#if>

</@layout.registrationLayout>
