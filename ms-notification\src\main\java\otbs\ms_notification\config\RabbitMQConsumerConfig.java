package otbs.ms_notification.config;

import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@EnableRabbit
public class RabbitMQConsumerConfig {

    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(new Jackson2JsonMessageConverter());
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(10); // 3 threads consommateurs
        factory.setMaxConcurrentConsumers(10); // max
        factory.setPrefetchCount(10); // récupère jusqu'à 10 messages d'un coup
        factory.setMismatchedQueuesFatal(false); // Ignorer les conflits de queues
        return factory;
    }
    
    @Bean
    public Queue incidentNotificationsQueue() {
        return QueueBuilder
                .durable("incident.notifications")
                .ttl(86400000) // 24 heures en millisecondes
                .maxLength(10000) // Limite de 10000 messages
                .build();
    }
    
    @Bean
    public Queue missionNotificationsQueue() {
        return QueueBuilder
                .durable("mission.notifications")
                .build();
    }
    
    @Bean
    public Queue astreinteNotificationsQueue() {
        return QueueBuilder
                .durable("astreinte.notifications")
                .build();
    }
}
