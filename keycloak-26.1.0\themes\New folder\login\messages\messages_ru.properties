doLogIn=Вход
doRegister=Регистрация
doRegisterSecurityKey=Регистрация
doCancel=Отмена
doSubmit=Подтвердить
doYes=Да
doNo=Нет
doContinue=Продолжить
doAccept=Подтвердить
doDecline=Отменить
doForgotPassword=Забыли пароль?
doClickHere=Нажмите сюда
doImpersonate=Имперсонализироваться
kerberosNotConfigured=Kerberos не сконфигурирован
kerberosNotConfiguredTitle=Kerberos не сконфигурирован
bypassKerberosDetail=Либо вы не вошли в систему с помощью Kerberos, либо ваш браузер не настроен для входа в систему Kerberos. Пожалуйста, нажмите кнопку 'Продолжить' для входа в с помощью других средств
kerberosNotSetUp=Kerberos не настроен. Вы не можете войти.
registerWithTitle=Зарегистрироваться с {0}
registerWithTitleHtml={0}
loginTitle=Вход {0}
loginTitleHtml={0}
impersonateTitle={0} Имперсонализация пользователя
impersonateTitleHtml=<strong>{0}</strong> Имперсонализация пользователя
realmChoice=Realm
unknownUser=Неизвестный пользователь
loginTotpTitle=Настройка мобильного аутентификатора
loginProfileTitle=Обновление информации учетной записи
loginTimeout=Вы слишком долго бездействовали. Процесс аутентификации начнется с начала.
oauthGrantTitle=Согласовать доступ
oauthGrantTitleHtml={0}
errorTitle=Мы сожалеем...
errorTitleHtml=Мы <strong>сожалеем</strong> ...
emailVerifyTitle=Подтверждение адреса E-mail
emailForgotTitle=Забыли пароль?
updatePasswordTitle=Обновление пароля
codeSuccessTitle=Успешный код
codeErrorTitle=Ошибочный код\: {0}

pageNotFound=Страница не найдена

termsTitle=Условия и положения
termsTitleHtml=Условия и положения
termsText=<p>Условия и положения должны быть определены</p>

recaptchaFailed=Некорректная Recaptcha
recaptchaNotConfigured=Recaptcha требуется, но не сконфигурирована
consentDenied=В согласовании отказано.

# SAML authentication
saml.post-form.title=Перенаправление аутентификации
saml.post-form.message=Перенаправление, пожалуйста подождите.

noAccount=Новый пользователь?
username=Имя пользователя
usernameOrEmail=Имя пользователя или E-mail
firstName=Имя
givenName=Выданное имя
fullName=Полное имя
lastName=Фамилия
familyName=Фамилия
email=E-mail
password=Пароль
passwordConfirm=Подтверждение пароля
passwordNew=Новый пароль
passwordNewConfirm=Подтверждение нового пароля
rememberMe=Запомнить меня
authenticatorCode=Одноразовый код
address=Адрес
street=Улица
locality=Город
region=Регион
postal_code=Почтовый индекс
country=Страна
emailVerified=E-mail подтвержден
gssDelegationCredential=Делегирование учетных данных GSS

logoutOtherSessions=Выполнить выход на других устройствах

loginAccountTitle=Вход в учетную запись
loginTotpStep1=Установите одно из следующих приложений на ваш мобильный телефон:
loginTotpStep2=Откройте приложение и просканируйте QR-код:
loginTotpStep3=Введите одноразовый код, выданный приложением, и нажмите Подтвердить для завершения настройки.
loginTotpManualStep2=Откройте приложение и введите ключ:
loginTotpManualStep3=Используйте следующие настройки, если приложение позволяет их устанавливать:
loginTotpStep3DeviceName=Укажите имя устройства, которое поможет вам найти его в списке ваших устройств.
loginTotpUnableToScan=Не удается выполнить сканирование?
loginTotpScanBarcode=Сканировать QR-код?
loginOtpOneTime=Одноразовый код
loginTotpDeviceName=Имя устройства
loginTotpType=Тип
loginTotpAlgorithm=Алгоритм
loginTotpDigits=Количество цифр
loginTotpInterval=Интервал
loginTotpCounter=Счетчик


oauthGrantRequest=Вы согласуете доступ к этим привилегиям?
inResource=в

emailVerifyInstruction1=Вам было отправлено письмо с инструкциями для подтверждения адреса E-mail.
emailVerifyInstruction2=Не получили письмо с кодом подтверждения?
emailVerifyInstruction3=для повторной отправки письма.

emailLinkIdpTitle=Связать {0}
emailLinkIdp1=Вам было отправлено письмо с инструкциями по объединению {0} учетной записи {1} с вашей учетной записью {2}.
emailLinkIdp2=Не получили код подтверждения на ваш E-mail?
emailLinkIdp3=для повторной отправки письма.

backToLogin=&laquo; Назад ко входу

emailInstruction=Введите Ваше имя пользователя или E-mail и мы вышлем Вам инструкции по получению нового пароля.

copyCodeInstruction=Пожалуйста, скопируйте этот код в приложение:

personalInfo=Персональная информация:
role_admin=Администратор
role_realm-admin=Администратор realm
role_create-realm=Создание realm
role_create-client=Создание клиента
role_view-realm=Просмотр realm
role_view-users=Просмотр пользователей
role_view-applications=Просмотр приложений
role_view-clients=Просмотр клиентов
role_view-events=Просмотр событий
role_view-identity-providers=Просмотр провайдеров учетных записей
role_manage-realm=Управление realm
role_manage-users=Управление пользователями
role_manage-applications=Управление приложениями
role_manage-identity-providers=Управление провайдерами учетных записей
role_manage-clients=Управление клиентами
role_manage-events=Управление событиями
role_view-profile=Просмотр профиля
role_manage-account=Управление учетной записью
role_read-token=Чтение токена
role_offline-access=Оффлайн доступ
client_account=Учетная запись
client_security-admin-console=Консоль администратора безопасности
client_admin-cli=Командный интерфейс администратора
client_realm-management=Управление realm
client_broker=Брокер

invalidUserMessage=Неправильное имя пользователя или пароль.
invalidEmailMessage=Неправильный E-mail.
accountDisabledMessage=Учетная запись заблокирована, свяжитесь с администратором.
accountTemporarilyDisabledMessage=Неправильное имя пользователя или пароль.
accountPermanentlyDisabledMessage=Неправильное имя пользователя или пароль.
accountTemporarilyDisabledMessageTotp=Неверный код аутентификатора.
accountPermanentlyDisabledMessageTotp=Неверный код аутентификатора.
expiredCodeMessage=Вход просрочен по таймауту. Пожалуйста, войдите снова.

missingFirstNameMessage=Пожалуйста введите имя.
missingLastNameMessage=Пожалуйста введите фамилию.
missingEmailMessage=Пожалуйста введите E-mail.
missingUsernameMessage=Пожалуйста введите имя пользователя.
missingPasswordMessage=Пожалуйста введите пароль.
missingTotpMessage=Пожалуйста введите код аутентификатора.
notMatchPasswordMessage=Пароли не совпадают.

invalidPasswordExistingMessage=Неверный существующий пароль.
invalidPasswordConfirmMessage=Подтверждение пароля не совпадает.
invalidTotpMessage=Неверный код аутентификатора.

usernameExistsMessage=Имя пользователя уже занято.
emailExistsMessage=E-mail уже существует.

federatedIdentityExistsMessage=Пользователь с {0} {1} уже существует. Пожалуйста войдите в управление учетными записями, чтобы связать эту учетную запись.

confirmLinkIdpTitle=Учетная запись уже существует
federatedIdentityConfirmLinkMessage=Пользователь с {0} {1} уже сущестует. Хотите продолжить?
federatedIdentityConfirmReauthenticateMessage=Аутентифицируйтесь, чтобы связать Вашу учетную запись с {0}
confirmLinkIdpReviewProfile=Обзор профиля
confirmLinkIdpContinue=Добавить в существующую учетную запись

configureTotpMessage=Вам необходимо настроить аутентификатор в мобильном устройстве, чтобы активировать учетную запись.
updateProfileMessage=Вам необходимо обновить свой профиль, чтобы активировать Вашу учетную запись.
updatePasswordMessage=Вам необходимо изменить пароль, чтобы активировать Вашу учетную запись.
verifyEmailMessage=Вам необходимо подтвердить Ваш E-mail, чтобы активировать Вашу учетную запись.
linkIdpMessage=Вам необходимо подтвердить Ваш E-mail, чтобы связать Вашу учетную запись с {0}.

emailSentMessage=В ближайшее время Вы должны получить письмо с дальнейшими инструкциями.
emailSendErrorMessage=Не получается отправить письмо. Пожалуйста, повторите позже.

accountUpdatedMessage=Ваша учетная запись успешно обновлена.
accountPasswordUpdatedMessage=Ваш пароль успешно обновлен.

noAccessMessage=Нет доступа

invalidPasswordMinLengthMessage=Некорректный пароль: длина пароля должна быть не менее {0} символов(а).
invalidPasswordMinDigitsMessage=Некорректный пароль: пароль должен содержать не менее {0} цифр(ы).
invalidPasswordMinLowerCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символов(а) в нижнем регистре.
invalidPasswordMinUpperCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символов(а) в верхнем регистре.
invalidPasswordMinSpecialCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} спецсимволов(а).
invalidPasswordNotUsernameMessage=Некорректный пароль: пароль не должен совпадать с именем пользователя.
invalidPasswordRegexPatternMessage=Некорректный пароль: пароль не прошел проверку по регулярному выражению.
invalidPasswordHistoryMessage=Некорректный пароль: пароль не должен совпадать с последним(и) {0} паролем(ями).
invalidPasswordGenericMessage=Некорректный пароль: новый пароль не соответствует правилам пароля.

failedToProcessResponseMessage=Не удалось обработать ответ
httpsRequiredMessage=Требуется HTTPS
realmNotEnabledMessage=Realm не включен
invalidRequestMessage=Неверный запрос
failedLogout=Выйти не удалось
unknownLoginRequesterMessage=Неизвестный клиент
loginRequesterNotEnabledMessage=Клиент отключен
bearerOnlyMessage=Bearer-only приложениям не разрешается инициализация входа через браузер
standardFlowDisabledMessage=Клиенту не разрешается инициировать вход через браузер с данным response_type. Standard flow отключен для этого клиента.
implicitFlowDisabledMessage=Клиенту не разрешается инициировать вход через браузер с данным response_type. Implicit flow отключен для этого клиента.
invalidRedirectUriMessage=Неверный uri для переадресации
unsupportedNameIdFormatMessage=Неподдерживаемый NameIDFormat
invalidRequesterMessage=Неверный запрашивающий
registrationNotAllowedMessage=Регистрация не разрешена
resetCredentialNotAllowedMessage=Сброс идентификационных данных не разрешен

permissionNotApprovedMessage=Разрешение не подтверждено.
noRelayStateInResponseMessage=Нет изменения состояния в ответе от провайдера учетных записей.
insufficientPermissionMessage=Недостаточно полномочий для связывания идентификаторов.
couldNotProceedWithAuthenticationRequestMessage=Невозможно обработать аутентификационный запрос в провайдере учетных записей.
couldNotObtainTokenMessage=Не удалось получить токен от провайдера учетных записей.
unexpectedErrorRetrievingTokenMessage=Непредвиденная ошибка при получении токена от провайдера учетных записей.
unexpectedErrorHandlingResponseMessage=Непредвиденная ошибка при обработке ответа от провайдера учетных записей.
identityProviderAuthenticationFailedMessage=Аутентификация провалена. Невозможно аутентифицировать с поставщиком учетных записей.
couldNotSendAuthenticationRequestMessage=Не получается выполнить запрос аутентификации к поставщику учетных записей.
unexpectedErrorHandlingRequestMessage=Непредвиденная ошибка при обработке запроса аутентификации поставщика учетных записей.
invalidAccessCodeMessage=Неверный код доступа.
sessionNotActiveMessage=Сессия не активна.
invalidCodeMessage=Произошла ошибка. Пожалуйста, войдите в систему снова через ваше приложение.
identityProviderUnexpectedErrorMessage=Непредвиденная ошибка при проверке подлинности поставщика учетных записей.
identityProviderNotFoundMessage=Не удалось найти поставщика учетных записей с данным идентификатором.
identityProviderLinkSuccess=Ваша учетная запись была успешно соединена с {0} учетной записью {1} .
staleCodeMessage=Эта страница больше не действительна, пожалуйста, вернитесь в приложение и снова войдите в систему.
realmSupportsNoCredentialsMessage=Realm не поддерживает никакой тип учетных данных.
identityProviderNotUniqueMessage=Realm поддерживает несколько поставщиков учетных записей. Не удалось определить, какой именно поставщик должен использоваться для аутентификации.
emailVerifiedMessage=Ваш E-mail был подтвержден.
staleEmailVerificationLink=Ссылка, по которой Вы перешли, устарела и больше не действует. Может быть, вы уже подтвердили свой E-mail?

backToApplication=&laquo; Назад в приложение
missingParameterMessage=Пропущенные параметры\: {0}
clientNotFoundMessage=Клиент не найден.
clientDisabledMessage=Клиент отключен.
invalidParameterMessage=Неверный параметр\: {0}
alreadyLoggedIn=Вы уже вошли.
