package otbs.vehicule.service;

import otbs.vehicule.dto.DocumentVehiculeDto;
import otbs.vehicule.model.*;
import otbs.vehicule.model.enums.Resultat;
import otbs.vehicule.repository.DocumentVehiculeRepo;
import otbs.vehicule.repository.VehiculeRepo;
import org.springframework.stereotype.Service;
import otbs.vehicule.repository.VisiteTechniqueRepo;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class DocumentVehiculeService {
    private final DocumentVehiculeRepo documentVehiculeRepo;
    private final VehiculeRepo vehiculeRepo;
    private final VisiteTechniqueRepo visiteTechniqueRepo;

    public DocumentVehiculeService(DocumentVehiculeRepo documentVehiculeRepo, VehiculeRepo vehiculeRepo,
                                   VisiteTechniqueRepo visiteTechniqueRepo) {
        this.documentVehiculeRepo = documentVehiculeRepo;
        this.vehiculeRepo = vehiculeRepo;
        this.visiteTechniqueRepo = visiteTechniqueRepo;
    }

    private static DocumentVehiculeDto entityToDto(DocumentVehicule documentVehicule) {
        DocumentVehiculeDto documentVehiculeDto = new DocumentVehiculeDto();
        documentVehiculeDto.setId(documentVehicule.getId());
        documentVehiculeDto.setMontant(documentVehicule.getMontant());
        documentVehiculeDto.setDateElaboration(documentVehicule.getDateElaboration());
        documentVehiculeDto.setDateDebut(documentVehicule.getDateDebut());
        documentVehiculeDto.setDateExpiration(documentVehicule.getDateExpiration());
        documentVehiculeDto.setFilePath(documentVehicule.getFilePath());
        documentVehiculeDto.setImmatriculation(documentVehicule.getVehicule().getImmatriculation());
        documentVehiculeDto.setType(documentVehicule.getClass().getSimpleName());
        if (documentVehicule instanceof VisiteTechnique visiteTechnique) {
            documentVehiculeDto.setDateReservation(visiteTechnique.getDateReservation());
            documentVehiculeDto.setResultat(visiteTechnique.getResultat());
        }
        return documentVehiculeDto;
    }

    private DocumentVehicule findDocumentVehicule(Long documentVehiculeId) {
        return documentVehiculeRepo.findById(documentVehiculeId)
                .orElseThrow(() -> new RuntimeException("DocumentVehicule non trouvé"));
    }

    private Vehicule getVehicule(Long vehiculeId) {
        return vehiculeRepo.findById(vehiculeId)
                .orElseThrow(() -> new RuntimeException("Véhicule non trouvé"));
    }

    private static <T extends DocumentVehicule> void setAttributes(DocumentVehiculeDto documentVehiculeDto, T documentVehicule) {
        documentVehicule.setDateDebut(documentVehiculeDto.getDateDebut());
        documentVehicule.setDateElaboration(documentVehiculeDto.getDateElaboration());
        documentVehicule.setDateExpiration(documentVehiculeDto.getDateExpiration());
        documentVehicule.setMontant(documentVehiculeDto.getMontant());

        if (documentVehicule instanceof VisiteTechnique visiteTechnique) {
            visiteTechnique.setResultat(documentVehiculeDto.getResultat());
            visiteTechnique.setDateReservation(documentVehiculeDto.getDateReservation());
        }
    }

    public <T extends DocumentVehicule> DocumentVehiculeDto addDocumentVehicule(
            Class<T> type, DocumentVehiculeDto documentVehiculeDto, Long vehiculeId, String extension) {
        try {
            T documentVehicule = type.getDeclaredConstructor().newInstance();

            Vehicule vehicule = getVehicule(vehiculeId);
            documentVehicule.setVehicule(vehicule);

            setAttributes(documentVehiculeDto, documentVehicule);

            DocumentVehicule savedDocumentVehicule = documentVehiculeRepo.save(documentVehicule);

            documentVehicule.setFilePath(savedDocumentVehicule.getId().toString() + "." + extension);
            return entityToDto(documentVehiculeRepo.save(documentVehicule));
        } catch (Exception e) {
            throw new IllegalArgumentException("Impossible de créer une instance de " + type.getSimpleName(), e);
        }
    }

    public DocumentVehiculeDto updateDocumentVehicule(DocumentVehiculeDto documentVehiculeDto, Long documentVehiculeId) {
        DocumentVehicule existingDocumentVehicule = findDocumentVehicule(documentVehiculeId);

        setAttributes(documentVehiculeDto, existingDocumentVehicule);

        DocumentVehicule updatedDocumentVehicule = documentVehiculeRepo.save(existingDocumentVehicule);
        return entityToDto(updatedDocumentVehicule);
    }

    public DocumentVehiculeDto getDocumentVehiculeById(Long documentVehiculeId) {
        DocumentVehicule documentVehicule = findDocumentVehicule(documentVehiculeId);

        return entityToDto(documentVehicule);
    }

    public <T extends DocumentVehicule> List<DocumentVehiculeDto> getListeDocumentVehicule(Class<T> type, Long idVehicule) {
        Vehicule vehicule = getVehicule(idVehicule);
        List<DocumentVehicule> documentVehiculeList = documentVehiculeRepo.findByVehicule(vehicule);

        return documentVehiculeList.stream()
                .filter(type::isInstance)
                .map(DocumentVehiculeService::entityToDto)
                .toList();
    }

    public void deleteDocumentVehicule(Long documentVehiculeId) {
        DocumentVehicule documentVehicule = findDocumentVehicule(documentVehiculeId);
        documentVehiculeRepo.delete(documentVehicule);
    }

    public List<DocumentVehiculeDto> getTopDocumentsExpiring() {
        LocalDateTime now = LocalDateTime.now(); // Date actuelle
        List<DocumentVehicule> topDocuments = documentVehiculeRepo
                .findTop20ByDateExpirationAfterOrderByDateExpirationAsc(now);
        topDocuments.removeIf(documentVehicule -> documentVehicule.getClass() == Vignette.class);

        return topDocuments.stream()
                .map(DocumentVehiculeService::entityToDto)
                .toList();
    }

    public DocumentVehiculeDto addReservation(Long vehiculeId, DocumentVehiculeDto documentVehiculeDto) {
        VisiteTechnique reservation = new VisiteTechnique();
        Vehicule vehicule = getVehicule(vehiculeId);
        reservation.setVehicule(vehicule);
        reservation.setDateReservation(documentVehiculeDto.getDateReservation());
        reservation.setMontant(documentVehiculeDto.getMontant());
        DocumentVehicule savedReservation = documentVehiculeRepo.save(reservation);
        return entityToDto(savedReservation);
    }

    public <T extends DocumentVehicule> DocumentVehiculeDto getLastDocumentByVehicule(Long vehiculeId, Class<T> type) {
        List<DocumentVehiculeDto> documentVehiculeDtoList = getListeDocumentVehicule(type, vehiculeId);

        return documentVehiculeDtoList.stream()
                .filter(dto -> dto.getDateExpiration() != null && dto.getResultat()!=Resultat.REFUSE)
                .max(Comparator.comparing(DocumentVehiculeDto::getDateExpiration))
                .orElse(null);
    }



    public DocumentVehiculeDto getReservationByVehicule(Long vehiculeId) {
        List<DocumentVehiculeDto> documentVehiculeDtoList = getListeDocumentVehicule(VisiteTechnique.class, vehiculeId);

        return documentVehiculeDtoList.stream()
                .filter(dto -> dto.getDateDebut() == null && dto.getResultat() == null)
                .findFirst()
                .orElse(null);
    }

    public DocumentVehiculeDto addVisiteTechnique(DocumentVehiculeDto documentVehiculeDto, Long vehiculeId, String extension) {
        try {
            Long idDocument = getReservationByVehicule(vehiculeId).getId();
            DocumentVehicule documentVehicule = findDocumentVehicule(idDocument);
            if (documentVehicule instanceof VisiteTechnique visiteTechnique) {
                Double montant = documentVehicule.getMontant();
                LocalDateTime dateReservation = visiteTechnique.getDateReservation();
                if (documentVehiculeDto.getResultat() == Resultat.ACCEPTE) {
                    setAttributes(documentVehiculeDto, documentVehicule);
                } else {
                    visiteTechnique.setResultat(documentVehiculeDto.getResultat());
                }
                documentVehicule.setMontant(montant);
                visiteTechnique.setDateReservation(dateReservation);
            }

            DocumentVehicule savedDocumentVehicule = documentVehiculeRepo.save(documentVehicule);

            documentVehicule.setFilePath(savedDocumentVehicule.getId().toString() + "." + extension);
            return entityToDto(documentVehiculeRepo.save(documentVehicule));
        } catch (Exception e) {
            throw new IllegalArgumentException("Impossible de créer une instance de ", e);
        }
    }

    public void deleteReservation(Long vehiculeId) {
        Long idDocument = getReservationByVehicule(vehiculeId).getId();
        DocumentVehicule reservation = findDocumentVehicule(idDocument);
        documentVehiculeRepo.delete(reservation);
    }

    public DocumentVehiculeDto updateReservation(DocumentVehiculeDto documentVehiculeDto, Long vehiculeId) {
        Long idDocument = getReservationByVehicule(vehiculeId).getId();
        DocumentVehicule reservation = findDocumentVehicule(idDocument);

        setAttributes(documentVehiculeDto, reservation);

        DocumentVehicule updatedReservation = documentVehiculeRepo.save(reservation);
        return entityToDto(updatedReservation);
    }

    public Map<String, Integer> getRefusedCars() {
        List<Vehicule> vehiculeList = vehiculeRepo.findAll();
        Map<String, Integer> refusedCars = new HashMap<>();

        for (Vehicule vehicule : vehiculeList) {
            if (vehicule != null) {
                int count = visiteTechniqueRepo.countByVehiculeAndResultat(vehicule, Resultat.REFUSE);
                if (count > 0) {
                    refusedCars.put(vehicule.getImmatriculation(), count);
                }
            }
        }
        return refusedCars;
    }

    public List<DocumentVehiculeDto> getDocumentsExpiringInMonth(){
        LocalDateTime now = LocalDateTime.now();
        List<Vehicule> vehiculeList = vehiculeRepo.findAll();

        List<DocumentVehiculeDto> documentVehiculeDtoList = new ArrayList<>();
        for(Vehicule vehicule : vehiculeList){
            DocumentVehiculeDto assurance = getLastDocumentByVehicule(vehicule.getIdVehicule(), Assurance.class);
            if(assurance != null && assurance.getDateExpiration().isAfter(now) && assurance.getDateExpiration().minusMonths(1L).isBefore(now)){
                documentVehiculeDtoList.add(assurance);
            }
            DocumentVehiculeDto vidange = getLastDocumentByVehicule(vehicule.getIdVehicule(), Vignette.class);
            if(vidange != null && vidange.getDateExpiration().isAfter(now) && vidange.getDateExpiration().minusMonths(1L).isBefore(now)){
                documentVehiculeDtoList.add(vidange);
            }
            DocumentVehiculeDto visiteTechnique = getLastDocumentByVehicule(vehicule.getIdVehicule(), VisiteTechnique.class);
            if(visiteTechnique != null && visiteTechnique.getDateExpiration().isAfter(now) && visiteTechnique.getDateExpiration().minusMonths(1L).isBefore(now)){
                documentVehiculeDtoList.add(visiteTechnique);
            }
        }
        return  documentVehiculeDtoList;
    }


    public List<DocumentVehiculeDto> getReservationInThreeDaysList() {
        LocalDateTime now = LocalDateTime.now();
        List<Vehicule> vehiculeList = vehiculeRepo.findAll();

        List<DocumentVehiculeDto> documentVehiculeDtoList = new ArrayList<>();
        for(Vehicule vehicule : vehiculeList){
            DocumentVehiculeDto documentVehiculeDto= getReservationByVehicule(vehicule.getIdVehicule());
            if(documentVehiculeDto != null && documentVehiculeDto.getDateReservation().isAfter(now) &&
                    documentVehiculeDto.getDateReservation().minusMonths(1L).isBefore(now)){
                documentVehiculeDtoList.add(documentVehiculeDto);
            }
        }
        return  documentVehiculeDtoList;
    }

    public List<DocumentVehiculeDto> getReservationWaitingForResult() {
        LocalDateTime now = LocalDateTime.now();
        List<Vehicule> vehiculeList = vehiculeRepo.findAll();

        List<DocumentVehiculeDto> documentVehiculeDtoList = new ArrayList<>();
        for(Vehicule vehicule : vehiculeList){
            DocumentVehiculeDto documentVehiculeDto= getReservationByVehicule(vehicule.getIdVehicule());
            if(documentVehiculeDto != null && documentVehiculeDto.getDateReservation().isBefore(now)){
                documentVehiculeDtoList.add(documentVehiculeDto);
            }
        }
        return  documentVehiculeDtoList;
    }

    public List<DocumentVehiculeDto> getReservationEnAttente() {
        List<DocumentVehicule> documentVehiculeList = visiteTechniqueRepo.findByResultat(Resultat.EN_ATTENTE);
        return documentVehiculeList.stream()
                .map(DocumentVehiculeService::entityToDto)
                .toList();
    }

}