<#macro content>
<footer class="kc-footer">
    <div class="footer-container">
        <!-- Section principale -->
        <div class="footer-main">
            <div class="footer-brand">
                <div class="logo-area">
                    <img src="${url.resourcesPath}/img/car.png" alt="OTBS Logo" class="brand-logo">
                    <div class="company-logo"><span class="parc">Parc</span><span class="auto">Auto</span></div>
                </div>
                <p class="footer-description">Votre solution de gestion de parc automobile</p>
            </div>
            
            <!-- Liens de navigation -->
            <div class="footer-nav">
                <div class="footer-nav-column">
                    <h3 class="footer-title">Support</h3>
                    <ul class="footer-links">
                        <li><a href="#">Centre d'aide</a></li>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-nav-column">
                    <h3 class="footer-title">Légal</h3>
                    <ul class="footer-links">
                        <li><a href="#">Confidentialité</a></li>
                        <li><a href="#">Conditions</a></li>
                        <li><a href="#">RGPD</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Animation de la voiture -->
    <div class="footer-car-container" id="carContainer">
        <div class="footer-car">
            <div class="car-top"></div>
            <div class="car-body"></div>
            <div class="window-front"></div>
            <div class="window-rear"></div>
            <div class="car-light-front"></div>
            <div class="car-light-rear"></div>
            <div class="wheel wheel-1"></div>
            <div class="wheel wheel-2"></div>
            <div class="exhaust">
                <div class="smoke smoke-1"></div>
                <div class="smoke smoke-2"></div>
                <div class="smoke smoke-3"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const carContainer = document.getElementById('carContainer');
            const car = carContainer.querySelector('.footer-car');
            let isMoving = false; // Initialement arrêtée

            function startCarAnimation() {
                isMoving = true;
                car.style.transition = 'none';
                car.style.left = '0';
                car.classList.add('moving');
                car.offsetHeight;
                car.style.transition = 'left 8s linear';
                car.style.left = 'calc(100% - 40px)';

                setTimeout(() => {
                    if (isMoving) {
                        startCarAnimation();
                    }
                }, 8000);
            }

            function toggleCarAnimation() {
                if (!isMoving) {
                    startCarAnimation();
                } else {
                    isMoving = false;
                    car.classList.remove('moving');
                    const computedStyle = window.getComputedStyle(car);
                    const currentLeft = computedStyle.left;
                    car.style.transition = 'none';
                    car.style.left = currentLeft;
                    car.offsetHeight;
                    car.style.transition = 'left 8s linear';
                }
            }

            // Positionner la voiture au départ sans animation
            car.style.left = '0';

            // Attendre le clic pour démarrer
            carContainer.addEventListener('click', toggleCarAnimation);
        });
    </script>

    <!-- Barre de copyright -->
    <div class="footer-copyright">
        <div class="copyright-content">
            <p>&copy; ${.now?string('yyyy')} OTBS. Tous droits réservés.</p>
        </div>
    </div>
</footer>
</#macro>
