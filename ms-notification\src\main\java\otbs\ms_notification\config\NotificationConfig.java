package otbs.ms_notification.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;


@Configuration
@ConfigurationProperties(prefix = "notification")
@Data
public class NotificationConfig {

    private IncidentConfig incident = new IncidentConfig();

    private DocumentVehiculeConfig documentVehicule = new DocumentVehiculeConfig();

    private MissionConfig mission = new MissionConfig();


    private Map<String, String> roleAliases;

    @Data
    public static class IncidentConfig {

        private List<String> createdRoles = List.of("RESPONSABLE_SERVICE_GENERAUX", "PROJECT_MANAGER");


        private List<String> resolvedRoles = List.of("RESPONSABLE_SERVICE_GENERAUX");
    }

    @Data
    public static class DocumentVehiculeConfig {
        private List<String> parcAutoRoles = List.of("RESPONSABLE_SERVICE_GENERAUX");
    }

    @Data
    public static class MissionConfig {

        private List<String> createdRoles = List.of("PROJECT_MANAGER", "RESPONSABLE_BUREAU_ORDRE");


        private List<String> updatedRoles = List.of("PROJECT_MANAGER", "RESPONSABLE_BUREAU_ORDRE");


        private List<String> assignedRoles = List.of("PROJECT_MANAGER", "RESPONSABLE_BUREAU_ORDRE");


        private List<String> completedRoles = List.of("PROJECT_MANAGER", "RESPONSABLE_BUREAU_ORDRE");


        private List<String> accompanistAssignedRoles = List.of("PROJECT_MANAGER", "RESPONSABLE_BUREAU_ORDRE");
    }


    public String normalizeRole(String role) {
        if (roleAliases != null && roleAliases.containsKey(role)) {
            return roleAliases.get(role);
        }
        return role;
    }

    public List<String> getIncidentRoles(String eventType) {
        return switch (eventType.toUpperCase()) {
            case "CREATED" -> incident.getCreatedRoles();
            case "RESOLVED" -> incident.getResolvedRoles();
            default -> List.of();
        };
    }

    public List<String> getVehiculeRoles(String eventType) {
        return switch (eventType.toUpperCase()) {
            case "EXPIRED" -> documentVehicule.getParcAutoRoles();
            case "RESERVATION_VISITE_TECHNIQUE" -> documentVehicule.getParcAutoRoles();
            case "RESULTAT_VISITE_TECHNIQUE" -> documentVehicule.getParcAutoRoles();
            case "EN_ATTENTE_VISITE_TECHNIQUE" -> documentVehicule.getParcAutoRoles();
            default -> List.of();
        };
    }


    public List<String> getMissionRoles(String eventType) {
        return switch (eventType.toUpperCase()) {
            case "MISSION_CREATED" -> mission.getCreatedRoles();
            case "MISSION_UPDATED" -> mission.getUpdatedRoles();
            case "MISSION_ASSIGNED" -> mission.getAssignedRoles();
            case "MISSION_COMPLETED" -> mission.getCompletedRoles();
            case "ACCOMPANIST_ASSIGNED" -> mission.getAccompanistAssignedRoles();
            default -> List.of();
        };
    }
} 