# <#import "template.ftl" as layout>
<@layout.registrationLayout displayInfo=true displayMessage=true; section>
    <#if section = "header">
         <!--${msg("emailForgotTitle")}-->
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form-wrapper">
                <div id="login-container">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("emailForgotTitle")}</h1>
                        <p class="form-subtitle">${msg("emailInstruction")}</p>
                    </div>

                    <form id="kc-reset-password-form" action="${url.loginAction}" method="post">
                        <div class="form-floating">
                            <input type="text" id="username" name="username" class="form-control"
                                   autofocus value="${(auth.attemptedUsername!'')}"
                                   aria-invalid="<#if messagesPerField.existsError('username')>true</#if>"
                                   placeholder=" " />
                            <label for="username" class="form-label">
                                <#if !realm.loginWithEmailAllowed>
                                    ${msg("username")}
                                <#elseif !realm.registrationEmailAsUsername>
                                    ${msg("usernameOrEmail")}
                                <#else>
                                    ${msg("email")}
                                </#if>
                            </label>
                            <span class="form-icon username-icon"></span>
                            
                            <#if messagesPerField.existsError('username')>
                                <span id="input-error-username" class="error-message" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('username'))?no_esc}
                                </span>
                            </#if>
                        </div>
                        
                        <div class="form-buttons">
                            <button class="login-button" type="submit">
                                <span class="button-text">${msg("doSubmit")}</span>
                                <span class="button-icon"></span>
                            </button>
                        </div>
                        
                        <div class="form-links">
                            <a href="${url.loginUrl}" class="form-link">
                                <span class="back-icon"></span>
                                <span>${kcSanitize(msg("backToLogin"))?no_esc}</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            <div class="login-illustration">
                <img src="${url.resourcesPath}/img/Login.jpg" alt="Login illustration">
            </div>
        </div>
    <#elseif section = "info" >
        <#if realm.duplicateEmailsAllowed>
            ${msg("emailInstructionUsername")}
        <#else>
            ${msg("emailInstruction")}
        </#if>
    </#if>
</@layout.registrationLayout>
