package otbs.ms_incident.dto.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.List;
import java.util.ArrayList;

/**
 * Classe de base pour tous les événements de notification.
 * Utilisée pour la communication asynchrone via RabbitMQ.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotificationEvent {
    private String eventId;
    private String eventType;
    private String source;
    private LocalDateTime timestamp;
    private List<String> destinataires; // Liste des destinataires (IDs utilisateurs)
    private String titre;
    private String message;
    private String priorite;
    private String urlAction;
    private Long entiteLieeId;
    private String entiteLieeType;
    private Map<String, Object> metadata;

    public NotificationEvent(String eventType, String source, List<String> destinataires,
                           String titre, String message) {
        this.eventId = UUID.randomUUID().toString();
        this.eventType = eventType;
        this.source = source;
        this.destinataires = destinataires != null ? new ArrayList<>(destinataires) : new ArrayList<>();
        this.titre = titre;
        this.message = message;
        this.timestamp = LocalDateTime.now();
        this.metadata = new HashMap<>();
    }

    /**
     * Constructeur avec un seul destinataire (pour compatibilité)
     */
    public NotificationEvent(String eventType, String source, String destinataire,
                           String titre, String message) {
        this.eventId = UUID.randomUUID().toString();
        this.eventType = eventType;
        this.source = source;
        this.destinataires = new ArrayList<>();
        if (destinataire != null && !destinataire.isEmpty()) {
            this.destinataires.add(destinataire);
        }
        this.titre = titre;
        this.message = message;
        this.timestamp = LocalDateTime.now();
        this.metadata = new HashMap<>();
    }

    /**
     * Ajoute un destinataire à la liste
     */
    public void ajouterDestinataire(String userId) {
        if (this.destinataires == null) {
            this.destinataires = new ArrayList<>();
        }
        if (userId != null && !userId.isEmpty() && !this.destinataires.contains(userId)) {
            this.destinataires.add(userId);
        }
    }
}
