doLogIn=Logowanie
doRegister=Rejestracja
doRegisterSecurityKey=Rejestracja
doCancel=Anuluj
doSubmit=Zatwierdź
doBack=Cofnij
doYes=Tak
doNo=Nie
doContinue=Kontynuuj
doIgnore=Ignoruj
doAccept=Akceptuj
doDecline=O<PERSON><PERSON>ć
doForgotPassword=Nie pami<PERSON> hasła?
doClickHere=Kliknij tutaj
doImpersonate=Wciel się
doTryAgain=Spróbuj ponownie
doTryAnotherWay=Spróbuj inną metodą
kerberosNotConfigured=Kerberos nie jest skonfigurowany
kerberosNotConfiguredTitle=Kerberos nie jest skonfigurowany
bypassKerberosDetail=Albo nie jesteś zalogowany przez Kerberos albo twoja przeglądarka nie jest skonfigurowana do logowania Kerberos. Kliknij kontynuuj by zalogować się w inny sposób.
kerberosNotSetUp=Kerberos nie jest skonfigurowany. Nie można się zalogować.
registerTitle=Rejestracja
loginAccountTitle=Zaloguj się na swoje konto
loginTitle=Zaloguj się do {0}
loginTitleHtml={0}
impersonateTitle=Wcielenie {0}
impersonateTitleHtml=Wcielenie <strong>{0}</strong>
realmChoice=Strefa
unknownUser=Nieznany użytkownik
loginTotpTitle=Konfiguracja dla Mobile Authenticator
loginProfileTitle=Zaktualizuj informacje konta
loginTimeout=Zbyt dużo czasu zajęło logowanie. Proces logowania rozpocznie się od nowa.
oauthGrantTitle=Przydziel dostęp dla {0}
oauthGrantTitleHtml={0}
errorTitle=Przykro nam...
errorTitleHtml=<strong>Przykro</strong> nam...
emailVerifyTitle=Weryfikacja e-maila
emailForgotTitle=Nie pamiętasz hasła?
updatePasswordTitle=Aktualizacja hasła
codeSuccessTitle=Kod sukcesu
codeErrorTitle=Kod błędu\: {0}
displayUnsupported=Żądany typ wyświetlania jest nieobsługiwany
browserRequired=Do zalogowania wymagana jest przeglądarka
browserContinue=Przeglądarka jest wymagana by dokończyć logowanie
browserContinuePrompt=Otworzyć przeglądarkę i kontynuować logowanie? [t/n]\:
browserContinueAnswer=t


termsTitle=Regulamin
termsText=<p>Regulamin, który należy zdefiniować</p>
termsPlainText=Regulamin, który należy zdefiniować.

recaptchaFailed=Błędna Recaptcha
recaptchaNotConfigured=Recaptcha jest wymagana, ale nie skonfigurowana
consentDenied=Zgoda odrzucona.

noAccount=Nie masz konta?
username=Nazwa użytkownika (login)
usernameOrEmail=Nazwa użytkownika lub e-mail (login)
firstName=Imię
givenName=Nadane imię
fullName=Pełne imię i nazwisko
lastName=Nazwisko
familyName=Nazwisko rodowe
email=E-mail
password=Hasło
passwordConfirm=Potwierdź hasło
passwordNew=Nowe hasło
passwordNewConfirm=Potwierdzenie nowego hasła
rememberMe=Zapamiętaj mnie
authenticatorCode=Kod jednorazowy
address=Adres
street=Ulica
locality=Miejscowość
region=Województwo
postal_code=Kod pocztowy
country=Państwo
emailVerified=Email zweryfikowany
website=Strona internetowa
phoneNumber=Nr telefonu
phoneNumberVerified=Nr telefonu zweryfikowany
gender=Płeć
birthday=Data urodzenia
zoneinfo=Strefa czasowa
gssDelegationCredential=Świadectwo przekazania uprawnień GSS

profileScopeConsentText=Profil użytkownika
emailScopeConsentText=Adres email
addressScopeConsentText=Adres
phoneScopeConsentText=Numer telefonu
offlineAccessScopeConsentText=Dostęp offline
samlRoleListScopeConsentText=Moje role
rolesScopeConsentText=Role użytkownika

restartLoginTooltip=Restart logowania

loginTotpIntro=Aby uzyskać dostęp do tego konta, musisz skonfigurować generator haseł jednorazowych
loginTotpStep1=Zainstaluj jedną z następujących aplikacji na telefonie komórkowym
loginTotpStep2=Otwórz aplikację i zeskanuj kod kreskowy
loginTotpStep3=Wprowadź jednorazowy kod podany przez aplikację i kliknij Prześlij aby zakończyć konfigurację
loginTotpManualStep2=Otwórz aplikację i wprowadź klucz
loginTotpManualStep3=Użyj poniższych wartości konfiguracji, jeśli aplikacja pozwala na ich ustawienie
loginTotpUnableToScan=Nie można skanować?
loginTotpScanBarcode=Zeskanować kod paskowy?
loginCredential=Poświadczenia
loginOtpOneTime=Kod jednorazowy
loginTotpType=Typ
loginTotpAlgorithm=Algorytm
loginTotpDigits=Cyfry
loginTotpInterval=Interwał
loginTotpCounter=Licznik
loginTotpDeviceName=Nazwa urządzenia

loginTotp.totp=Oparte o czas
loginTotp.hotp=Oparte o licznik

loginChooseAuthenticator=Wybierz metodę logowania

oauthGrantRequest=Czy przyznajesz te uprawnienia dostępu?
inResource=w

emailVerifyInstruction1=Została wysłana do Ciebie wiadomość e-mail z instrukcjami jak zweryfikować swój adres e-mail.
emailVerifyInstruction2=Nie otrzymałem kodu weryfikacyjnego w wiadomości e-mail?
emailVerifyInstruction3=aby ponownie wysłać wiadomość e-mail.

emailLinkIdpTitle=Link {0}
emailLinkIdp1=Wiadomość e-mail z instrukcjami, aby powiązać konto {0} {1} z kontem {2} została wysłana do Ciebie.
emailLinkIdp2=Nie otrzymałem kodu weryfikacyjnego w wiadomości e-mail?
emailLinkIdp3=aby ponownie wysłać wiadomość e-mail.
emailLinkIdp4=Jeśli już zweryfikowana e-mail w innej przeglądarce
emailLinkIdp5=aby kontynuować.

backToLogin=&laquo; Powrót do logowania

emailInstruction=Wpisz swój adres e-mail lub nazwę użytkownika a wyślemy do Ciebie instrukcje, jak utworzyć nowe hasło.

copyCodeInstruction=Proszę skopiować ten kod i wklej go do aplikacji\:

pageExpiredTitle=Strona wygasła
pageExpiredMsg1=Aby ponownie uruchomić proces logowania
pageExpiredMsg2=Aby kontynuować proces logowania

personalInfo=Informacje osobiste\:
role_admin=Admin
role_realm-admin=Strefa Admin
role_create-realm=Utwórz strefę
role_create-client=Utwórz klienta
role_view-realm=Wyświetl strefę
role_view-users=Wyświetl użytkowników
role_view-applications=Wyświetl aplikacje
role_view-clients=Wyświetl klientów
role_view-events=Wyświetl zdarzenia
role_view-identity-providers=Wyświetl dostawców tożsamości
role_manage-realm=Zarządzaj strefą
role_manage-users=Zarządzaj użytkownikami
role_manage-applications=Zarządzaj aplikacjami
role_manage-identity-providers=Zarządzaj dostawcami tożsamości
role_manage-clients=Zarządzaj klientami
role_manage-events=Zarządzaj zdarzeniami
role_view-profile=Zobacz profil
role_manage-account=Zarządzaj kontem
role_manage-account-links=Zarządzanie łączami konta
role_read-token=Odczytu tokenu
role_offline-access=Dostęp offline
client_account=Konto
client_account-console=Konsola konta
client_security-admin-console=Konsola administratora bezpieczeństwa
client_admin-cli=Admin CLI
client_realm-management=Zarządzanie strefą
client_broker=Broker

requiredFields=Wymagane pola

invalidUserMessage=Nieprawidłowa nazwa użytkownika lub hasło.
invalidUsernameMessage=Nieprawidłowa nazwa użytkownika.
invalidUsernameOrEmailMessage=Nieprawidłowa nazwa użytkownika lub hasło.
invalidPasswordMessage=Nieprawidłowe hasło.
invalidEmailMessage=Nieprawidłowy adres e-mail.
accountDisabledMessage=Konto jest wyłączone, skontaktuj się z administratorem.
accountTemporarilyDisabledMessage=Nieprawidłowa nazwa użytkownika lub hasło.
accountPermanentlyDisabledMessage=Nieprawidłowa nazwa użytkownika lub hasło.
accountTemporarilyDisabledMessageTotp=Nieprawidłowy kod uwierzytelnienia.
accountPermanentlyDisabledMessageTotp=Nieprawidłowy kod uwierzytelnienia.
expiredCodeMessage=Przekroczono limit czasu logowania. Proszę Zaloguj się ponownie.
expiredActionMessage=Akcja wygasła. Proszę kontynuować logowanie.
expiredActionTokenNoSessionMessage=Akcja wygasła.
expiredActionTokenSessionExistsMessage=Akcja wygasła. Proszę uruchomić ponownie.

missingFirstNameMessage=Proszę podać imię.
missingLastNameMessage=Proszę podać nazwisko.
missingEmailMessage=Proszę podać e-mail.
missingUsernameMessage=Proszę podać nazwę użytkownika.
missingPasswordMessage=Proszę podać hasło.
missingTotpMessage=Proszę podać kod uwierzytelniający.
missingTotpDeviceNameMessage=Proszę podać nazwę urządzenia.
notMatchPasswordMessage=Hasła nie są zgodne.

invalidPasswordExistingMessage=Nieprawidłowe istniejące hasło.
invalidPasswordBlacklistedMessage=Nieprawidłowe hasło\: hasło jest na czarnej liście.
invalidPasswordConfirmMessage=Potwierdzenie hasła nie pasuje.
invalidTotpMessage=Nieprawidłowy kod uwierzytelnienia.

usernameExistsMessage=Nazwa użytkownika już istnieje.
emailExistsMessage=Email już istnieje.

federatedIdentityExistsMessage=Użytkownik z {0} {1} już istnieje. Zaloguj się do zarządzania kontem aby połączyć konto.

confirmLinkIdpTitle=Konto już istnieje
federatedIdentityConfirmLinkMessage=Użytkownik z {0} {1} już istnieje. Co chcesz zrobić?
federatedIdentityConfirmReauthenticateMessage=Uwierzytelnij się aby połączyć swoje konto z {0}
nestedFirstBrokerFlowMessage=Użytkownik {0} {1} nie jest powiązany z żadnym znanym użytkownikiem.
confirmLinkIdpReviewProfile=Przejrzyj profil
confirmLinkIdpContinue=Dodaj do istniejącego konta

configureTotpMessage=Musisz skonfigurować Mobile Authenticator aby aktywować swoje konto.
updateProfileMessage=Musisz zaktualizować profilu użytkownika aby aktywować swoje konto.
updatePasswordMessage=Musisz zmienić swoje hasło aby aktywować swoje konto.
resetPasswordMessage=Musisz zmienić swoje hasło.
verifyEmailMessage=Musisz zweryfikować swój adres e-mail aby aktywować swoje konto.
linkIdpMessage=Musisz zweryfikować swój adres e-mail, aby połączyć swoje konto z {0}.

emailSentMessage=Powinieneś otrzymywać wkrótce pocztę z dalszymi instrukcjami.
emailSendErrorMessage=Nie można wysłać wiadomości e-mail, proszę spróbować ponownie później.

accountUpdatedMessage=Twoje konto zostało zaktualizowane.
accountPasswordUpdatedMessage=Twoje hasło zostało zaktualizowane.

delegationCompleteHeader=Logowanie udane
delegationCompleteMessage=Możesz zamknąć okno przeglądarki i przejść wstecz do aplikacji konsoli.
delegationFailedHeader=Logowanie nie powiodło się
delegationFailedMessage=Możesz zamknąć okno przeglądarki, wrócić do aplikacji konsoli i spróbować zalogować się ponownie.

noAccessMessage=Brak dostępu

invalidPasswordMinLengthMessage=Nieprawidłowe hasło\: minimalna długość {0}.
invalidPasswordMinDigitsMessage=Nieprawidłowe hasło\: musi zawierać przynajmniej {0} cyfr.
invalidPasswordMinLowerCaseCharsMessage=Nieprawidłowe hasło\: musi zawierać co najmniej {0} małych liter.
invalidPasswordMinUpperCaseCharsMessage=Nieprawidłowe hasło\: musi zawierać co najmniej {0} wielkich liter.
invalidPasswordMinSpecialCharsMessage=Nieprawidłowe hasło\: musi zawierać przynajmniej {0} znaków specjalnych.
invalidPasswordNotUsernameMessage=Nieprawidłowe hasło\: nie może być nazwą użytkownika.
invalidPasswordRegexPatternMessage=Nieprawidłowe hasło\: brak zgodności z wyrażeniem regularnym.
invalidPasswordHistoryMessage=Nieprawidłowe hasło\: nie może być takie jak {0} ostatnich haseł.
invalidPasswordGenericMessage=Nieprawidłowe hasło\: nowe hasło nie jest zgodne z zasadami haseł.

failedToProcessResponseMessage=Nie można przetworzyć odpowiedzi
httpsRequiredMessage=Wymagany HTTPS
realmNotEnabledMessage=Strefa nie jest aktywna
invalidRequestMessage=Nieprawidłowe żądanie
failedLogout=Wylogowanie nie powiodło się
unknownLoginRequesterMessage=Nieznany żądający logowania
loginRequesterNotEnabledMessage=Żądający logowania nie jest aktywny
bearerOnlyMessage=Klienci bearer-only nie mogą inicjować logowania przez przeglądarkę
standardFlowDisabledMessage=Klient nie może zainicjować logowania przez przeglądarkę z podanym response_type. Standardowy przepływ jest wyłączony dla klienta.
implicitFlowDisabledMessage=Klient nie może zainicjować logowania przez przeglądarkę z podanym response_type. Niejawny przepływ jest wyłączony dla klienta.
invalidRedirectUriMessage=Nieprawidłowy uri przekierowania
unsupportedNameIdFormatMessage=Nieobsługiwany NameIDFormat
invalidRequesterMessage=Nieprawidłowy żądający
registrationNotAllowedMessage=Rejestracja nie jest dozwolona
resetCredentialNotAllowedMessage=Zresetowanie poświadczeń nie jest dozwolone

permissionNotApprovedMessage=Uprawnienie nie zatwierdzone.
noRelayStateInResponseMessage=Brak przekazanego stanu w odpowiedzi dostawcy tożsamości.
insufficientPermissionMessage=Niewystarczające uprawnienia do łączenia tożsamości.
couldNotProceedWithAuthenticationRequestMessage=Nie można kontynuować żądania uwierzytelnienia do dostawcy tożsamości.
couldNotObtainTokenMessage=Nie można uzyskać tokenu od dostawcy tożsamości.
unexpectedErrorRetrievingTokenMessage=Nieoczekiwany błąd podczas pobierania tokenu od dostawcy tożsamości.
unexpectedErrorHandlingResponseMessage=Nieoczekiwany błąd podczas obsługi odpowiedzi od dostawcy tożsamości.
identityProviderAuthenticationFailedMessage=Uwierzytelnianie nie powiodło się. Nie można uwierzytelnić za pomocą dostawcy tożsamości.
couldNotSendAuthenticationRequestMessage=Nie może wysyłać żądania uwierzytelniania do dostawcy tożsamości.
unexpectedErrorHandlingRequestMessage=Nieoczekiwany błąd podczas obsługi żądania uwierzytelnienia do dostawcy tożsamości.
invalidAccessCodeMessage=Nieprawidłowy kod dostępu.
sessionNotActiveMessage=Sesja nie jest aktywna.
invalidCodeMessage=Wystąpił błąd, zaloguj się ponownie za pośrednictwem aplikacji.
identityProviderUnexpectedErrorMessage=Nieoczekiwany błąd podczas uwierzytelniania u dostawcy tożsamości
identityProviderNotFoundMessage=Nie można odnaleźć dostawcy tożsamości z tym identyfikatorem.
identityProviderLinkSuccess=Pomyślnie zweryfikowano e-mail. Wróć do oryginalnej przeglądarki i tam kontynuuj logowanie.
staleCodeMessage=Ta strona nie jest już ważna, proszę wrócić do aplikacji i zalogować się ponownie
realmSupportsNoCredentialsMessage=Strefa nie obsługuje dowolnego typu poświadczeń.
identityProviderNotUniqueMessage=Strefa obsługuje wielu dostawców tożsamości. Nie można określić dostawcy tożsamości, który powinien być używany do uwierzytelniania.
emailVerifiedMessage=Twój adres e-mail został zweryfikowany.
staleEmailVerificationLink=Użyto nieaktualny link stanu, który stracił ważność.  Może e-mail został już zweryfikowany?
identityProviderAlreadyLinkedMessage=Stowarzyszona tożsamość, zwrócona przez {0} jest już połączona z innym użytkownikiem.
confirmAccountLinking=Potwierdź powiązanie konta {0} dostawcy tożsamości {1} z twoim kontem.
confirmEmailAddressVerification=Potwierdź ważność adresu e-mail {0}.
confirmExecutionOfActions=Wykonaj następujące akcje

backToApplication=&laquo; Powrót do aplikacji
missingParameterMessage=Brakujące parametry\: {0}
clientNotFoundMessage=Klient nie znaleziony.
clientDisabledMessage=Klient nieaktywny.
invalidParameterMessage=Nieprawidłowy parametr\: {0}
alreadyLoggedIn=Jesteś już zalogowany.
differentUserAuthenticated=Jesteś już uwierzytelniona/y jako inny użytkownik ''{0}'' w tej sesji. Najpierw się wyloguj.
brokerLinkingSessionExpired=Żądano łączenia kont brokera, ale bieżąca sesja już jest nieważna.
proceedWithAction=&raquo; kliknij tutaj, aby przejść

requiredAction.CONFIGURE_TOTP=Skonfiguruj OTP
requiredAction.TERMS_AND_CONDITIONS=Regulamin
requiredAction.UPDATE_PASSWORD=Zaktualizuj hasło
requiredAction.UPDATE_PROFILE=Zaktualizuj profil
requiredAction.VERIFY_EMAIL=Zweryfikuj adres e-mail

doX509Login=Użytkownik będzie zalogowany jako\:
clientCertificate=X509 certyfikat klienta\:
noCertificate=[brak certyfikatu]


pageNotFound=Nie znaleziono strony
internalServerError=Wystąpił błąd wewnętrzny serwera

console-username=Nazwa użytkownika\:
console-password=Hasło\:
console-otp=Hasło jednorazowe\:
console-new-password=Nowe hasło\:
console-confirm-password=Potwierdź hasło\:
console-update-password=Aktualizacja hasła jest wymagana.
console-verify-email=Musisz zweryfikować swój adres e-mail.  Wiadomość e-mail z kodem weryfikacyjnym została wysłana do {0}. Podaj ten kod poniżej.
console-email-code=Kod z e-mail\:
console-accept-terms=Akceptujesz warunki? [t/n]\:
console-accept=t

readOnlyUsernameMessage=Zmiana nazwy użytkownika nie jest możliwa, ponieważ edycja konta jest zablokowana.
