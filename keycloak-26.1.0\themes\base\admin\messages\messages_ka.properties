invalidPasswordMinUpperCaseCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} დიდ ასოს.
invalidPasswordNotContainsUsernameMessage=არასწორი პაროლი: არ შეიძლება, მომხმარებლის სახელს შეიცავდეს.
invalidPasswordHistoryMessage=არასწორი პაროლი: არ უნდა უდრიდეს ბოლო {0} პაროლს.
invalidPasswordBlacklistedMessage=არასწორი პაროლი: პაროლი შავ სიაშია.
invalidPasswordGenericMessage=არასწორი პაროლი: ახალი პაროლი არ აკმაყოფილებს პაროლის პოლიტიკებს.
ldapErrorConnectionTimeoutNotNumber=მიერთების მოლოდინის ვადა რიცხვი უნდა იყოს
ldapErrorEditModeMandatory=ჩასწორების რეჟიმი აუცილებელია
ldapErrorReadTimeoutNotNumber=წაკითხვის მოლოდინის ვადა რიცხვი უნდა იყოს
ldapErrorInvalidCustomFilter=მომხმარებლის მორგებული LDAP ფილტრი არ იწყება სიმბოლოთი "(" და არ მთავრდება სიმბოლოთი ")".
invalidPasswordMinLengthMessage=არასწორი პაროლი: მინიმალური სიგრძეა {0}.
invalidPasswordMaxLengthMessage=არასწორი პაროლი: მაქსიმალური სიგრძეა {0}.
invalidPasswordMinLowerCaseCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} პატარა ასოს.
invalidPasswordMinDigitsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} ციფრს.
invalidPasswordMinSpecialCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} სპეციალურ სიმბოლოს.
invalidPasswordNotUsernameMessage=არასწორი პაროლი: არ უნდა იყოს მომხმარებლის სახელის ტოლი.
invalidPasswordNotEmailMessage=არასწორი პაროლი: არ უნდა უდრიდეს ელფოსტას.
invalidPasswordRegexPatternMessage=არასწორი პაროლი: არ ემთხვევა რეგგამოსის ნიმუშ(ებ)-ს.
ldapErrorMissingClientId=კლიენტის ID-ის მითითება აუცილებელია კონფიგურაციაში, როცა რეალმის როლების ასახვა არ გამოიყენება.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=ჯგუფის მემკვიდრეობითობის შენარჩუნება და UID წევრობის ტიპს ერთად ვერ გამოიყენებთ.
ldapErrorCantWriteOnlyForReadOnlyLdap=მხოლოდ, ჩაწერის დაყენება შეუძლებელია, როცა LDAP მომწოდებლის რეჟიმი WRITABLE არაა
ldapErrorCantEnableStartTlsAndConnectionPooling=ორივეს, StartTLS და მიერთების პულინგს ერთდროულად ვერ გამოიყენებთ.
ldapErrorCantEnableUnsyncedAndImportOff=მომხმარებლის შემოტანის გათიშვა შეუძლებელია, როცა LDAP მომწოდებლის ტიპი წარმოადგენს: UNSYNCED
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=პაროლების პოლიტიკის გადამოწმება, მხოლოდ, WRITABLE ჩასწორების რეჟიმში შეიძლება
clientRedirectURIsFragmentError=გადასამისამართებელი URI-ები URI ფრაგმენტს არ უნდა შეიცავდეს
clientRootURLIllegalSchemeError=ძირითადი URL არასწორ სქემას იყენებს
backchannelLogoutUrlIllegalSchemeError=Backchannel-ის გასვლის URL არასწორ სქემას იყენებს
clientRedirectURIsIllegalSchemeError=გადამისამართების URI არასწორ სქემას იყენებს
clientBaseURLInvalid=საბაზისო URL სწორი URL არაა
clientRootURLInvalid=საწყისი URL სწორი URL არაა
clientRedirectURIsInvalid=გადამისამართების URI სწორი URI არაა
backchannelLogoutUrlIsInvalid=უკანა არხის გასვლის URL სწორი URL არაა


pairwiseMalformedClientRedirectURI=კლიენტი არასწორ გადამისამართების URI-ს შეიცავდა.
pairwiseClientRedirectURIsMissingHost=კლიენტის გადამისამართების URI-ები სწორ ჰოსტის კომპონენტს უნდა შეიცავდეს.
pairwiseRedirectURIsMismatch=კლიენტის გადამისამართების URI-ები არ ემთხვევა გადამისამართების URI-ებს, რომელიც სექტორის იდენტიფიკატორის URI-დან მივიღე.
error-invalid-value=არასწორი მნიშვნელობა.
error-invalid-blank=შეიყვანეთ მნიშვნელობა.
error-empty=შეიყვანეთ მნიშვნელობა.
error-invalid-length-too-short=ატრიბუტის {0} მინიმალური სიგრძეა {1}.
error-invalid-email=არასწორი ელფოსტის მისამართი.
error-number-out-of-range-too-big=ატრიბუტის {0} მაქსიმალური მნიშვნელობაა {2}.
error-pattern-no-match=არასწორი მნიშვნელობა.
error-person-name-invalid-character={0} არასწორ სიმბოლოს შეიცავს.
ldapErrorCantWriteOnlyAndReadOnly=მხოლოდ-ჩაწერად და მხოლოდ-წაკითხვად ალმებს ერთდროულად ვერ დააყენებთ
ldapErrorMissingGroupsPathGroup=ჯგუფების ბილიკის ჯგუფი არ არსებობს - ჯერ მითითებულ ბილიკზე ჯგუფი შექმენით
clientBaseURLIllegalSchemeError=ძირითადი URL არასწორ სქემას იყენებს
clientRootURLFragmentError=ძირითადი URL-ები URL-ის ფრაგმენტს არ უნდა შეიცავდნენ
error-invalid-length-too-long=ატრიბუტის {0} მაქსიმალური სიგრძეა {2}.
error-invalid-number=არასწორი რიცხვი.
error-number-out-of-range=ატრიბუტი {0} უნდა იყოს რიცხვი შუალედიდან {1}-{2}.
error-invalid-date=ატრიბუტი {0} არასწორი თარიღია.
error-invalid-uri-scheme=არასწორი ბმულის სქემა.
error-number-out-of-range-too-small=ატრიბუტის {0} მინიმალური სიგრძე {1} შეიძლება იყოს.
error-invalid-uri=არასწორი URL.
error-invalid-uri-fragment=არასწორი URL-ის ფრაგმენტი.
error-user-attribute-required=მიუთითეთ ატრიბუტი {0}.
error-user-attribute-read-only=ატრიბუტი {0} მხოლოდ-წაკითხვადია.
error-username-invalid-character={0} არასწორ სიმბოლოს შეიცავს.
pairwiseMalformedSectorIdentifierURI=არასწორად ჩამოყალიბებული სექტორის იდენტიფიკატორის URI.
pairwiseFailedToGetRedirectURIs=შეცდომა გადამისამართების URI-ების მიღებისას სექტორის იდენტიფიკატორის URI-დან.
error-invalid-multivalued-size=ატრიბუტი {0} მინიმუმ {1} და მაქსიმუმ {2} მნიშვნელობას უნდა შეიცავდეს.
error-invalid-length=ატრიბუტის {0} სიგრძე {1}-{2} შუალედში უნდა იყოს.
pairwiseClientRedirectURIsMultipleHosts=მორგებული სექტორის იდენტიფიკატორის URI-ის გარდა კლიენტის გადამისამართების URI-ები ერთზე მეტ ჰოსტის კომპონენტს არ უნდა შეიცავდეს.
duplicatedJwksSettings=პარამეტრები "JWKS-ის გამოყენება" და პარამეტრი "JWKS-ის URL-ის გამოყენება" ერთდროულად ჩართული ვერ იქნება.
