emailVerificationSubject=Eメールの確認
emailVerificationBody=このメールアドレスで{2}アカウントが作成されました。以下のリンクをクリックしてメールアドレスの確認を完了してください。\n\n{0}\n\nこのリンクは{3}だけ有効です。\n\nもしこのアカウントの作成に心当たりがない場合は、このメールを無視してください。
emailVerificationBodyHtml=<p>このメールアドレスで{2}アカウントが作成されました。以下のリンクをクリックしてメールアドレスの確認を完了してください。</p><p><a href="{0}">メールアドレスの確認</a></p><p>このリンクは{3}だけ有効です。</p><p>もしこのアカウントの作成に心当たりがない場合は、このメールを無視してください。</p>
emailTestSubject=[KEYCLOAK] - SMTPテストメッセージ
emailTestBody=これはテストメッセージです
emailTestBodyHtml=<p>これはテストメッセージです</p>
identityProviderLinkSubject=リンク {0}
identityProviderLinkBody=あなたの"{1}"アカウントと{2}ユーザーの"{0}"アカウントのリンクが要求されました。以下のリンクをクリックしてアカウントのリンクを行ってください。\n\n{3}\n\nこのリンクは{5}だけ有効です。\n\nもしアカウントのリンクを行わない場合は、このメッセージを無視してください。アカウントのリンクを行うことで、{0}経由で{1}にログインすることができるようになります。
identityProviderLinkBodyHtml=<p>あなたの<b>{1}</b>アカウントと{2}ユーザーの<b>{0}</b>アカウントのリンクが要求されました。以下のリンクをクリックしてアカウントのリンクを行ってください。</p><p><a href="{3}">アカウントリンクの確認</a></p><p>このリンクは{5}だけ有効です。</p><p>もしアカウントのリンクを行わない場合は、このメッセージを無視してください。アカウントのリンクを行うことで、{0}経由で{1}にログインすることができるようになります。</p>
passwordResetSubject=パスワードのリセット
passwordResetBody=あなたの{2}アカウントのパスワードの変更が要求されています。以下のリンクをクリックしてパスワードのリセットを行ってください。\n\n{0}\n\nこのリンクは{3}だけ有効です。\n\nもしパスワードのリセットを行わない場合は、このメッセージを無視してください。何も変更されません。
passwordResetBodyHtml=<p>あなたの{2}アカウントのパスワードの変更が要求されています。以下のリンクをクリックしてパスワードのリセットを行ってください。</p><p><a href="{0}">パスワードのリセット</a></p><p>このリンクは{3}だけ有効です。</p><p>もしパスワードのリセットを行わない場合は、このメッセージを無視してください。何も変更されません。</p>
executeActionsSubject=アカウントの更新
executeActionsBody=次のアクションを実行することにより、管理者よりあなたの{2}アカウントの更新が要求されています: {3}。以下のリンクをクリックしてこのプロセスを開始してください。\n\n{0}\n\nこのリンクは{4}だけ有効です。\n\n管理者からのこの変更要求についてご存知ない場合は、このメッセージを無視してください。何も変更されません。
executeActionsBodyHtml=<p>次のアクションを実行することにより、管理者よりあなたの{2}アカウントの更新が要求されています: {3}。以下のリンクをクリックしてこのプロセスを開始してください。</p><p><a href="{0}">アカウントの更新</a></p><p>このリンクは{4}だけ有効です。</p><p>管理者からのこの変更要求についてご存知ない場合は、このメッセージを無視してください。何も変更されません。</p>
eventLoginErrorSubject=ログインエラー
eventLoginErrorBody={0}に{1}からのログイン失敗があなたのアカウントで検出されました。心当たりがない場合は、管理者に連絡してください。
eventLoginErrorBodyHtml=<p>{0}に{1}からのログイン失敗があなたのアカウントで検出されました。心当たりがない場合は管理者に連絡してください。</p>
eventRemoveTotpSubject=OTPの削除
eventRemoveTotpBody={0}に{1}からの操作でOTPが削除されました。心当たりがない場合は、管理者に連絡してください。
eventRemoveTotpBodyHtml=<p>{0}に{1}からの操作でOTPが削除されました。心当たりがない場合は、管理者に連絡してください。</p>
eventUpdatePasswordSubject=パスワードの更新
eventUpdatePasswordBody={0}に{1}からの操作であなたのパスワードが変更されました。心当たりがない場合は、管理者に連絡してください。
eventUpdatePasswordBodyHtml=<p>{0}に{1}からの操作であなたのパスワードが変更されました。心当たりがない場合は、管理者に連絡してください。</p>
eventUpdateTotpSubject=OTPの更新
eventUpdateTotpBody={0}に{1}からの操作でOTPが更新されました。心当たりがない場合は、管理者に連絡してください。
eventUpdateTotpBodyHtml=<p>{0}に{1}からの操作でOTPが更新されました。心当たりがない場合は、管理者に連絡してください。</p>
requiredAction.CONFIGURE_TOTP=OTPの設定
requiredAction.TERMS_AND_CONDITIONS=利用規約
requiredAction.UPDATE_PASSWORD=パスワードの更新
requiredAction.UPDATE_PROFILE=プロファイルの更新
requiredAction.VERIFY_EMAIL=Eメールの確認

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=秒
linkExpirationFormatter.timePeriodUnit.minutes=分
linkExpirationFormatter.timePeriodUnit.hours=時間
linkExpirationFormatter.timePeriodUnit.days=日
emailVerificationBodyCode=次のコードを入力してメールアドレスを確認してください。\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>次のコードを入力してメールアドレスを確認してください。</p><p><b>{0}</b></p>
orgInviteSubject=組織 {0} への招待
