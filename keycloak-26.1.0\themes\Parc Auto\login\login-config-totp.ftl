<#import "template.ftl" as layout>
<#import "password-commons.ftl" as passwordCommons>
<@layout.registrationLayout displayRequiredFields=false displayMessage=!messagesPerField.existsError('totp','userLabel'); section>

    <#if section = "header">


    <#elseif section = "form">

        <#-- Section principale du formulaire -->
        <div id="kc-form">
            <div id="kc-form-wrapper">
                <#-- En-tête avec logo et titre -->
                <img src="${url.resourcesPath}/img/OTBS.png" alt="Onetech Logo" id="logo"><br>
                <span id="title">Configuration de l'authentification à deux facteurs</span>

                <#-- Liste des étapes de configuration TOTP -->
                <ol id="kc-totp-settings">
                    <#-- Étape 1: Installation de l'application -->
                    <li>
                        <p>Installez une application d'authentification</p>
                    </li>

                    <#-- Configuration manuelle ou par QR code -->
                    <#if mode?? && mode = "manual">
                        <#-- Mode manuel: Affichage de la clé secrète -->
                        <li>
                            <p>${msg("loginTotpManualStep2")}</p>
                            <p><span>${totp.totpSecretEncoded}</span></p>
                            <p><a href="${totp.qrUrl}" id="mode-barcode">${msg("loginTotpScanBarcode")}</a></p>
                        </li>
                        <#-- Détails de configuration TOTP -->
                        <li>
                            <p>${msg("loginTotpManualStep3")}</p>
                            <div>
                                <ul>
                                    <#-- Paramètres techniques de l'authentification TOTP -->
                                    <li id="kc-totp-type">${msg("loginTotpType")}: ${msg("loginTotp." + totp.policy.type)}</li>
                                    <li id="kc-totp-algorithm">${msg("loginTotpAlgorithm")}: ${totp.policy.getAlgorithmKey()}</li>
                                    <li id="kc-totp-digits">${msg("loginTotpDigits")}: ${totp.policy.digits}</li>
                                    <#if totp.policy.type = "totp">
                                        <li id="kc-totp-period">${msg("loginTotpInterval")}: ${totp.policy.period}</li>
                                    <#elseif totp.policy.type = "hotp">
                                        <li id="kc-totp-counter">${msg("loginTotpCounter")}: ${totp.policy.initialCounter}</li>
                                    </#if>
                                </ul>
                            </div>
                        </li>
                    <#else>
                        <#-- Mode QR Code: Affichage du code QR -->
                        <li>
                            <p>${msg("loginTotpStep2")}</p>
                            <img id="kc-totp-secret-qr-code" src="data:image/png;base64, ${totp.totpSecretQrCode}" alt="Figure: Barcode"><br/>
                            <p><a href="${totp.manualUrl}" id="mode-manual">${msg("loginTotpUnableToScan")}</a></p>
                        </li>
                    </#if>
                    <#-- Étape finale: Nom du périphérique -->
                    <li>
                        <p>${msg("loginTotpStep3")}</p>
                        <p>${msg("loginTotpStep3DeviceName")}</p>
                    </li>
                </ol>

                <#-- Formulaire de configuration -->
                <form action="${url.loginAction}" class="${properties.kcFormClass!}" id="kc-totp-settings-form" method="post">
                    <#-- Champ pour le code d'authentification -->
                    <div class="${properties.kcFormGroupClass!}">
                        <div class="${properties.kcInputWrapperClass!}">
                            <label for="totp" class="control-label">${msg("authenticatorCode")}</label> <span class="required">*</span>
                        </div>
                        <div class="${properties.kcInputWrapperClass!}">
                            <input type="text" 
                                   id="totp" 
                                   name="totp" 
                                   autocomplete="off" 
                                   class="${properties.kcInputClass!}"
                                   aria-invalid="<#if messagesPerField.existsError('totp')>true</#if>"
                                   dir="ltr"
                            />

                            <#-- Message d'erreur pour le code TOTP -->
                            <#if messagesPerField.existsError('totp')>
                                <span id="input-error-otp-code" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('totp'))?no_esc}
                                </span>
                            </#if>
                        </div>

                        <#-- Champs cachés pour la configuration -->
                        <input type="hidden" id="totpSecret" name="totpSecret" value="${totp.totpSecret}" />
                        <#if mode??><input type="hidden" id="mode" name="mode" value="${mode}"/></#if>
                    </div>

                    <#-- Champ pour le nom du périphérique -->
                    <div class="${properties.kcFormGroupClass!}">
                        <div class="${properties.kcInputWrapperClass!}">
                            <label for="userLabel" class="control-label">${msg("loginTotpDeviceName")}</label> 
                            <#if totp.otpCredentials?size gte 1><span class="required">*</span></#if>
                        </div>

                        <div class="${properties.kcInputWrapperClass!}">
                            <input type="text" 
                                   class="${properties.kcInputClass!}" 
                                   id="userLabel" 
                                   name="userLabel" 
                                   autocomplete="off"
                                   aria-invalid="<#if messagesPerField.existsError('userLabel')>true</#if>" 
                                   dir="ltr"
                            />

                            <#-- Message d'erreur pour le nom du périphérique -->
                            <#if messagesPerField.existsError('userLabel')>
                                <span id="input-error-otp-label" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('userLabel'))?no_esc}
                                </span>
                            </#if>
                        </div>
                    </div>

                    <#-- Boutons de soumission et d'annulation -->
                    <div style="text-align: center; margin-top: 20px;">
                        <#if isAppInitiatedAction??>
                            <input type="submit"
                                   class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonLargeClass!}"
                                   id="saveTOTPBtn" 
                                   value="${msg("doSubmit")}"
                                   style="margin: 0 10px;"
                            />
                            <button type="submit"
                                    class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonLargeClass!} ${properties.kcButtonLargeClass!}"
                                    id="cancelTOTPBtn1" 
                                    name="cancel-aia" 
                                    value="true"
                                    style="margin: 0 10px;"
                            >
                                ${msg("doCancel")}
                            </button>
                        <#else>
                            <input type="submit"
                                   class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonBlockClass!} ${properties.kcButtonLargeClass!}"
                                   id="saveTOTPBtn" 
                                   value="${msg("doSubmit")}"
                                   style="margin: 0 10px;"
                            />
                        </#if>
                    </div>
                </form>
            </div>
        </div>

    </#if>

</@layout.registrationLayout>
