package otbs.ms_notification.client.keycloak;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * Service pour interagir avec Keycloak.
 * Utilise le Keycloak Admin Client pour récupérer les données utilisateur.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KeycloakService {

    private final Keycloak keycloak;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUri;

    private String getTargetRealm() {
        return issuerUri.substring(issuerUri.lastIndexOf("/") + 1);
    }

    @Cacheable("users")
    public List<UserDTO> getAllUsers() {
        try {
            String realm = getTargetRealm();
            UsersResource usersResource = keycloak.realm(realm).users();
            List<UserRepresentation> users = usersResource.list();

            return users.stream()
                    .map(user -> mapToUserDTOWithRoles(user, realm))
                    .toList();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des utilisateurs depuis Keycloak: {}", e.getMessage());
            return createMockUsers();
        }
    }

    @Cacheable(value = "users", key = "#id")
    public Optional<UserDTO> getUserById(String id) {
        try {
            String realm = getTargetRealm();
            UserResource userResource = keycloak.realm(realm).users().get(id);
            UserRepresentation userRepresentation = userResource.toRepresentation();

            if (userRepresentation != null) {
                return Optional.of(mapToUserDTOWithRoles(userRepresentation, realm));
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération de l'utilisateur {}: {}", id, e.getMessage());
            return Optional.empty();
        }
    }

    @Cacheable("userSelectors")
    public List<UserSelectorOptionDTO> getUsersForSelector() {
        return getAllUsers().stream()
                .filter(UserDTO::isEnabled)
                .map(user -> UserSelectorOptionDTO.builder()
                        .id(user.getId())
                        .fullName(user.getFullName())
                        .email(user.getEmail())
                        .build())
                .toList();
    }

    @Cacheable(value = "users", key = "'search_name_' + #name")
    public List<UserDTO> searchUsersByName(String name) {
        try {
            UsersResource usersResource = keycloak.realm(getTargetRealm()).users();
            List<UserRepresentation> usersByUsername = usersResource.search(name);
            List<UserRepresentation> usersByFirstName = usersResource.searchByFirstName(name, true);
            List<UserRepresentation> usersByLastName = usersResource.searchByLastName(name, true);

            String realm = getTargetRealm();
            return Stream.of(usersByUsername, usersByFirstName, usersByLastName)
                    .flatMap(List::stream)
                    .distinct()
                    .map(user -> mapToUserDTOWithRoles(user, realm))
                    .toList();
        } catch (Exception e) {
            log.error("Erreur lors de la recherche d'utilisateurs par nom {}: {}", name, e.getMessage());
            return getAllUsers().stream()
                    .filter(user -> matchesName(user, name))
                    .toList();
        }
    }

    @Cacheable(value = "users", key = "'search_email_' + #email")
    public List<UserDTO> searchUsersByEmail(String email) {
        try {
            UsersResource usersResource = keycloak.realm(getTargetRealm()).users();
            List<UserRepresentation> usersByEmail = usersResource.searchByEmail(email, true);

            String realm = getTargetRealm();
            return usersByEmail.stream()
                    .map(user -> mapToUserDTOWithRoles(user, realm))
                    .toList();
        } catch (Exception e) {
            log.error("Erreur lors de la recherche d'utilisateurs par email {}: {}", email, e.getMessage());
            return getAllUsers().stream()
                    .filter(user -> user.getEmail() != null &&
                                  user.getEmail().toLowerCase().contains(email.toLowerCase()))
                    .toList();
        }
    }

    private boolean matchesName(UserDTO user, String name) {
        String lowerName = name.toLowerCase();
        return (user.getUsername() != null && user.getUsername().toLowerCase().contains(lowerName)) ||
               (user.getFirstName() != null && user.getFirstName().toLowerCase().contains(lowerName)) ||
               (user.getLastName() != null && user.getLastName().toLowerCase().contains(lowerName)) ||
               (user.getFullName().toLowerCase().contains(lowerName));
    }

    public boolean userExists(String id) {
        return getUserById(id).isPresent();
    }

    private List<UserDTO> createMockUsers() {
        
        return Arrays.asList(
                UserDTO.builder()
                        .id("mock-user-1")
                        .username("testuser1")
                        .email("<EMAIL>")
                        .firstName("Test")
                        .lastName("User 1")
                        .enabled(true)
                        .emailVerified(true)
                        .createdTimestamp(LocalDateTime.now())
                        .realmRoles(List.of("user"))
                        .clientRoles(List.of())
                        .build(),
                
                UserDTO.builder()
                        .id("mock-user-2")
                        .username("testuser2")
                        .email("<EMAIL>")
                        .firstName("Test")
                        .lastName("User 2")
                        .enabled(true)
                        .emailVerified(true)
                        .createdTimestamp(LocalDateTime.now())
                        .realmRoles(List.of("user"))
                        .clientRoles(List.of())
                        .build(),
                
                UserDTO.builder()
                        .id("admin-user")
                        .username("admin")
                        .email("<EMAIL>")
                        .firstName("Admin")
                        .lastName("User")
                        .enabled(true)
                        .emailVerified(true)
                        .createdTimestamp(LocalDateTime.now())
                        .realmRoles(List.of("admin", "user"))
                        .clientRoles(List.of("admin"))
                        .build()
        );
    }

    private UserDTO mapToUserDTOWithRoles(UserRepresentation userRepresentation, String realm) {
        List<String> realmRoles = keycloak.realm(realm)
                .users()
                .get(userRepresentation.getId())
                .roles()
                .realmLevel()
                .listEffective()
                .stream()
                .map(RoleRepresentation::getName)
                .toList();

        List<String> clientRoles = new ArrayList<>();
        try {
            clientRoles = keycloak.realm(realm)
                    .users()
                    .get(userRepresentation.getId())
                    .roles()
                    .getAll()
                    .getClientMappings()
                    .values()
                    .stream()
                    .flatMap(mapping -> mapping.getMappings().stream())
                    .map(RoleRepresentation::getName)
                    .distinct()
                    .toList();
        } catch (Exception e) {
            log.debug("Impossible de récupérer les rôles client pour {}: {}", 
                    userRepresentation.getUsername(), e.getMessage());
        }

        return mapToUserDTO(userRepresentation, realmRoles, clientRoles);
    }

    private UserDTO mapToUserDTO(UserRepresentation userRepresentation, List<String> realmRoles, List<String> clientRoles) {
        LocalDateTime createdTimestamp = null;
        if (userRepresentation.getCreatedTimestamp() != null) {
            createdTimestamp = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(userRepresentation.getCreatedTimestamp()),
                    ZoneId.systemDefault()
            );
        }

        return UserDTO.builder()
                .id(userRepresentation.getId())
                .username(userRepresentation.getUsername())
                .email(userRepresentation.getEmail())
                .firstName(userRepresentation.getFirstName())
                .lastName(userRepresentation.getLastName())
                .enabled(userRepresentation.isEnabled())
                .emailVerified(userRepresentation.isEmailVerified())
                .createdTimestamp(createdTimestamp)
                .attributes(userRepresentation.getAttributes())
                .realmRoles(realmRoles != null ? realmRoles : List.of())
                .clientRoles(clientRoles != null ? clientRoles : List.of())
                .build();
    }
} 