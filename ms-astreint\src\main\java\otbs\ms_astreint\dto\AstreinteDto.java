package otbs.ms_astreint.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import otbs.ms_astreint.model.NiveauAstreinte;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AstreinteDto {
    private Long id;
    private LocalDateTime dateDebut;
    private LocalDateTime dateFin;
    private String description;
    private List<ConsultantDto> consultants = new ArrayList<>();
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConsultantDto {
        private Long id;
        private String consultant;       
        private String consultantName;    
        private NiveauAstreinte niveau;
}
} 