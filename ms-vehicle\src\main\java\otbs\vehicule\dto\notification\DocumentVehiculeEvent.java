package otbs.vehicule.dto.notification;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Événement spécifique aux missions.
 * Hérite de NotificationEvent et ajoute des informations spécifiques aux missions.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DocumentVehiculeEvent extends NotificationEvent implements Serializable {

    private Long documentVehiculeId;
    private Long vehiculeId;
    private String type;

    public DocumentVehiculeEvent(String eventType, Long documentVehiculeId, Long vehiculeId, String type, String titre, String message) {
        super(eventType, "ms-vehicule", titre, message);
        this.documentVehiculeId = documentVehiculeId;
        this.vehiculeId = vehiculeId;
        this.type = type;
        this.setEntiteLieeType("documentVehicule");
        this.setEntiteLieeId(documentVehiculeId);
        if(type.equals("VisiteTechnique")){
            this.setUrlAction("/vehicules/details/" + vehiculeId + "/visiteTechnique" );
        } else if ( type.equals("Assurance")) {
            this.setUrlAction("/vehicules/details/" + vehiculeId + "/assurance" );
        }else {
            this.setUrlAction("/vehicules/details/" + vehiculeId + "/vinette" );
        }
    }
}
