package otbs.vehicule.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import otbs.vehicule.model.DocumentVehicule;
import otbs.vehicule.model.Vehicule;
import otbs.vehicule.model.VisiteTechnique;
import otbs.vehicule.model.enums.Resultat;

import java.util.List;

@Repository
public interface VisiteTechniqueRepo extends JpaRepository<VisiteTechnique, Long> {
    int countByVehiculeAndResultat(Vehicule vehicule, Resultat resultat);

    List<DocumentVehicule> findByResultat(Resultat resultat);
}
