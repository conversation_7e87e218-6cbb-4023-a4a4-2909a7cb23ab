doSave=Salva
doCancel=Annulla
doLogOutAllSessions=Effettua il logout da tutte le sessioni
doRemove=Elimina
doAdd=Aggiungi
doSignOut=Esci
doLogIn=Log In
doLink=Link

personalInfoSidebarTitle=Informazioni personali
accountSecuritySidebarTitle=Sicurezza dell''account
signingInSidebarTitle=Impostazioni di accesso
deviceActivitySidebarTitle=Attività del dispositivo
linkedAccountsSidebarTitle=Account collegati

editAccountHtmlTitle=Modifica Account
personalInfoHtmlTitle=Informazioni personali
federatedIdentitiesHtmlTitle=Identità federate
accountLogHtmlTitle=Log dell''account
changePasswordHtmlTitle=Cambia password
deviceActivityHtmlTitle=Attività dei dispositivi
sessionsHtmlTitle=Sessioni
accountManagementTitle=Gestione degli account di Keycloak
authenticatorTitle=Autenticatore
applicationsHtmlTitle=Applicazioni
linkedAccountsHtmlTitle=Account collegati

accountManagementWelcomeMessage=Benvenuto nella gestione degli account di Keycloak
personalInfoIntroMessage=Gestisci le tue informazioni di base
accountSecurityTitle=Sicurezza dell''account
accountSecurityIntroMessage=Controlla la tua password e gli accessi dell''account
applicationsIntroMessage=Traccia e gestisci i permessi delle applicazioni nell''accesso al tuo account
resourceIntroMessage=Condividi le tue risorse tra i membri del team
passwordLastUpdateMessage=La tua password è stata aggiornata il
updatePasswordTitle=Aggiornamento password
updatePasswordMessageTitle=Assicurati di scegliere una password robusta
updatePasswordMessage=Una password robusta contiene un misto di numeri, lettere, e simboli. È difficile da indovinare, non assomiglia a una parola reale, ed è utilizzata solo per questo account.
personalSubTitle=Le tue informazioni personali
personalSubMessage=Gestisce queste informazioni di base: il tuo nome, cognome, e indirizzo email

authenticatorCode=Codice monouso

email=Email
firstName=Nome
givenName=Nome
fullName=Nome completo
lastName=Cognome
familyName=Cognome
password=Password
currentPassword=Password attuale
passwordConfirm=Conferma password
passwordNew=Nuova password
username=Username
address=Indirizzo
street=Via
locality=Città o località
region=Stato, Provincia, o Regione
postal_code=CAP
country=Paese
emailVerified=Email verificata
gssDelegationCredential=Credenziali delega GSS

profileScopeConsentText=Profilo utente
emailScopeConsentText=Indirizzo email
addressScopeConsentText=Indirizzo
phoneScopeConsentText=Numero di telefono
offlineAccessScopeConsentText=Accesso offline
samlRoleListScopeConsentText=I miei ruoli
rolesScopeConsentText=Ruoli utente
role_admin=Admin
role_realm-admin=Realm admin
role_create-realm=Crea realm
role_view-realm=Visualizza realm
role_view-users=Visualizza utenti
role_view-applications=Visualizza applicazioni
role_view-clients=Visualizza client
role_view-events=Visualizza eventi
role_view-identity-providers=Visualizza identity provider
role_view-consent=Visualizza consensi
role_manage-realm=Gestisci realm
role_manage-users=Gestisci utenti
role_manage-applications=Gestisci applicazioni
role_manage-identity-providers=Gestisci identity provider
role_manage-clients=Gestisci client
role_manage-events=Gestisci eventi
role_view-profile=Visualizza profilo
role_manage-account=Gestisci account
role_manage-account-links=Gestisci i link dell''account
role_manage-consent=Gestisci consensi
role_read-token=Leggi token
role_offline-access=Accesso offline
role_uma_authorization=Ottieni permessi
client_account=Account
client_account-console=Console account
client_security-admin-console=Console di amministrazione di sicurezza
client_admin-cli=Admin CLI
client_realm-management=Gestione realm
client_broker=Broker


requiredFields=Campi obbligatori
allFieldsRequired=Tutti campi obbligatori

backToApplication=&laquo; Torna all''applicazione
backTo=Torna a {0}

date=Data
event=Evento
ip=IP
client=Client
clients=Client
details=Dettagli
started=Iniziato
lastAccess=Ultimo accesso
expires=Scade
applications=Applicazioni

account=Account
federatedIdentity=Identità federate
authenticator=Autenticatore
device-activity=Attività dei dispositivi
sessions=Sessioni
log=Log

application=Applicazione
availablePermissions=Autorizzazioni disponibili
grantedPermissions=Autorizzazioni concesse
grantedPersonalInfo=Informazioni personali concesse
additionalGrants=Ulteriori concessioni
action=Azione
inResource=in
fullAccess=Accesso completo
offlineToken=Token offline
revoke=Revoca concessione

configureAuthenticators=Autenticatori configurati
mobile=Dispositivo mobile
totpStep1=Installa una delle seguenti applicazioni sul tuo dispositivo mobile
totpStep2=Apri l''applicazione e scansiona il codice QR
totpStep3=Scrivi il codice monouso fornito dall''applicazione e clicca Salva per completare il setup.
totpStep3DeviceName=Fornisci il nome del dispositivo per aiutarti a gestire i dispositivi di autenticazione.

totpManualStep2=Apri l''applicazione e scrivi la chiave
totpManualStep3=Usa le seguenti impostazioni se l''applicazione lo consente
totpUnableToScan=Non riesci a scansionare il codice QR?
totpScanBarcode=Vuoi scansionare il codice QR?

totp.totp=Basato sull''ora
totp.hotp=Basato sul contatore

totpType=Tipo
totpAlgorithm=Algoritmo
totpDigits=Cifre
totpInterval=Intervallo
totpCounter=Contatore
totpDeviceName=Nome dispositivo

missingUsernameMessage=Inserisci lo username.
missingFirstNameMessage=Inserisci il nome.
invalidEmailMessage=Indirizzo email non valido.
missingLastNameMessage=Inserisci il cognome.
missingEmailMessage=Inserisci l''indirizzo email.
missingPasswordMessage=Inserisci la password.
notMatchPasswordMessage=Le password non coincidono.
invalidUserMessage=Utente non valido

missingTotpMessage=Inserisci il codice di autenticazione.
missingTotpDeviceNameMessage=Inserisci il nome del dispositivo di autenticazione.
invalidPasswordExistingMessage=Password esistente non valida.
invalidPasswordConfirmMessage=La password di conferma non coincide.
invalidTotpMessage=Codice di autenticazione non valido.

usernameExistsMessage=Username già esistente.
emailExistsMessage=Email già esistente.

readOnlyUserMessage=Non puoi aggiornare il tuo account poiché è in modalità sola lettura.
readOnlyUsernameMessage=Non puoi aggiornare il tuo nome utente poiché è in modalità sola lettura.
readOnlyPasswordMessage=Non puoi aggiornare il tuo account poiché è in modalità sola lettura.

successTotpMessage=Autenticatore mobile configurato.
successTotpRemovedMessage=Autenticatore mobile eliminato.

successGrantRevokedMessage=Concessione revocata con successo.

accountUpdatedMessage=Il tuo account è stato aggiornato.
accountPasswordUpdatedMessage=La tua password è stata aggiornata.

missingIdentityProviderMessage=Identity provider non specificato.
invalidFederatedIdentityActionMessage=Azione non valida o mancante.
identityProviderNotFoundMessage=L''identity provider specificato non è stato trovato.
federatedIdentityLinkNotActiveMessage=Questo identity non è più attivo.
federatedIdentityRemovingLastProviderMessage=Non puoi rimuovere l''ultima identità federata poiché non hai più la password.
identityProviderRedirectErrorMessage=Il reindirizzamento all''identity provider è fallito.
identityProviderRemovedMessage=Identity provider eliminato correttamente.
identityProviderAlreadyLinkedMessage=L''identità federata restituita da {0} è già collegata ad un altro utente.
staleCodeAccountMessage=La pagina è scaduta. Prova di nuovo.
consentDenied=Consenso negato.

accountDisabledMessage=Account disabilitato, contatta l''amministratore.

accountTemporarilyDisabledMessage=L''account è temporaneamente disabilitato, contatta l''amministratore o riprova più tardi.
invalidPasswordMinLengthMessage=Password non valida: lunghezza minima {0}.
invalidPasswordMinLowerCaseCharsMessage=Password non valida: deve contenere almeno {0} caratteri minuscoli.
invalidPasswordMinDigitsMessage=Password non valida: deve contenere almeno {0} numeri.
invalidPasswordMinUpperCaseCharsMessage=Password non valida: deve contenere almeno {0} caratteri maiuscoli.
invalidPasswordMinSpecialCharsMessage=Password non valida: deve contenere almeno {0} caratteri speciali.
invalidPasswordNotUsernameMessage=Password non valida: non deve essere uguale allo username.
invalidPasswordRegexPatternMessage=Password non valida: fallito il match con una o più espressioni regolari.
invalidPasswordHistoryMessage=Password non valida: non deve essere uguale a una delle ultime {0} password.
invalidPasswordBlacklistedMessage=Password non valida: la password non è consentita.
invalidPasswordGenericMessage=Password non valida: la nuova password non rispetta le indicazioni previste.

# Authorization
myResources=Le mie risorse
myResourcesSub=Le mie risorse
doDeny=Nega
doRevoke=Revoca
doApprove=Approva
doRemoveSharing=Rimuovi condivisione
doRemoveRequest=Rimuovi richiesta
peopleAccessResource=Persone che hanno accesso a questa risorsa
resourceManagedPolicies=Permessi che danno accesso a questa risorsa
resourceNoPermissionsGrantingAccess=Nessun permesso dà accesso a questa risorsa
anyAction=Qualsiasi azione
description=Descrizione
name=Nome
scopes=Ambito
resource=Risorsa
user=Utente
peopleSharingThisResource=Persone che condividono questa risorsa
shareWithOthers=Condividi con altri
needMyApproval=Richiede la mia approvazione
requestsWaitingApproval=La tua richiesta è in attesa di approvazione
icon=Icona
requestor=Richiedente
owner=Proprietario
resourcesSharedWithMe=Risorse condivise con me
permissionRequestion=Richiesta di permesso
permission=Permesso
shares=condivisioni
notBeingShared=Questa risorsa non è in condivisione.
notHaveAnyResource=Non hai nessuna risorsa
noResourcesSharedWithYou=Non ci sono risorse condivise con te
havePermissionRequestsWaitingForApproval=Hai {0} richiesta(e) di permesso in attesa di approvazione.
clickHereForDetails=Clicca qui per i dettagli.
resourceIsNotBeingShared=La risorsa non è in condivisione

# Applications
applicationName=Nome
applicationType=Tipo applicazione
applicationInUse=In-use app only
clearAllFilter=Azzera tutti i filtri
activeFilters=Filtri attivi
filterByName=Filtra per nome ...
allApps=Tutte le applicazioni
internalApps=Applicazioni interne
thirdpartyApps=Applicazioni di terze parti
appResults=Risultati
clientNotFoundMessage=Client non trovato.

# Linked account
authorizedProvider=Provider autorizzato
authorizedProviderMessage=Provider autorizzati collegati al tuo account
identityProvider=Identity provider
identityProviderMessage=Collegare il tuo account con gli identity provider che hai configurato
socialLogin=Social Login
userDefined=Definito dall''utente
removeAccess=Rimuovi accesso
removeAccessMessage=Devi concedere di nuovo l''accesso, se vuoi utilizzare l''account di questa applicazione.

#Authenticator
authenticatorStatusMessage=L''autenticazione a due fattori è attualmente
authenticatorFinishSetUpTitle=La tua autenticazione a due fattori
authenticatorFinishSetUpMessage=Ogni volta che effettui l''accesso al tuo account Keycloak, ti verrà richiesto di fornire il tuo codice di autenticazione a due fattori.
authenticatorSubTitle=Imposta l''autenticazione a due fattori
authenticatorSubMessage=Per incrementare la sicurezza del tuo account, attiva almeno uno dei metodi disponibili per l''autenticazione a due fattori.
authenticatorMobileTitle=Autenticatore mobile
authenticatorMobileMessage=Utilizza l''autenticatore mobile per ottenere i codici di verifica per l''autenticazione a due fattori.
authenticatorMobileFinishSetUpMessage=L''autenticatore è stato collegato al tuo telefono.
authenticatorActionSetup=Set up
authenticatorSMSTitle=Codice SMS
authenticatorSMSMessage=Keycloak invierà il codice di verifica al tuo telefono per l''autenticazione a due fattori.
authenticatorSMSFinishSetUpMessage=I messaggi di testo vengono inviati a
authenticatorDefaultStatus=Default
authenticatorChangePhone=Cambia numero di telefono

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=Setup autenticatore mobile
smscodeIntroMessage=Inserisci il tuo numero di telefono e ti verrà inviato un codice di verifica.
mobileSetupStep1=Installa un''applicazione di autenticazione sul tuo telefono. Sono supportate le applicazioni qui elencate.
mobileSetupStep2=Apri l''applicazione e scansiona il codice QR:
mobileSetupStep3=Inserisci il codice monouso fornito dall''applicazione e clicca Salva per completare il setup.
scanBarCode=Vuoi scansionare il codice QR?
enterBarCode=Inserisci il codice monouso
doCopy=Copia
doFinish=Termina

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=Setup codice SMS
chooseYourCountry=Scegli la tua nazione
enterYourPhoneNumber=Inserisci il tuo numero di telefono
sendVerficationCode=Invia il codice di verifica
enterYourVerficationCode=Inserisci il codice di verifica

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=Setup backup codici
realmName=Realm
doDownload=Download
doPrint=Stampa
generateNewBackupCodes=Genera dei nuovi codici di backup
backtoAuthenticatorPage=Torna alla pagina dell''autenticatore


#Resources
resources=Risorse
sharedwithMe=Condiviso con me
share=Condiviso
sharedwith=Condiviso con
accessPermissions=Permessi di accesso
permissionRequests=Richieste di permesso
approve=Approva
approveAll=Approva tutti
people=persone
perPage=per pagina
currentPage=Pagina corrente
sharetheResource=Condividi la risorsa
group=Gruppo
selectPermission=Seleziona permessi
addPeople=Aggiungi persone con le quali condividere la tua risorsa
addTeam=Aggiungi gruppi con i quali condividere la tua risorsa
myPermissions=Miei permessi
waitingforApproval=Attesa dell''approvazione
anyPermission=Qualsiasi permesso

# Openshift messages
openshift.scope.user_info=Informazioni utente
openshift.scope.user_check-access=Informazioni per l''accesso dell''utente
openshift.scope.user_full=Accesso completo
openshift.scope.list-projects=Elenca progetti
