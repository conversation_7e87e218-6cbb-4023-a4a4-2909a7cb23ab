package otbs.ms_mission.enums;

/**
 * Énumération des types de missions unifiés.
 * Regroupe tous les types de missions (client et projet) en un seul enum.
 */
public enum TypeMission {

    // === MISSIONS CLIENT ===

    /**
     * Mission de reconnaissance du site.
     * Visite préliminaire pour évaluer les besoins et contraintes.
     */
    SITESURVEY("Site Survey", "Mission de reconnaissance du site", MissionCategory.CLIENT),

    /**
     * Visite avant-vente.
     * Présentation des solutions et démonstration technique.
     */
    VISITEAVANTVENTE("Visite Avant-Vente", "Visite commerciale et technique", MissionCategory.CLIENT),

    // === MISSIONS PROJET ===

    /**
     * Visite préventive.
     * Intervention de maintenance préventive.
     */
    VISITEPREVENTIVE("Visite Préventive", "Intervention de maintenance préventive", MissionCategory.PROJET),

    /**
     * Visite curative.
     * Intervention de maintenance curative.
     */
    VISITECURATIVE("Visite Curative", "Intervention de maintenance curative", MissionCategory.PROJET),

    /**
     * Projet.
     * Visite générale dans le cadre d'un projet en cours.
     */
    PROJET("Projet", "Visite générale de projet", MissionCategory.PROJET);

    private final String displayName;
    private final String description;
    private final MissionCategory category;

    TypeMission(String displayName, String description, MissionCategory category) {
        this.displayName = displayName;
        this.description = description;
        this.category = category;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public MissionCategory getCategory() {
        return category;
    }

    /**
     * Vérifie si le type de mission est une mission client.
     */
    public boolean isClientMission() {
        return category == MissionCategory.CLIENT;
    }

    /**
     * Vérifie si le type de mission est une mission projet.
     */
    public boolean isProjectMission() {
        return category == MissionCategory.PROJET;
    }

    /**
     * Catégorie de mission pour classifier les types.
     */
    public enum MissionCategory {
        CLIENT("Mission Client"),
        PROJET("Mission Projet");

        private final String displayName;

        MissionCategory(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
