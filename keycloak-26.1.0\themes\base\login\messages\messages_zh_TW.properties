# encoding: utf-8
doLogIn=登入
doRegister=註冊
doRegisterSecurityKey=註冊安全性金鑰
doCancel=取消
doSubmit=送出
doBack=返回
doYes=是
doNo=否
doContinue=繼續
doIgnore=忽略
doAccept=同意
doDecline=拒絕
doForgotPassword=忘記密碼？
doClickHere=點擊這裡
doImpersonate=模擬
doTryAgain=請重試
doTryAnotherWay=使用其他方式
doConfirmDelete=確認刪除
errorDeletingAccount=刪除帳號的過程中出錯了
deletingAccountForbidden=您沒有被允許能刪除自己的帳號，請聯絡系統管理員。
kerberosNotConfigured=尚未設定 Kerberos
kerberosNotConfiguredTitle=尚未設定 Kerberos
bypassKerberosDetail=您沒有通過 Kerberos 登入 或者您的瀏覽器沒有設定 Kerberos 登入。請點擊繼續使用其他方式登入。
kerberosNotSetUp=尚未設定 Kerberos，您無法登入
registerTitle=註冊
loginAccountTitle=登入您的帳號
loginTitle=登入到 {0}
loginTitleHtml={0}
impersonateTitle=模擬使用者 {0}
impersonateTitleHtml=模擬使用者 <strong>{0}</strong>
realmChoice=Realm
unknownUser=未知使用者
loginTotpTitle= 設定 OTP 驗證器
loginProfileTitle=更新帳號資訊
loginIdpReviewProfileTitle=更新帳號資訊
loginTimeout=登入逾時，請重新登入。
reauthenticate=重新登入以繼續
oauthGrantTitle=授予權限給 {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=請確認您信任 {0}，並且了解 {0} 如何處理您的資料。
oauthGrantReview=您可以檢視 
oauthGrantTos=服務條款
oauthGrantPolicy=隱私權政策
errorTitle=出錯啦！
errorTitleHtml=我們<strong>很抱歉</strong> ...
emailVerifyTitle=驗證電子信箱
emailForgotTitle=忘記密碼？
updateEmailTitle=更新電子信箱
emailUpdateConfirmationSentTitle=確認信已寄出
emailUpdateConfirmationSent=確認信已寄送到 {0}。您必須依照信中的指示完成電子信箱的更新。
emailUpdatedTitle=電子信箱已更新
emailUpdated=帳號的電子信箱已成功更新為 {0}。
updatePasswordTitle=更新密碼
codeSuccessTitle=成功代碼
codeErrorTitle=錯誤碼：{0}
displayUnsupported=未支援的請求狀態
browserRequired=需要透過瀏覽器進行登入
browserContinue=需要透過瀏覽器完成登入
browserContinuePrompt=透過瀏覽器繼續登入流程？ [是/否]:
browserContinueAnswer=是

# Transports
usb=USB
nfc=NFC
bluetooth=藍芽
internal=內部
unknown=未知

termsTitle=服務條款
termsText=<p>尚未被定義的服務條款</p>
termsPlainText=尚未被定義的服務條款。
termsAcceptanceRequired=您必續同意我們的服務條款。
acceptTerms=我同意服務條款

recaptchaFailed=無效的 Recaptcha 驗證碼
recaptchaNotConfigured=Recaptcha 尚未設定
consentDenied=許可被拒。

noAccount=新的使用者？
username=使用者名稱
usernameOrEmail=使用者名稱 或 電子信箱
firstName=名字
givenName=姓氏
fullName=全名
lastName=姓氏
familyName=姓氏
email=電子信箱
password=密碼
passwordConfirm=確認密碼
passwordNew=新密碼
passwordNewConfirm=確認新密碼
hidePassword=隱藏密碼
showPassword=顯示密碼
rememberMe=記住我
authenticatorCode=一次性驗證碼
address=地址
street=街道
locality=市
region=省、自治區、直轄市
postal_code=郵遞區號
country=國家
emailVerified=已驗證電子信箱
website=網頁
phoneNumber=電話號碼
phoneNumberVerified=已驗證電話號碼
gender=性別
birthday=出生日期
zoneinfo=時區
gssDelegationCredential=GSS Delegation Credential
logoutOtherSessions=從其他裝置登出

profileScopeConsentText=使用者資訊
emailScopeConsentText=電子信箱
addressScopeConsentText=地址
phoneScopeConsentText=電話號碼
offlineAccessScopeConsentText=離線存取
samlRoleListScopeConsentText=我的角色清單
rolesScopeConsentText=使用者角色清單
organizationScopeConsentText=組織

restartLoginTooltip=重新登入

loginTotpIntro=您需要設定 OTP 驗證器來啟用您的帳號
loginTotpStep1=使用您的手機安裝以下程式 (擇一即可)：
loginTotpStep2=開啟應用程式並掃描 QR code：
loginTotpStep3=輸入應用程式提供的一次性密碼並點擊送出來完成設定。
loginTotpStep3DeviceName=輸入裝置名稱，以便管理您的 OTP 驗證器。
loginTotpManualStep2=開啟應用程式並輸入金鑰：
loginTotpManualStep3=如果應用程式能做設定的話，請使用以下這些設定：
loginTotpUnableToScan=無法掃描？
loginTotpScanBarcode=掃描 QR code？
loginCredential=憑證
loginOtpOneTime=一次性密碼
loginTotpType=種類
loginTotpAlgorithm=算法
loginTotpDigits=位數
loginTotpInterval=間隔
loginTotpCounter=次數
loginTotpDeviceName=裝置名稱

loginTotp.totp=基於時間 (Time-based)
loginTotp.hotp=基於次數 (Counter-based)

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=選擇登入方式

oauthGrantRequest=您是否想要授予以下權限？
inResource=在

oauth2DeviceVerificationTitle=登入裝置
verifyOAuth2DeviceUserCode=輸入裝置提供的驗證碼並點擊送出
oauth2DeviceInvalidUserCodeMessage=錯誤的驗證碼，請重新嘗試
oauth2DeviceExpiredUserCodeMessage=驗證碼已過期，請回到您的裝置並重新嘗試連線。
oauth2DeviceVerificationCompleteHeader=裝置登入成功
oauth2DeviceVerificationCompleteMessage=您現在可以放心的關閉分頁並回到您的裝置。
oauth2DeviceVerificationFailedHeader=裝置登入失敗
oauth2DeviceVerificationFailedMessage=您現在可以放心的關閉分頁並重新嘗試連線。
oauth2DeviceConsentDeniedMessage=裝置授權失敗。
oauth2DeviceAuthorizationGrantDisabledMessage=客戶端不允許發起授權 OAuth 2.0 裝置。這個流程已被客戶端禁用。

emailVerifyInstruction1=一封含有驗證方式的信已經寄送到您的信箱 {0}
emailVerifyInstruction2=還沒收到驗證碼？
emailVerifyInstruction3=重新寄送驗證碼。

emailLinkIdpTitle=連結到 {0}
emailLinkIdp1=一封含有如何連結 {0} 到 {2} 的信件已寄送給您的 {1} 帳號。
emailLinkIdp2=還沒收到驗證碼？
emailLinkIdp3=重新寄送驗證碼。
emailLinkIdp4=如果您已經透過其他瀏覽器驗證
emailLinkIdp5=繼續。

backToLogin=&laquo; 回到登入畫面

emailInstruction=輸入您的 使用者名稱 或 電子信箱 我們會寄送電子郵件並告知您如何產生新密碼。
emailInstructionUsername=輸入您的使用者名稱，我們會寄送電子郵件並告知您如何產生新密碼。

copyCodeInstruction=請複製以下密碼，並貼入您的應用程式：

pageExpiredTitle=網頁已逾期
pageExpiredMsg1=重新登入
pageExpiredMsg2=繼續登入

personalInfo=個人資料：
role_admin=管理員
role_realm-admin=領域管理員
role_create-realm=創立領域
role_create-client=創立客戶端
role_view-realm=瀏覽領域
role_view-users=瀏覽使用者清單
role_view-applications=檢視應用清單
role_view-clients=檢視客戶端清單
role_view-events=檢視事件清單
role_view-identity-providers=檢視身分提供者清單
role_manage-realm=管理領域
role_manage-users=管理使用者清單
role_manage-applications=管理應用程式清單
role_manage-identity-providers=管理身分提供者清單
role_manage-clients=管理客戶端清單
role_manage-events=管理事件清單
role_view-profile=檢視個人資料
role_manage-account=管理帳號
role_manage-account-links=管理帳號連結
role_read-token=讀取 token
role_offline-access=離線存取
client_account=帳號
client_account-console=帳號終端
client_security-admin-console=管理員終端
client_admin-cli=Admin CLI
client_realm-management=領域管理
client_broker=Broker

requiredFields=必填的欄位

invalidUserMessage=無效的使用者名稱或密碼。
invalidUsernameMessage=無效的使用者名稱。
invalidUsernameOrEmailMessage=無效的使用者名稱或電子信箱。
invalidPasswordMessage=無效的密碼。
invalidEmailMessage=無效的電子信箱。
accountDisabledMessage=帳號已停用，請聯繫系統管理員。
accountTemporarilyDisabledMessage=無效的使用者名稱或密碼。
accountPermanentlyDisabledMessage=無效的使用者名稱或密碼。
accountTemporarilyDisabledMessageTotp=無效的驗證碼。
accountPermanentlyDisabledMessageTotp=無效的驗證碼。
expiredCodeMessage=登入逾時，請重新登入。
expiredActionMessage=動作逾時，請繼續登入。
expiredActionTokenNoSessionMessage=動作逾時。
expiredActionTokenSessionExistsMessage=動作逾時，請重新開始。
sessionLimitExceeded=您已開啟過多登入狀態
identityProviderLogoutFailure=SAML IdP 登出失敗

missingFirstNameMessage=請提供名字。
missingLastNameMessage=請提供姓氏。
missingEmailMessage=請提供電子信箱。
missingUsernameMessage=請提供使用者名稱。
missingPasswordMessage=請提供密碼。
missingTotpMessage=請輸入驗證碼。
missingTotpDeviceNameMessage=請提供裝置名稱。
notMatchPasswordMessage=密碼不相符。

error-invalid-value=無效的數值。
error-invalid-blank=數值不可為空。
error-empty=數值不可為空。
error-invalid-length=長度必須在 {1} 和 {2}  之間。
error-invalid-length-too-short=長度最短為 {1}。
error-invalid-length-too-long=長度最長為 {2}。
error-invalid-email=無效的電子信箱。
error-invalid-number=無效的號碼。
error-number-out-of-range=數字必須在 {1} 和 {2}  之間。
error-number-out-of-range-too-small=數字最小為 {1}。
error-number-out-of-range-too-big=數字最大為 {2}。
error-pattern-no-match=無效的數值。
error-invalid-uri=無效的 URL。
error-invalid-uri-scheme=無效的 URL 協定。
error-invalid-uri-fragment=無效的 URL 片段。
error-user-attribute-required=請提供此欄位。
error-invalid-date=無效的日期。
error-user-attribute-read-only=此欄位為唯讀。
error-username-invalid-character=含有無效字元。
error-person-name-invalid-character=含有無效字元。
error-reset-otp-missing-id=請選擇 OTP 驗證器。

invalidPasswordExistingMessage=無效的密碼：曾使用過的密碼。
invalidPasswordBlacklistedMessage=無效的密碼：該密碼已在黑名單中。
invalidPasswordConfirmMessage=密碼不一致。
invalidTotpMessage=無效的驗證碼。

usernameExistsMessage=相同的使用者名稱已存在。
emailExistsMessage=相同的電子信箱已存在。

federatedIdentityExistsMessage=使用者 {0} 已存在。請登入帳號管理來連結帳號。
federatedIdentityUnavailableMessage=在 {1} 身分提供者上驗證的使用者 {0} 不存在。請聯繫您的管理員。
federatedIdentityUnmatchedEssentialClaimMessage=身分提供者所發出的 ID 令牌與設定的聲明不符。請聯絡您的管理員。

confirmLinkIdpTitle=帳號已存在
federatedIdentityConfirmLinkMessage=使用者 {0} {1} 已存在。您要如何繼續？
federatedIdentityConfirmReauthenticateMessage=請重新驗證您的帳號以連結 {0}。
nestedFirstBrokerFlowMessage=使用者 {1} 未連結至 {0} 中的任何已知使用者。
confirmLinkIdpReviewProfile=檢視個人資料
confirmLinkIdpContinue=新增至現有帳號

configureTotpMessage=您需要設定 OTP 驗證器來啟用您的帳號。
configureBackupCodesMessage=您需要設定備份代碼來啟用您的帳號。
updateProfileMessage=您需要更新您的使用者資料來啟用您的帳號。
updatePasswordMessage=您需要更改您的密碼來啟用您的帳號。
updateEmailMessage=您需要更新您的電子信箱來啟用您的帳號。
resetPasswordMessage=您需要更新您的密碼。
verifyEmailMessage=您需要驗證您的電子信箱來啟用您的帳號。
linkIdpMessage=您需要驗證您的電子信箱來將您的帳號與 {0} 連結。

emailSentMessage=您將會收到一封電子郵件，包含進一步的指示。
emailSendErrorMessage=無法發送電子郵件，請稍後再試。

accountUpdatedMessage=您的帳號資訊已更新。
accountPasswordUpdatedMessage=您的密碼已更新。

delegationCompleteHeader=登入成功
delegationCompleteMessage=您可以放心地關閉此視窗，並回到您的應用程式。
delegationFailedHeader=登入失敗
delegationFailedMessage=您可以放心地關閉此視窗，並回到您的應用程式並再次嘗試登入。

noAccessMessage=沒有存取權

invalidPasswordMinLengthMessage=無效的密碼：最短長度為 {0}。
invalidPasswordMaxLengthMessage=無效的密碼：最長長度為 {0}。
invalidPasswordMinDigitsMessage=無效的密碼：至少需要 {0} 個數字。
invalidPasswordMinLowerCaseCharsMessage=無效的密碼：至少需要 {0} 個小寫字母。
invalidPasswordMinUpperCaseCharsMessage=無效的密碼：至少需要 {0} 個大寫字母。
invalidPasswordMinSpecialCharsMessage=無效的密碼：至少需要 {0} 個特殊字元。
invalidPasswordNotUsernameMessage=無效的密碼：不可與使用者名稱相同。
invalidPasswordNotContainsUsernameMessage=無效的密碼：不可包含使用者名稱。
invalidPasswordNotEmailMessage=無效的密碼：不可與電子信箱相同。
invalidPasswordRegexPatternMessage=無效的密碼：不符合 regex 規則。
invalidPasswordHistoryMessage=無效的密碼：不可與前 {0} 個密碼相同。
invalidPasswordGenericMessage=無效的密碼：新密碼不符合政策。

failedToProcessResponseMessage=無法處理回應
httpsRequiredMessage=需要使用 HTTPS
realmNotEnabledMessage=該領域尚未啟用
invalidRequestMessage=無效的請求
successLogout=您已經成功登出
failedLogout=登出失敗
unknownLoginRequesterMessage=未知的登入請求
loginRequesterNotEnabledMessage=該登入請求尚未啟用
bearerOnlyMessage=Bearer-only 的應用程式不允許啟用瀏覽器登入
standardFlowDisabledMessage=客戶端不允許使用給定的 response_type 啟動瀏覽器登入。標準流程已被該客戶端禁用。
implicitFlowDisabledMessage=客戶端不允許使用給定的 response_type 啟動瀏覽器登入。隱含流程已被該客戶端禁用。
invalidRedirectUriMessage=無效的 uri 重導向
unsupportedNameIdFormatMessage=尚未支援 NameIDFormat
invalidRequesterMessage=無效的請求
registrationNotAllowedMessage=已禁用註冊
resetCredentialNotAllowedMessage=已禁用重設密碼

permissionNotApprovedMessage=權限未核准。
noRelayStateInResponseMessage=身份提供者的回應中沒有中繼狀態。
insufficientPermissionMessage=缺少權限以連結身份。
couldNotProceedWithAuthenticationRequestMessage=無法繼續身份提供者的身份驗證請求。
couldNotObtainTokenMessage=無法從身份提供者取得 token。
unexpectedErrorRetrievingTokenMessage=從身份提供者取得 token 時發生非預期錯誤。
unexpectedErrorHandlingResponseMessage=處理身份提供者的回應時發生非預期錯誤。
identityProviderAuthenticationFailedMessage=身份提供者的身份驗證失敗。無法與身份提供者進行身份驗證。
couldNotSendAuthenticationRequestMessage=無法將身份驗證請求傳送至身份提供者。
unexpectedErrorHandlingRequestMessage=處理身份提供者的身份驗證請求時發生非預期錯誤。
invalidAccessCodeMessage=無效的存取代碼。
sessionNotActiveMessage=登入已過期。
invalidCodeMessage=出錯啦，請透過您的應用程式重新登入。
cookieNotFoundMessage=找不到 Cookie。請確認您的瀏覽器已啟用 Cookie。
insufficientLevelOfAuthentication=未滿足所要求的身份驗證層級。
identityProviderUnexpectedErrorMessage=透過身份提供者進行身份驗證時發生非預期錯誤
identityProviderMissingStateMessage=缺少身份提供者回應中的狀態參數。
identityProviderMissingCodeOrErrorMessage=身分提供者的回應中缺少錯誤代碼或參數。
identityProviderInvalidResponseMessage=身份提供者的回應無效。
identityProviderInvalidSignatureMessage=身份提供者回應中的簽章無效。
identityProviderNotFoundMessage=找不到身分提供者。
identityProviderLinkSuccess=您已成功驗證您的電子信箱。請回到瀏覽器分頁並繼續登入。
staleCodeMessage=此頁面已過期，請回到您的應用程式並重新登入。
realmSupportsNoCredentialsMessage=領域不支援任何憑證類型。
credentialSetupRequired=無法登入，需要設定憑證。
identityProviderNotUniqueMessage=領域支援多個身份提供者。無法決定應該使用哪個身份提供者進行身份驗證。
emailVerifiedMessage=您的電子信箱已通過驗證。
emailVerifiedAlreadyMessage=您的電子信箱已經完成驗證。
staleEmailVerificationLink=您點選的連結已過期，並且不再有效。也許您已驗證過您的電子信箱。
identityProviderAlreadyLinkedMessage=身份提供者 {0} 傳回的聯盟身份已連結至另一個使用者。
confirmAccountLinking=確認連結身份提供者 {1} 的帳戶 {0} 至您的帳戶。
confirmEmailAddressVerification=確認電子信箱 {0} 的有效性。
confirmExecutionOfActions=執行下列動作

backToApplication=&laquo; 返回應用程式
missingParameterMessage=遺失參數\: {0}
clientNotFoundMessage=找不到客戶端。
clientDisabledMessage=客戶端已禁用。
invalidParameterMessage=無效參數\: {0}
alreadyLoggedIn=您已經登入。
differentUserAuthenticated=您已經以不同的使用者 ''{0}'' 登入。請先登出後再繼續。
brokerLinkingSessionExpired=請求代理人帳戶連結，但目前的工作階段已不再有效。
proceedWithAction=&raquo; 點擊這裡繼續
acrNotFulfilled=未滿足授權需求

requiredAction.CONFIGURE_TOTP=設定 OTP
requiredAction.TERMS_AND_CONDITIONS=服務條款
requiredAction.UPDATE_PASSWORD=更新密碼
requiredAction.UPDATE_PROFILE=更新個人資訊
requiredAction.VERIFY_EMAIL=驗證電子信箱
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=產生復原代碼
requiredAction.webauthn-register-passwordless=Webauthn 無密碼註冊

invalidTokenRequiredActions=連結中包含無效行為

doX509Login=您將以下列身分登入\:
clientCertificate=X509 客戶端憑證\:
noCertificate=[沒有憑證]


pageNotFound=無法找到網頁
internalServerError=內部伺服器錯誤

console-username=使用者名稱：
console-password=密碼：
console-otp=一次性密碼：
console-new-password=新密碼：
console-confirm-password=確認密碼：
console-update-password=您需要更新您的密碼。
console-verify-email=您需要驗證您的電子郵件。我們已經發送了一封包含驗證碼的電子郵件給 {0}。請將此碼輸入到下面的輸入框中。
console-email-code=電子郵件驗證碼：
console-accept-terms=接受服務條款？ [是/否]:
console-accept=是

# Openshift messages
openshift.scope.user_info=使用者資訊
openshift.scope.user_check-access=使用者存取資訊
openshift.scope.user_full=完整存取權
openshift.scope.list-projects=列出專案

# SAML authentication
saml.post-form.title=跳轉身份驗證
saml.post-form.message=跳轉中，請稍候。
saml.post-form.js-disabled=JavaScript 已停用。我們強烈建議您啟用它。點擊下面的按鈕繼續。
saml.artifactResolutionServiceInvalidResponse=無法解析工件。

#authenticators
otp-display-name=驗證器應用程式
otp-help-text=輸入驗證器應用程式中的驗證碼。
otp-reset-description=要移除哪一個 OTP 設定？
password-display-name=密碼
password-help-text=輸入您的密碼以登入。
auth-username-form-display-name=使用者名稱
auth-username-form-help-text=輸入您的使用者名稱以登入。
auth-username-password-form-display-name=使用者名稱和密碼
auth-username-password-form-help-text=輸入您的使用者名稱和密碼以登入。

# Recovery Codes
auth-recovery-authn-code-form-display-name=恢復驗證碼
auth-recovery-authn-code-form-help-text=輸入先前產生的清單中的恢復驗證碼。
auth-recovery-code-info-message=輸入指定的恢復驗證碼。
auth-recovery-code-prompt=恢復代碼 #{0}
auth-recovery-code-header=使用恢復驗證碼登入
recovery-codes-error-invalid=無效的恢復驗證碼
recovery-code-config-header=恢復驗證碼
recovery-code-config-warning-title=這些恢復驗證碼在離開此頁面後將不會再出現
recovery-code-config-warning-message=請確保列印、下載或複製到密碼管理器中，並將其保存。取消此設定將從您的帳戶中刪除這些恢復驗證碼。
recovery-codes-print=列印
recovery-codes-download=下載
recovery-codes-copy=複製
recovery-codes-copied=已複製
recovery-codes-confirmation-message=我已經將這些代碼保存在安全的地方
recovery-codes-action-complete=完成設定
recovery-codes-action-cancel=取消設定
recovery-codes-download-file-header=請將這些恢復驗證碼保存在安全的地方。
recovery-codes-download-file-description=恢復驗證碼是一次性密碼，如果您無法存取您的驗證器，將允許您透過恢復驗證碼登入您的帳戶。
recovery-codes-download-file-date= 產生了這些代碼
recovery-codes-label-default=恢復驗證碼

# WebAuthn
webauthn-display-name=安全金鑰
webauthn-help-text=使用你的安全金鑰登入。
webauthn-passwordless-display-name=免密碼登入用的安全金鑰
webauthn-passwordless-help-text=使用你的安全金鑰進行免密碼登入。
webauthn-login-title=安全金鑰登入
webauthn-registration-title=註冊安全金鑰
webauthn-available-authenticators=可用的安全金鑰
webauthn-unsupported-browser-text=此瀏覽器不支援 WebAuthn。請嘗試其他瀏覽器或聯絡您的系統管理員。
webauthn-doAuthenticate=透過安全金鑰進行登入
webauthn-createdAt-label=已建立
webauthn-registration-init-label=安全金鑰 (預設標籤)
webauthn-registration-init-label-prompt=請為註冊的安全金鑰輸入標籤


# WebAuthn Error
webauthn-error-title=安全金鑰錯誤
webauthn-error-registration=無法註冊您的安全金鑰。<br/> {0}
webauthn-error-api-get=無法透過安全金鑰進行驗證。<br/> {0}
webauthn-error-different-user=第一個驗證的使用者不是這個安全金鑰驗證的使用者。
webauthn-error-auth-verification=安全金鑰驗證結果無效。<br/> {0}
webauthn-error-register-verification=安全金鑰註冊結果無效。<br/> {0}
webauthn-error-user-not-found=未知的使用者透過安全金鑰進行驗證。

# Identity provider
identity-provider-redirector=連結到其他身份提供者
identity-provider-login-label=或透過其他身份提供者登入
idp-email-verification-display-name=電子信箱驗證
idp-email-verification-help-text=透過驗證您的電子信箱來連結您的帳戶。
idp-username-password-form-display-name=使用者名稱和密碼
idp-username-password-form-help-text=透過登入來連結您的帳戶。

finalDeletionConfirmation=假如您刪除您的帳戶，它將無法復原。若要保留您的帳戶，請按取消。
irreversibleAction=這個動作是不可逆的
deleteAccountConfirm=確認刪除帳戶

deletingImplies=刪除您的帳戶意味著：
errasingData=清除您的所有資料
loggingOutImmediately=立即登出您的帳戶
accountUnusable=任何後續使用此帳戶的應用程式都將無法使用
userDeletedSuccessfully=帳戶已成功刪除

access-denied=存取遭拒
access-denied-when-idp-auth=使用 {0} 進行身分驗證時存取遭拒

frontchannel-logout.title=即將登出
frontchannel-logout.message=您正在登出以下應用程式
logoutConfirmTitle=即將登出
logoutConfirmHeader=您確定要登出嗎？
doLogout=登出

readOnlyUsernameMessage=您無法更新您的使用者名稱，因為它是唯讀的。
error-invalid-multivalued-size=屬性 {0} 必須包含至少 {1} ，最多 {2} 個值。
