package otbs.ms_mission;

import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


/**
 * Classe principale de l'application de gestion des missions.
 * Active les fonctionnalités JPA, l'enregistrement auprès d'Eureka et les clients Feign.
 * Le circuit breaker pour la tolérance aux pannes est configuré via Resilience4j.
 * Le cache est activé pour optimiser les performances.
 * RabbitMQ est activé pour la messagerie asynchrone.
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableCaching
@EnableRabbit
public class MsMissionApplication {

	public static void main(String[] args) {
		SpringApplication.run(MsMissionApplication.class, args);
	}

}
