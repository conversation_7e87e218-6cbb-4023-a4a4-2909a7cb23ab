<#import "template.ftl" as layout>
<#-- Configuration du template avec gestion des messages d'erreur pour le champ TOTP -->
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('totp'); section>
    <#if section="header">

    <#-- Section principale du formulaire -->
    <#elseif section="form">
        <#-- Formulaire principal d'authentification OTP -->
        <form id="kc-otp-login-form" class="${properties.kcFormClass!}" 
              onsubmit="login.disabled = true; return true;" 
              action="${url.loginAction}"
              method="post">
            
            <#-- En-tête du formulaire avec logo et titre -->
            <img src="${url.resourcesPath}/img/OTBS.png" alt="Onetech Logo" id="logo"><br>
            <span id="title">${msg("Connecter à parc auto Ontech")}</span>

            <#-- Section de sélection des credentials OTP multiples -->
            <#if otpLogin.userOtpCredentials?size gt 1>
                <div class="${properties.kcFormGroupClass!}">
                    <div class="${properties.kcInputWrapperClass!}">
                        <#-- Boucle sur les credentials OTP disponibles -->
                        <#list otpLogin.userOtpCredentials as otpCredential>
                            <#-- Radio button pour chaque credential -->
                            <input id="kc-otp-credential-${otpCredential?index}" 
                                   class="${properties.kcLoginOTPListInputClass!}" 
                                   type="radio" 
                                   name="selectedCredentialId" 
                                   value="${otpCredential.id}" 
                                   <#if otpCredential.id == otpLogin.selectedCredentialId>checked="checked"</#if>>
                            
                            <#-- Label pour le credential avec icône et nom -->
                            <label for="kc-otp-credential-${otpCredential?index}" 
                                   class="${properties.kcLoginOTPListClass!}" 
                                   tabindex="${otpCredential?index}">
                                <span class="${properties.kcLoginOTPListItemHeaderClass!}">
                                    <span class="${properties.kcLoginOTPListItemIconBodyClass!}">
                                        <i class="${properties.kcLoginOTPListItemIconClass!}" aria-hidden="true"></i>
                                    </span>
                                    <span class="${properties.kcLoginOTPListItemTitleClass!}">${otpCredential.userLabel}</span>
                                </span>
                            </label>
                        </#list>
                    </div>
                </div>
            </#if>

            <#-- Conteneur principal pour le formulaire OTP -->
            <div id="kc-form-bottom-right-container">
                <#-- Section de saisie du code OTP -->
                <div class="${properties.kcFormGroupClass!}" id="kc-otp-group" style="margin-bottom: 15px">
                    <#-- Label pour le champ de saisie OTP -->
                    <div class="${properties.kcLabelWrapperClass!}" id="kc-otp-label-wrapper">
                        <label for="kc-otp-input" class="${properties.kcLabelClass!}" id="kc-otp-label">
                            ${msg("loginOtpOneTime")}
                        </label>
                    </div>

                    <#-- Champ de saisie du code OTP -->
                    <div class="${properties.kcInputWrapperClass!}" id="kc-otp-input-wrapper">
                        <input id="kc-otp-input" 
                               name="otp" 
                               type="text"
                               class="${properties.kcInputClass!}"
                               autocomplete="one-time-code"
                               autofocus
                               aria-invalid="<#if messagesPerField.existsError('totp')>true</#if>"
                               dir="ltr" />

                        <#-- Affichage des messages d'erreur -->
                        <#if messagesPerField.existsError('totp')>
                            <span id="kc-otp-error" 
                                  class="${properties.kcInputErrorMessageClass!}" 
                                  aria-live="polite">
                                ${kcSanitize(messagesPerField.get('totp'))?no_esc}
                            </span>
                        </#if>
                    </div>
                </div>

                <#-- Section du bouton de soumission -->
                <div class="${properties.kcFormGroupClass!}" id="kc-submit-group">
                    <div id="kc-form-buttons" class="${properties.kcFormButtonsClass!}">
                        <input type="submit"
                               id="kc-login-submit"
                               class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!}"
                               value="${msg("doLogIn")}" />
                    </div>
                </div>
            </div>
        </form>
    </#if>
</@layout.registrationLayout>