spring.application.name=eureka-server

# Disable Spring Cloud Config
spring.cloud.config.enabled=false
spring.cloud.config.import-check.enabled=false

# Server port
server.port=9102

# Eureka Server Configuration
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false
eureka.client.service-url.defaultZone=http://localhost:9102/eureka/

# Eureka Server Instance Configuration
eureka.instance.hostname=localhost
eureka.server.enable-self-preservation=false
eureka.server.eviction-interval-timer-in-ms=5000

# Logging configuration
logging.level.root=INFO
logging.level.com.netflix.eureka=DEBUG
logging.level.com.netflix.discovery=DEBUG