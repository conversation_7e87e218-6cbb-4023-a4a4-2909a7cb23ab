emailVerificationBody=ვიღაცამ {2}-ის ანგარიში შექმნა ამ ელფოსტის მისამართით. თუ ეს თქვენ ბრძანდებოდით, დააწკაპუნეთ ქვედა ბმულს, რომ თქვენი ელფოსტის მისამართი დაადასტუროთ\n\n{0}\n\nამ ბმულის ამოწურვის დროა {3}.\n\nთუ ანგარიში არ შეგიქმნიათ, ეს შეტყობინება უბრალოდ გამოტოვეთ.
emailVerificationBodyHtml=<p>ვიღაცამ {2}-ის ანგარიში შექმნა ამ ელფოსტის მისამართით. თუ ეს თქვენ ბრძანდებოდით, დააწკაპუნეთ ქვედა ბმულს, რომ თქვენი ელფოსტის მისამართი დაადასტუროთ</p><p><a href="{0}">ბმული ელფოსტის მისამართის გადასამოწმებლად</a></p><p>ამ ბმულის ამოწურვის დროა {3}.</p><p>თუ ანგარიში არ შეგიქმნიათ, ეს შეტყობინება უბრალოდ გამოტოვეთ.</p>
orgInviteSubject=მოსაწვევი, შეუერთდეთ ორგანიზაციას {0}
orgInviteBody=მოგიწვიეს, მიუერთდეთ ორგანიზაციას "{3}". დააწკაპუნეთ ქვედა ბმულს, რომ შეუერთდეთ.\n\n{0}\n\nბმულის ამოწურვის დროა {4}.\n\nთუ არ გნებავთ, ორგანიზაციას მიუერთდეთ, უბრალოდ გამოტოვეთ ეს შეტყობინება.
orgInviteBodyHtml=<p>მოგიწვიეს, მიუერთდეთ ორგანიზაციას "{3}". დააწკაპუნეთ ქვედა ბმულს, რომ შეუერთდეთ.</p><p><a href="{0}">ბმული ორგანიზაციასთან მისაერთებლად.</a></p><p>ბმულის ამოწურვის დროა {4}.</p><p>თუ არ გნებავთ, ორგანიზაციას მიუერთდეთ, უბრალოდ გამოტოვეთ ეს შეტყობინება.</p>
orgInviteBodyPersonalized=გამარჯობა, "{5}" "{6}"\n\n თქვენ მიგიწვიეს, შეუერთდეთ ორგანიზაციას "{3}". ამისთვის დააწკაპუნეთ ქვედა ბმულს.\n\n{0}\n\nბმულის ვადის ამოწურვის დროა {4}.\n\nთუ არ გსურთ, ორგანიზაციას მიუერთდეთ, უბრალოდ გამოტოვეთ ეს შეტყობინება.
emailUpdateConfirmationBody=იმისათვის, რომ განაახლოთ თქვენი {2}-ის ანგარიში ელფოსტის ანგარიშით {1}, დააწკაპუნეთ ქვედა ბმულზე\n\n{0}\n\nამ ბმული ვადის ამოწურვის დროა {3}.\n\nთუ არ გნებავთ ამ ცვლილების შეტანა, უბრალოდ გამოტოვეთ ეს შეტყობინება.
orgInviteBodyPersonalizedHtml=<p>{5} {6}</p><p>გამარჯობა. თქვენ მიგიწვიეს, შეუერთდეთ ორგანიზაციას "{3}". ამისთვის დააწკაპუნეთ ქვედა ბმულს. </p><p><a href="{0}">ბმული ორგანიზაციასთან მისაერთებლად</a></p><p>ბმულის ვადის ამოწურვის დროა {4}.</p><p>თუ არ გსურთ, ორგანიზაციას მიუერთდეთ, უბრალოდ გამოტოვეთ ეს შეტყობინება.</p>
identityProviderLinkBody=ვიღაცას უნდა, თქვენი "{1}" ანგარიში მიაბას "{0}" ანგარიშს მომხმარებლისთვის {2} . თუ ეს თქვენ ბრძანდებით, დააწკაპუნეთ ქვედა ბმულზე, ანგარიშის მისაბმელად\n\n{3}\n\nამ ბმულის ვადის ამოწურვის დროა {5}.\n\nთუ არ გნებავთ ანგარიშის მიბმა, უბრალოდ გამოტოვეთ ეს შეტყობინება. თუ ანგარიშებს მიაბამთ, შეგეძლებათ შეხვიდეთ {1}-დან {0}-მდე ყველაფერზე.
executeActionsBody=თქვენმა ადმინისტრატორმა მოითხოვა, რომ თქვენ განაახლოთ {2} ანგარიში, შემდეგი ქმედებების შესრულებით: {3}. დააწკაპუნეთ ქვედა ბმულზე, რომ ეს პროცესი დაიწყოთ\n\n{0}\n\nამ ბმულის ვადის ამოწურვის დროა {4}.\n\nთუ თქვენ არ გგონიათ, რომ ეს თქვენმა სისტემურმა ადმინისტრატორმა მოგთხოვთ, უბრალოდ გამოტოვეთ ეს შეტყობინება და არაფერი შეიცვლება.
identityProviderLinkBodyHtml=<p>ვიღაცას უნდა, თქვენი <b>{1}</b> ანგარიში მიაბას <b>{0}</b> ანგარიშს მომხმარებლისთვის {2} . თუ ეს თქვენ ბრძანდებით, დააწკაპუნეთ ქვედა ბმულზე, ანგარიშის მისაბმელად</p><p><a href="{3}">ბმული ანგარიშის მიბმის დასადასტურებლად.</a></p><p>ამ ბმულის ვადის ამოწურვის დროა {5}.</p><p>თუ არ გნებავთ ანგარიშის მიბმა, უბრალოდ გამოტოვეთ ეს შეტყობინება. თუ ანგარიშებს მიაბამთ, შეგეძლებათ შეხვიდეთ {1}-დან {0}-მდე ყველაფერზე.</p>
passwordResetBody=ვიღაცამ თქვენი {2} ანგარიშის ავტორიზაციის დეტალების შეცვლა მოინდომა. თუ ეს თქვენ ბრძანდებოდით, მათ ჩამოსაყრელად ქვედა ბმულს დააჭირეთ.\n\n{0}\n\nეს ბმულის და კოდის ვადის ამოწურვის დროა {3}.\n\nთუ არ გნებავთ თქვენი ავტორიზაციის დეტალების ჩამოყრა, უბრალოდ გამოტოვეთ ეს შეტყობინება და არაფერი შეიცვლება.
passwordResetBodyHtml=<p>ვიღაცამ თქვენი {2} ანგარიშის ავტორიზაციის დეტალების შეცვლა მოინდომა. თუ ეს თქვენ ბრძანდებოდით, მათ ჩამოსაყრელად ქვედა ბმულს დააჭირეთ.</p><p><a href="{0}">ბმული ავტორიზაციის დეტალების ჩამოსაყრელად.</a></p><p>ეს ბმულის და კოდის ვადის ამოწურვის დროა {3}.</p><p>თუ არ გნებავთ თქვენი ავტორიზაციის დეტალების ჩამოყრა, უბრალოდ გამოტოვეთ ეს შეტყობინება და არაფერი შეიცვლება.</p>
executeActionsBodyHtml=<p>თქვენმა ადმინისტრატორმა მოითხოვა, რომ თქვენ განაახლოთ {2} ანგარიში, შემდეგი ქმედებების შესრულებით: {3}. დააწკაპუნეთ ქვედა ბმულზე, რომ ეს პროცესი დაიწყოთ.</p><p><a href="{0}">ბმული ანგარიშის განახლებაზე</a></p><p>ამ ბმულის ვადის ამოწურვის დროა {4}. </p><p>თუ თქვენ არ გგონიათ, რომ ეს თქვენმა სისტემურმა ადმინისტრატორმა მოგთხოვთ, უბრალოდ გამოტოვეთ ეს შეტყობინება და არაფერი შეიცვლება.</p>

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#წამი|1#წამი|1<წამი}
eventRemoveTotpBodyHtml=<p>OTP წაიშალა თქვენი ანგარიშიდან {0} {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.</p>
eventUpdatePasswordBodyHtml=<p>თქვენი პაროლი შეიცვალა {0}-ზე {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.</p>
eventUpdateTotpBody=OTP განახლდა თქვენი ანგარიშისთვის {0}-ზე {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.
requiredAction.UPDATE_PROFILE=პროფილის განახლება
emailVerificationSubject=ელფოსტის გადამოწმება
identityProviderLinkSubject={0}-ის მიბმა
executeActionsSubject=თქვენი ანგარიშის განახლება
emailUpdateConfirmationSubject=ახალი ელფოსტის გადამოწმება
emailUpdateConfirmationBodyHtml=<p>იმისათვის, რომ განაახლოთ თქვენი {2}-ის ანგარიში ელფოსტის ანგარიშით {1}, დააწკაპუნეთ ქვედა ბმულზე</p><p><a href="{0}">{0}</a></p><p>ამ ბმული ვადის ამოწურვის დროა {3}.</p><p>თუ არ გნებავთ ამ ცვლილების შეტანა, უბრალოდ გამოტოვეთ ეს შეტყობინება.</p>
emailTestSubject=[KEYCLOAK] - SMTP-ის შემოწმების შეტყობინება
emailTestBody=ეს შემოწმების შეტყობინებაა
emailTestBodyHtml=<p>ეს შემოწმების შეტყობინებაა</p>
passwordResetSubject=პაროლის ჩამოყრა
eventLoginErrorSubject=შესვლის შეცდომა
eventLoginErrorBody=აღმოჩენილია შესვლის ჩავარდნილი მცდელობა თქვენს ანგარიშზე {0} {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.
eventLoginErrorBodyHtml=<p>აღმოჩენილია შესვლის ჩავარდნილი მცდელობა თქვენს ანგარიშზე {0} {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.</p>
eventRemoveTotpSubject=OTP-ის წაშლა
eventRemoveTotpBody=OTP წაიშალა თქვენი ანგარიშიდან {0} {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.
eventUpdatePasswordSubject=პაროლის განახლება
eventUpdatePasswordBody=თქვენი პაროლი შეიცვალა {0}-ზე {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.
eventUpdateTotpSubject=OTP-ის განახლება
eventUpdateTotpBodyHtml=<p>OTP განახლდა თქვენი ანგარიშისთვის {0}-ზე {1}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.</p>
requiredAction.CONFIGURE_TOTP=OTP-ის მორგება
requiredAction.UPDATE_PASSWORD=პაროლის განახლება
requiredAction.VERIFY_EMAIL=ელფოსტის გადამოწმება
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=აღდგენის კოდების გენერაცია
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#წუთი|1#წუთი|1<წუთი}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#საათი|1#საათი|1<საათი}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#დღე|1#დღე|1<დღე}
emailVerificationBodyCode=გადაამოწმეთ თქვენი ელფოსტის მისამართი შემდეგი კოდის შეყვანით\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>გადაამოწმეთ თქვენი ელფოსტის მისამართი შემდეგი კოდის შეყვანით.</p><p><b>{0}</b></p>
requiredAction.TERMS_AND_CONDITIONS=წესები და პირობები
eventUpdateCredentialSubject=ავტ. დეტალების განახლება
eventUpdateCredentialBody=თქვენი {0} ავტორიზაციის დეტალი შეიცვალა {1}-ზე {2}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.
eventUpdateCredentialBodyHtml=<p>თქვენი {0}-ის ავტორიზაციის დეტალები შეიცვალა {1}-ზე {2}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.</p>
eventRemoveCredentialSubject=ავტორიზაციის დეტალის წაშლა
eventRemoveCredentialBody=ავტორიზაციის დეტალი {0} წაიშალა თქვენი ანგარიშიდან {1}-ზე {2}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.
eventRemoveCredentialBodyHtml=<p>შესვლის დეტალ {0} წაიშალა თქვენი ანგარიშიდან {1}-ზე {2}-დან. თუ ეს თქვენ არ ბრძანდებოდით, დაუკავშირდით ადმინისტრატორს.</p>
eventUserDisabledByTemporaryLockoutHtml=<p>თქვენი მომხმარებელი დროებით გათიშულია {0}-ზე მრავალჯერ შესვლის შეცდომის გამო. დაუკავშირდით ადმინისტრატორს, თუ ეს საჭიროა.</p>
eventUserDisabledByPermanentLockoutBody=თქვენი მომხმარებელი სამუდამოდ გათიშულია {0}-ზე მრავალჯერ შესვლის შეცდომის გამო. დაუკავშირდით ადმინისტრატორს, თუ ეს საჭიროა.
eventUserDisabledByPermanentLockoutHtml=<p>თქვენი მომხმარებელი სამუდამოდ გათიშულია {0}-ზე მრავალჯერ შესვლის შეცდომის გამო. დაუკავშირდით ადმინისტრატორს, თუ ეს საჭიროა.</p>
eventUserDisabledByTemporaryLockoutSubject=მომხმარებელი გათიშულია დროებითი დაბლოკვის მიერ
eventUserDisabledByTemporaryLockoutBody=თქვენი მომხმარებელი დროებით გათიშულია {0}-ზე მრავალჯერ შესვლის შეცდომის გამო. დაუკავშირდით ადმინისტრატორს, თუ ეს საჭიროა.
eventUserDisabledByPermanentLockoutSubject=მომხმარებელი სამუდამოდ დაბლოკილია
