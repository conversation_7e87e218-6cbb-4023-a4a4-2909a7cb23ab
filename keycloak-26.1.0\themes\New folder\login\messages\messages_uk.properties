doLogIn=Увійти
doRegister=Зареєструватися
doRegisterSecurityKey=Зареєструватися
doCancel=Скасувати
doSubmit=Надіслати
doBack=Назад
doYes=Так
doNo=Ні
doContinue=Продовжити
doIgnore=Ігнорувати
doAccept=Прийняти
doDecline=Відхилити
doForgotPassword=Забули пароль?
doClickHere=Натисніть тут
doImpersonate=Імперсоналізуватися
doTryAgain=Спробуйте знову
doTryAnotherWay=Спробуйте інший спосіб
doConfirmDelete=Підтвердити видалення
errorDeletingAccount=Під час видалення облікового запису сталася помилка
deletingAccountForbidden=У вас недостатньо прав для видалення власного облікового запису, зверніться до адміністратора.
kerberosNotConfigured=Kerberos не налаштовано
kerberosNotConfiguredTitle=Kerberos не налаштовано
bypassKerberosDetail=Або ви не ввійшли в систему через Kerberos, або ваш браузер не налаштований для входу через Kerberos. Натисніть «Продовжити», щоб увійти іншим способом
kerberosNotSetUp=Kerberos не налаштовано. Ви не можете увійти.
registerTitle=Зареєструватися
loginAccountTitle=Увійдіть у свій обліковий запис
loginTitle=Увійти в {0}
loginTitleHtml={0}
impersonateTitle={0} Видавати себе за користувача
impersonateTitleHtml=<strong>{0}</strong> Видавати себе за користувача
realmChoice=Realm
unknownUser=Невідомий користувач
loginTotpTitle=Налаштування мобільного автентифікатора
loginProfileTitle=Оновити інформацію облікового запису
loginIdpReviewProfileTitle=Оновити інформацію облікового запису
loginTimeout=Час очікування вашої спроби входу минув. Вхід почнеться з початку.
reauthenticate=Будь ласка, пройдіть повторну автентифікацію, щоб продовжити
oauthGrantTitle=Надати доступ до {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Переконайтеся, що Ви довіряєте {0}, дізнавшись, як {0} оброблятиме Ваші дані.
oauthGrantReview=Ви можете переглянути
oauthGrantTos=умови обслуговування.
oauthGrantPolicy=політика конфіденційності.
errorTitle=Нам шкода...
errorTitleHtml=<strong>Вибачте</strong> ...
emailVerifyTitle=Підтвердження електронної пошти
emailForgotTitle=Забули пароль?
updateEmailTitle=Оновити адресу електронної пошти
emailUpdateConfirmationSentTitle=Електронний лист із підтвердженням надіслано
emailUpdateConfirmationSent=Електронний лист із підтвердженням надіслано на адресу {0}. Слідуйте вказівкам, щоб завершити оновлення адреси електронної пошти.
emailUpdatedTitle=Адреса електронної пошти оновлена
emailUpdated=Електронна адреса облікового запису була успішно оновлена на {0}.
updatePasswordTitle=Оновити пароль
codeSuccessTitle=Код успіху
codeErrorTitle=Код помилки\: {0}
displayUnsupported=Запитуваний тип дисплея не підтримується
browserRequired=Браузер потрібен для входу
browserContinue=Браузер потрібен для завершення входу
browserContinuePrompt=Відкрити браузер і продовжити вхід? [y/n]:
browserContinueAnswer=y

termsTitle=Умови
termsText=<p>Положення та умови повинні бути визначені</p>
termsPlainText=Положення та умови, які будуть визначені.
termsAcceptanceRequired=Ви повинні погодитися з умовами.
acceptTerms=Я погоджуюсь з умовами

recaptchaFailed=Недійсний Recaptcha
recaptchaNotConfigured=Recaptcha потрібна, але не налаштована
consentDenied=Згода відхилена.

noAccount=Новий користувач?
username=Ім''я користувача
usernameOrEmail=Ім''я користувача або електронна пошта
firstName=Ім''я
givenName=Ім''я
fullName=Повне ім''я
lastName=Прізвище
familyName=Прізвище
email=Електронна пошта
password=Пароль
passwordConfirm=Підтвердити пароль
passwordNew=Новий пароль
passwordNewConfirm=Нове підтвердження пароля
hidePassword=Приховати пароль
showPassword=Показати пароль
rememberMe=Запам''ятати мене
authenticatorCode=Одноразовий код
address=Адреса
street=Вулиця
locality=Місто
region=Область
postal_code=Поштовий індекс
country=Країна
emailVerified=Адреса електронної пошти підтверджена
website=Веб-сторінка
phoneNumber=Номер телефону
phoneNumberVerified=Номер телефону перевірено
gender=Стать
birthday=Дата народження
zoneinfo=Часовий пояс
gssDelegationCredential=Облікові дані делегування GSS
logoutOtherSessions=Вийти з інших пристроїв

profileScopeConsentText=Профіль користувача
emailScopeConsentText=Адреса електронної пошти
addressScopeConsentText=Адреса
phoneScopeConsentText=Номер телефону
offlineAccessScopeConsentText=Доступ офлайн
samlRoleListScopeConsentText=Мої ролі
rolesScopeConsentText=Ролі користувача

restartLoginTooltip=Перезапустити вхід

loginTotpIntro=Вам потрібно налаштувати генератор одноразових паролів для доступу до цього облікового запису
loginTotpStep1=Встановіть на свій мобільний телефон один із наступних застосунків:
loginTotpStep2=Відкрийте застосунок та відскануйте штрих-код:
loginTotpStep3=Введіть одноразовий код, наданий застосунком, і натисніть "Надіслати", щоб завершити налаштування.
loginTotpStep3DeviceName=Введіть назву пристрою, щоб допомогти вам керувати пристроями OTP.
loginTotpManualStep2=Відкрийте застосунок та введіть ключ:
loginTotpManualStep3=Використовуйте такі значення конфігурації, якщо застосунок дозволяє їх установку:
loginTotpUnableToScan=Неможливо сканувати?
loginTotpScanBarcode=Сканувати штрих-код?
loginCredential=Облікові дані
loginOtpOneTime=Одноразовий код
loginTotpType=Тип
loginTotpAlgorithm=Алгоритм
loginTotpDigits=Цифри
loginTotpInterval=Інтервал
loginTotpCounter=Лічильник
loginTotpDeviceName=Назва пристрою

loginTotp.totp=На основі часу
loginTotp.hotp=На основі лічильника

totpAppFreeOTPName=Безкоштовний OTP
totpAppGoogleName=Автентифікатор Google
totpAppMicrosoftAuthenticatorName=Автентифікатор Microsoft

loginChooseAuthenticator=Виберіть метод входу

oauthGrantRequest=Ви згодні надати ці привілеї доступу?
inResource=у

oauth2DeviceVerificationTitle=Вхід на пристрій
verifyOAuth2DeviceUserCode=Введіть код, наданий Вашим пристроєм, і натисніть "Надіслати"
oauth2DeviceInvalidUserCodeMessage=Недійсний код, спробуйте ще раз.
oauth2DeviceExpiredUserCodeMessage=Термін дії коду минув. Поверніться до свого пристрою та спробуйте підключитися знову.
oauth2DeviceVerificationCompleteHeader=Успішний вхід на пристрій
oauth2DeviceVerificationCompleteMessage=Ви можете закрити це вікно браузера та повернутися до свого пристрою.
oauth2DeviceVerificationFailedHeader=Помилка входу на пристрій
oauth2DeviceVerificationFailedMessage=Ви можете закрити це вікно браузера, повернутися до свого пристрою та спробувати підключитися знову.
oauth2DeviceConsentDeniedMessage=Згода на підключення пристрою відмовлена.
oauth2DeviceAuthorizationGrantDisabledMessage=Клієнту не дозволено ініціювати дозвіл авторизації пристрою OAuth 2.0. Цей процес вимкнено для клієнта.

emailVerifyInstruction1=Електронний лист з інструкціями щодо підтвердження Вашої адреси електронної пошти було надіслано на Вашу адресу {0}.
emailVerifyInstruction2=Не отримали код підтвердження на вашу електронну пошту?
emailVerifyInstruction3=щоб повторно надіслати електронний лист.

emailLinkIdpTitle=Посилання {0}
emailLinkIdp1=Вам був надісланий електронний лист з інструкціями щодо зв''язування облікового запису {0} {1} з вашим обліковим записом {2}.
emailLinkIdp2=Не отримали код підтвердження на вашу електронну пошту?
emailLinkIdp3=для повторного відправлення листа.
emailLinkIdp4=Якщо ви вже підтвердили електронну адресу в іншому браузері
emailLinkIdp5=для продовження.

backToLogin=&laquo; Назад до входу

emailInstruction=Введіть своє ім''я користувача або адресу електронної пошти, і ми надішлемо Вам інструкції щодо створення нового пароля.
emailInstructionUsername=Введіть своє ім''я користувача, і ми надішлемо Вам інструкції щодо створення нового пароля.

copyCodeInstruction=Будь ласка, скопіюйте цей код і вставте його у свій застосунок:

pageExpiredTitle=Термін дії сторінки закінчився
pageExpiredMsg1=Щоб перезапустити процес входу
pageExpiredMsg2=Щоб продовжити процес входу

personalInfo=Особиста інформація:
role_admin=Адміністратор
role_realm-admin=Адміністратор realm
role_create-realm=Створення realm
role_create-client=Створення клієнта
role_view-realm=Перегляд realm
role_view-users=Перегляд користувачів
role_view-applications=Перегляд застосунків
role_view-clients=Перегляд клієнтів
role_view-events=Перегляд подій
role_view-identity-providers=Перегляд провайдерів облікових записів
role_manage-realm=Керування realm
role_manage-users=Керування користувачами
role_manage-applications=Керування застосунками
role_manage-identity-providers=Керування провайдерами облікових записів
role_manage-clients=Керування клієнтами
role_manage-events=Керування подіями
role_view-profile=Перегляд профілю
role_manage-account=Керування обліковим записом
role_manage-account-links=Керування зв''язаними обліковими записами
role_read-token=Перегляд токену
role_offline-access=Офлайн-доступ
client_account=Обліковий запис
client_account-console=Консоль облікового запису
client_security-admin-console=Консоль адміністратора безпеки
client_admin-cli=Admin CLI
client_realm-management=Керування realm
client_broker=Брокер

requiredFields=Обов''язкові поля

invalidUserMessage=Невірне ім''я користувача або пароль.
invalidUsernameMessage=Невірне ім''я користувача.
invalidUsernameOrEmailMessage=Невірне ім''я користувача або адреса електронної пошти.
invalidPasswordMessage=Невірний пароль.
invalidEmailMessage=Невірна адреса електронної пошти.
accountDisabledMessage=Обліковий запис заблоковано, зверніться до адміністратора.
accountTemporarilyDisabledMessage=Невірне ім''я користувача або пароль.
accountPermanentlyDisabledMessage=Невірне ім''я користувача або пароль.
accountTemporarilyDisabledMessageTotp=Невірний код автентифікатора.
accountPermanentlyDisabledMessageTotp=Невірний код автентифікатора.
expiredCodeMessage=Час очікування входу закінчився. Увійдіть знову.
expiredActionMessage=Термін дії закінчився. Будь ласка, увійдіть знову.
expiredActionTokenNoSessionMessage=Термін дії закінчився.
expiredActionTokenSessionExistsMessage=Термін дії минув. Будь ласка, почніть знову.
sessionLimitExceeded=Перевищено ліміт по кількості сесій

missingFirstNameMessage=Будь ласка, вкажіть ім''я.
missingLastNameMessage=Будь ласка, вкажіть прізвище.
missingEmailMessage=Будь ласка, вкажіть адресу електронної пошти.
missingUsernameMessage=Будь ласка, вкажіть ім''я користувача.
missingPasswordMessage=Будь ласка, вкажіть пароль.
missingTotpMessage=Будь ласка, вкажіть код автентифікатора.
missingTotpDeviceNameMessage=Будь ласка, вкажіть назву пристрою.
notMatchPasswordMessage=Паролі не збігаються.

error-invalid-value=Невірне значення.
error-invalid-blank=Будь ласка, вкажіть значення.
error-empty=Будь ласка, вкажіть значення.
error-invalid-length=Кількість символів повинна бути не менше {1} і не більше {2}.
error-invalid-length-too-short=Кількість символів повинна бути не менше {1}.
error-invalid-length-too-long=Кількість символів повинна бути не більше {2}.
error-invalid-email=Невірна адреса електронної пошти.
error-invalid-number=Невірне число.
error-number-out-of-range=Число повинно бути не менше {1} і не більше {2}.
error-number-out-of-range-too-small=Число повинно бути не менше {1}.
error-number-out-of-range-too-big=Число повинно бути не більше {2}.
error-pattern-no-match=Невірне значення.
error-invalid-uri=Невірний URL.
error-invalid-uri-scheme=Невірна схема URL.
error-invalid-uri-fragment=Невірний фрагмент URL.
error-user-attribute-required=Будь ласка, вкажіть атрибут.
error-invalid-date=Невірна дата.
error-user-attribute-read-only=Це поле доступне лише для читання.
error-username-invalid-character=Значення містить невірний символ.
error-person-name-invalid-character=Значення містить невірний символ.
error-reset-otp-missing-id=Будь ласка, виберіть конфігурацію OTP.

invalidPasswordExistingMessage=Невірний існуючий пароль.
invalidPasswordBlacklistedMessage=Невірний пароль: пароль у чорному списку.
invalidPasswordConfirmMessage=Підтвердження паролю не збігається.
invalidTotpMessage=Невірний код автентифікатора.

usernameExistsMessage=Ім''я користувача вже існує.
emailExistsMessage=Адреса електронної пошти вже існує.

federatedIdentityExistsMessage=Користувач з {0} {1} вже існує. Будь ласка, увійдіть до системи керування обліковим записом, щоб зв''язати обліковий запис.
federatedIdentityUnavailableMessage=Користувач {0}, автентифікований провайдером облікових записів {1}, не існує. Будь ласка, зверніться до адміністратора.
federatedIdentityUnmatchedEssentialClaimMessage=ID-токен, виданий провайдером облікових записів, не відповідає налаштуванням. Будь ласка, зверніться до свого адміністратора.

confirmLinkIdpTitle=Обліковий запис уже існує
federatedIdentityConfirmLinkMessage=Користувач із {0} {1} вже існує. Бажаєте продовжити?
federatedIdentityConfirmReauthenticateMessage=Пройдіть автентифікацію, щоб зв''язати свій обліковий запис із {0}
nestedFirstBrokerFlowMessage=Користувач {0} {1} не зв''язаний з жодним відомим користувачем.
confirmLinkIdpReviewProfile=Переглянути профіль
confirmLinkIdpContinue=Додати до існуючого облікового запису

configureTotpMessage=Вам потрібно налаштувати Mobile Authenticator, щоб активувати обліковий запис.
configureBackupCodesMessage=Вам потрібно налаштувати резервні коди, щоб активувати обліковий запис.
updateProfileMessage=Вам потрібно оновити свій профіль користувача, щоб активувати обліковий запис.
updatePasswordMessage=Вам потрібно змінити пароль, щоб активувати обліковий запис.
updateEmailMessage=Вам потрібно оновити адресу електронної пошти, щоб активувати обліковий запис.
resetPasswordMessage=Вам потрібно змінити пароль.
verifyEmailMessage=Вам потрібно підтвердити адресу електронної пошти, щоб активувати обліковий запис.
linkIdpMessage=Вам потрібно підтвердити свою адресу електронної пошти, щоб зв''язати обліковий запис із {0}.

emailSentMessage=Невдовзі Ви маєте отримати електронний лист із подальшими інструкціями.
emailSendErrorMessage=Не вдалося надіслати листа на електронну пошту, повторіть спробу пізніше.

accountUpdatedMessage=Ваш обліковий запис оновлено.
accountPasswordUpdatedMessage=Ваш пароль оновлено.

delegationCompleteHeader=Вхід успішний
delegationCompleteMessage=Ви можете закрити це вікно браузера та повернутися до вашої консольної програми.
delegationFailedHeader=Помилка входу
delegationFailedMessage=Ви можете закрити це вікно браузера, повернутися до консольної програми та спробувати увійти знову.

noAccessMessage=Немає доступу

invalidPasswordMinLengthMessage=Невірний пароль: довжина пароля повинна бути не менше {0} символів(а).
invalidPasswordMaxLengthMessage=Невірний пароль: довжина пароля повинна бути не більше {0} символів(а).
invalidPasswordMinDigitsMessage=Невірний пароль: пароль має містити принаймні {0} цифр.
invalidPasswordMinLowerCaseCharsMessage=Невірний пароль: пароль має містити щонайменше {0} символів(а) у нижньому регістру.
invalidPasswordMinUpperCaseCharsMessage=Невірний пароль: пароль має містити щонайменше {0} символів(а) у верхньому регістрі.
invalidPasswordMinSpecialCharsMessage=Невірний пароль: пароль має містити щонайменше {0} спеціальних символів(а).
invalidPasswordNotUsernameMessage=Невірний пароль: пароль не повинен збігатися з іменем користувача.
invalidPasswordNotEmailMessage=Невірний пароль: пароль не повинен збігатися з адресою електронної пошти.
invalidPasswordRegexPatternMessage=Невірний пароль: пароль не відповідає шаблону(ам) регулярного виразу.
invalidPasswordHistoryMessage=Недійсний пароль: пароль не повинен збігатися з жодним з останніх {0} паролів.
invalidPasswordGenericMessage=Невірний пароль: новий пароль не відповідає політиці паролів.

failedToProcessResponseMessage=Не вдалося обробити відповідь
httpsRequiredMessage=Потрібен HTTPS
realmNotEnabledMessage=Realm не включений
invalidRequestMessage=Невірний запит
successLogout=Ви вийшли з системи
failedLogout=Помилка виходу
unknownLoginRequesterMessage=Невідомий клієнт
loginRequesterNotEnabledMessage=Клієнт відключений
bearerOnlyMessage=Застосункам, які працюють лише з носієм, заборонено ініціювати вхід у браузер
standardFlowDisabledMessage=Клієнту не дозволено ініціювати вхід у браузер із вказаним response_type. Standard flow вимкнено для клієнта.
implicitFlowDisabledMessage=Клієнту не дозволено ініціювати вхід у браузер із вказаним response_type. Implicit flow вимкнено для клієнта.
invalidRedirectUriMessage=Невірний uri перенаправлення
unsupportedNameIdFormatMessage=NameIDFormat не підтримується
invalidRequesterMessage=Невірний клієнт
registrationNotAllowedMessage=Реєстрація не дозволена
resetCredentialNotAllowedMessage=Скинути облікові дані заборонено

permissionNotApprovedMessage=Дозвіл не схвалено.
noRelayStateInResponseMessage=Немає стану ретрансляції у відповіді від провайдера облікових записів.
insufficientPermissionMessage=Недостатньо дозволів для зв''язування облікових записів.
couldNotProceedWithAuthenticationRequestMessage=Не вдалося продовжити запит автентифікації до провайдера облікових записів.
couldNotObtainTokenMessage=Не вдалося отримати токен від провайдера облікових записів.
unexpectedErrorRetrievingTokenMessage=Неочікувана помилка під час отримання токену від провайдера облікових записів.
unexpectedErrorHandlingResponseMessage=Неочікувана помилка під час обробки відповіді від провайдера облікових записів
identityProviderAuthenticationFailedMessage=Помилка автентифікації. Не вдалося автентифікуватися за допомогою провайдера облікових записів.
couldNotSendAuthenticationRequestMessage=Не вдалося надіслати запит автентифікації до провайдера облікових записів.
unexpectedErrorHandlingRequestMessage=Неочікувана помилка під час обробки запиту автентифікації до провайдера облікових записів.
invalidAccessCodeMessage=Невірний код доступу.
sessionNotActiveMessage=Сесія неактивна.
invalidCodeMessage=Сталася помилка, будь ласка, увійдіть знову через ваш застосунок.
cookieNotFoundMessage=Файл cookie не знайдено. Переконайтеся, що файли cookie увімкнено у вашому браузері.
insufficientLevelOfAuthentication=Запитаний рівень автентифікації не був задоволений.
identityProviderUnexpectedErrorMessage=Неочікувана помилка під час автентифікації за допомогою провайдера облікових записів.
identityProviderMissingStateMessage=Відсутній параметр стану у відповіді від провайдера облікових записів.
identityProviderMissingCodeOrErrorMessage=Відсутній код або параметр помилки у відповіді від провайдера облікових записів.
identityProviderInvalidResponseMessage=Невірна відповідь від провайдера облікових записів.
identityProviderInvalidSignatureMessage=Невірний підпис у відповіді від провайдера облікових записів.
identityProviderNotFoundMessage=Не вдалося знайти провайдера облікових записів.
identityProviderLinkSuccess=Ви успішно підтвердили свою адресу електронної пошти. Будь ласка, поверніться до свого браузера та продовжте вхід.
staleCodeMessage=Ця сторінка більше не дійсна, будь ласка, поверніться до свого застосунку та увійдіть знову
realmSupportsNoCredentialsMessage=Realm не підтримує жодного типу облікових даних.
credentialSetupRequired=Не вдається увійти, потрібні налаштування облікових даних.
identityProviderNotUniqueMessage=Realm підтримує кілька провайдера облікових записів. Не вдалося визначити, якого провайдера облікових записів слід використовувати для автентифікації.
emailVerifiedMessage=Вашу адресу електронної пошти було перевірено.
emailVerifiedAlreadyMessage=Вашу адресу електронної пошти вже перевірено.
staleEmailVerificationLink=Посилання, на яке Ви натиснули, є застарілим і більше не працює. Можливо, Ви вже підтвердили свою електронну адресу.
identityProviderAlreadyLinkedMessage=Обліковий запис, повернутий {0}, вже зв''язаний з іншим користувачем.
confirmAccountLinking=Підтвердити зв''язування облікового запису {0} провайдера облікових записів {1} з вашим обліковим записом.
confirmEmailAddressVerification=Підтвердити адресу електронної пошти {0}.
confirmExecutionOfActions=Виконайте наступні дії

backToApplication=&laquo; Назад до застосунку
missingParameterMessage=Відсутні параметри\: {0}
clientNotFoundMessage=Клієнта не знайдено.
clientDisabledMessage=Клієнта вимкнено.
invalidParameterMessage=Невірний параметр\: {0}
alreadyLoggedIn=Ви вже ввійшли в систему.
differentUserAuthenticated=Ви вже аутентифіковані як інший користувач ''{0}'' у цій сесії. Спочатку вийдіть.
brokerLinkingSessionExpired=Запитано зв''язування облікового запису брокера, але поточна сесія більше недійсна.
proceedWithAction=&raquo; Натисніть тут, щоб продовжити
acrNotFulfilled=Вимоги автентифікації не виконано

requiredAction.CONFIGURE_TOTP=Налаштувати OTP
requiredAction.TERMS_AND_CONDITIONS=Положення та умови
requiredAction.UPDATE_PASSWORD=Оновити пароль
requiredAction.UPDATE_PROFILE=Оновити профіль
requiredAction.VERIFY_EMAIL=Підтвердити адресу електронної пошти
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Генерувати коди відновлення
requiredAction.webauthn-register-passwordless=Реєстрація Webauthn без пароля

invalidTokenRequiredActions=Необхідні дії, які містяться в посиланні, недійсні

doX509Login=Ви увійдете як\:
clientCertificate=сертифікат клієнта X509\:
noCertificate=[Немає сертифіката]


pageNotFound=Сторінка не знайдена
internalServerError=Сталася внутрішня помилка сервера

console-username=Ім''я користувача:
console-password=Пароль:
console-otp=Одноразовий пароль:
console-new-password=Новий пароль:
console-confirm-password=Підтвердити пароль:
console-update-password=Потрібно оновити ваш пароль.
console-verify-email=Вам потрібно підтвердити свою адресу електронної пошти. Ми надіслали електронний лист на адресу {0}, який містить код підтвердження. Введіть цей код у поле нижче.
console-email-code=Код з електронної пошти:
console-accept-terms=Прийняти умови? [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=Інформація про користувача
openshift.scope.user_check-access=Інформація про доступ користувача
openshift.scope.user_full=Повний доступ
openshift.scope.list-projects=Список проектів

# SAML authentication
saml.post-form.title=Переадресація автентифікації
saml.post-form.message=Переадресація, будь ласка, зачекайте.
saml.post-form.js-disabled=JavaScript вимкнено. Ми наполегливо рекомендуємо ввімкнути його. Щоб продовжити, натисніть кнопку нижче.
saml.artifactResolutionServiceInvalidResponse=Не вдається вирішити артефакт.

# authenticators
otp-display-name=Застосунок автентифікації
otp-help-text=Введіть код підтвердження з застосунку автентифікації.
otp-reset-description=Яку конфігурацію OTP слід видалити?
password-display-name=Пароль
password-help-text=Увійдіть, ввівши свій пароль.
auth-username-form-display-name=Ім''я користувача
auth-username-form-help-text=Почніть вхід, ввівши своє ім''я користувача
auth-username-password-form-display-name=Ім''я користувача та пароль
auth-username-password-form-help-text=Увійдіть, ввівши своє ім''я користувача та пароль.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Код відновлення автентифікації
auth-recovery-authn-code-form-help-text=Введіть код відновлення автентифікації з попередньо створеного списку.
auth-recovery-code-info-message=Введіть код відновлення.
auth-recovery-code-prompt=Код відновлення #{0}
auth-recovery-code-header=Увійдіть за допомогою коду відновлення автентифікації
recovery-codes-error-invalid=Недійсний код відновлення автентифікації
recovery-code-config-header=Коди відновлення автентифікації
recovery-code-config-warning-title=Коди відновлення більше не можливо буде переглянути після виходу з цієї сторінки
recovery-code-config-warning-message=Обов''язково роздрукуйте, завантажте або скопіюйте їх до менеджера паролів і збережіть. Скасування цього налаштування призведе до видалення цих кодів відновлення з вашого облікового запису.
recovery-codes-print=Друк
recovery-codes-download=Завантажити
recovery-codes-copy=Копіювати
recovery-codes-copied=Скопійовано
recovery-codes-confirmation-message=Я зберіг ці коди в безпечному місці
recovery-codes-action-complete=Завершити налаштування
recovery-codes-action-cancel=Скасувати налаштування
recovery-codes-download-file-header=Зберігайте ці коди відновлення в безпечному місці.
recovery-codes-download-file-description=Коди відновлення — це одноразові паролі, які дозволяють Вам увійти до свого облікового запису, якщо у Вас немає доступу до автентифікатора.
recovery-codes-download-file-date=Ці коди були згенеровані
recovery-codes-label-default=Коди відновлення

# WebAuthn
webauthn-display-name=Ключ безпеки
webauthn-help-text=Використовуйте свій ключ безпеки для входу.
webauthn-passwordless-display-name=Ключ безпеки
webauthn-passwordless-help-text=Використовуйте свій ключ безпеки для входу без пароля.
webauthn-login-title=Ключ безпеки для входу
webauthn-registration-title=Реєстрація ключа безпеки
webauthn-available-authenticators=Доступні ключі безпеки
webauthn-unsupported-browser-text=WebAuthn не підтримується цим браузером. Спробуйте інший або зверніться до адміністратора.
webauthn-doAuthenticate=Увійти за допомогою ключа безпеки
webauthn-createdAt-label=Створено

# WebAuthn Error
webauthn-error-title=Помилка ключа безпеки
webauthn-error-registration=Не вдалося зареєструвати ваш ключ безпеки.<br/> {0}
webauthn-error-api-get=Помилка автентифікації за допомогою ключа безпеки.<br/> {0}
webauthn-error-different-user=Перший автентифікований користувач не є тим, хто автентифікований ключем безпеки.
webauthn-error-auth-verification=Результат автентифікації ключа безпеки невірний.<br/> {0}
webauthn-error-register-verification=Результат реєстрації ключа безпеки невірний.<br/> {0}
webauthn-error-user-not-found=Невідомий користувач автентифікований за допомогою ключа безпеки.

# Identity provider
identity-provider-redirector=Підключіться до іншого провайдера облікових записів
identity-provider-login-label=Або увійдіть за допомогою
idp-email-verification-display-name=Підтвердження електронної пошти
idp-email-verification-help-text=Підв''яжіть свій обліковий запис, підтвердивши свою електронну адресу.
idp-username-password-form-display-name=Ім''я користувача та пароль
idp-username-password-form-help-text=Підв''яжіть свій обліковий запис, увійшовши в систему.

finalDeletionConfirmation=Якщо ви видалите свій обліковий запис, його неможливо буде відновити. Щоб зберегти обліковий запис, натисніть «Скасувати».
irreversibleAction=Ця дія незворотна
deleteAccountConfirm=Видалити підтвердження облікового запису

deletingImplies=Видалення Вашого облікового запису передбачає:
errasingData=Видалення усіх Ваших даних
loggingOutImmediately=Негайний вихід із системи
accountUnusable=Будь-яке подальше використання застосунку буде неможливим з цим обліковим записом
userDeletedSuccessfully=Користувача успішно видалено

access-denied=У доступі відмовлено
access-denied-when-idp-auth=У доступі відмовлено під час автентифікації за допомогою {0}

frontchannel-logout.title=Вихід
frontchannel-logout.message=Ви виходите з наступних застосунків
logoutConfirmTitle=Вихід
logoutConfirmHeader=Ви бажаєте вийти?
doLogout=Вийти

readOnlyUsernameMessage=Ви не можете оновити своє ім''я користувача, оскільки воно доступне лише для читання.
