{"realm": "parc-auto", "displayName": "Parc Auto", "enabled": true, "accessTokenLifespan": 3600, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "refreshTokenMaxReuse": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": true, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": true, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"name": "DIRECTEUR_GENERAL", "description": "Directeur gén<PERSON>l - Accès complet à toutes les fonctionnalités"}, {"name": "RESPONSABLE_SERVICE_GENERAUX", "description": "Responsable des services généraux - Gestion des ressources et astreintes"}, {"name": "CONSULTANT", "description": "Consultant - Accès limité aux fonctionnalités de base"}, {"name": "PROJECT_MANAGER", "description": "Chef de projet - Gestion des missions et projets"}, {"name": "FINANCE_MANAGER", "description": "Responsable financier - Gestion des aspects financiers et remboursements"}]}, "clients": [{"clientId": "ms_reservation", "enabled": true, "publicClient": false, "serviceAccountsEnabled": true, "standardFlowEnabled": true, "directAccessGrantsEnabled": true, "redirectUris": ["http://localhost:9006/*", "http://localhost:9006/swagger-ui/*"], "webOrigins": ["http://localhost:9006"], "attributes": {"access.token.lifespan": "3600"}}, {"clientId": "ms_astreint", "enabled": true, "publicClient": false, "serviceAccountsEnabled": true, "standardFlowEnabled": true, "directAccessGrantsEnabled": true, "redirectUris": ["http://localhost:9007/*", "http://localhost:9007/swagger-ui/*"], "webOrigins": ["http://localhost:9007"], "attributes": {"access.token.lifespan": "3600"}}, {"clientId": "ms_vehicule", "enabled": true, "publicClient": false, "serviceAccountsEnabled": true, "standardFlowEnabled": true, "directAccessGrantsEnabled": true, "redirectUris": ["http://localhost:9003/*", "http://localhost:9003/swagger-ui/*"], "webOrigins": ["http://localhost:9003"], "attributes": {"access.token.lifespan": "3600"}}, {"clientId": "ms_incident", "enabled": true, "publicClient": false, "serviceAccountsEnabled": true, "standardFlowEnabled": true, "directAccessGrantsEnabled": true, "redirectUris": ["http://localhost:9002/*", "http://localhost:9002/swagger-ui/*"], "webOrigins": ["http://localhost:9002"], "attributes": {"access.token.lifespan": "3600"}}, {"clientId": "ms_mission", "enabled": true, "publicClient": false, "serviceAccountsEnabled": true, "standardFlowEnabled": true, "directAccessGrantsEnabled": true, "redirectUris": ["http://localhost:9005/*", "http://localhost:9005/swagger-ui/*"], "webOrigins": ["http://localhost:9005"], "attributes": {"access.token.lifespan": "3600"}}, {"clientId": "ms_notification", "enabled": true, "publicClient": false, "serviceAccountsEnabled": true, "standardFlowEnabled": true, "directAccessGrantsEnabled": true, "redirectUris": ["http://localhost:9004/*", "http://localhost:9004/swagger-ui/*"], "webOrigins": ["http://localhost:9004"], "attributes": {"access.token.lifespan": "3600"}}], "users": [{"username": "admin.parc", "enabled": true, "emailVerified": true, "firstName": "Admin", "lastName": "Parc Auto", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "admin123", "temporary": false}], "realmRoles": ["DIRECTEUR_GENERAL"]}, {"username": "consultant.test", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "Consultant", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "test123", "temporary": false}], "realmRoles": ["CONSULTANT"]}, {"username": "manager.test", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "Manager", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "test123", "temporary": false}], "realmRoles": ["PROJECT_MANAGER"]}, {"username": "finance.test", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "Finance", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "test123", "temporary": false}], "realmRoles": ["FINANCE_MANAGER"]}, {"username": "service.test", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "Service", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "test123", "temporary": false}], "realmRoles": ["RESPONSABLE_SERVICE_GENERAUX"]}]}