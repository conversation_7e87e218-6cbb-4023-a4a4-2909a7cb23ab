image: maven:3-eclipse-temurin-17

variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Cache pour l'analyse Sonar
  GIT_DEPTH: "0"  # Récupère tout l'historique git (branches/tags)

stages:
  - sonarqube-check
  - sonarqube-vulnerability-report
  - package
  - build_deploy

sonarqube-check:
  stage: sonarqube-check
  tags:
    - sonarqube-runner
  script:
    - mvn verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'master'
    - if: $CI_COMMIT_BRANCH == 'main'
    - if: $CI_COMMIT_BRANCH == 'develop'

sonarqube-vulnerability-report:
  stage: sonarqube-vulnerability-report
  tags:
    - sonarqube-runner
  script:
    - 'curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=ms_astreint&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
  allow_failure: true
  artifacts:
    expire_in: 1 day
    reports:
      sast: gl-sast-sonar-report.json
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'master'
    - if: $CI_COMMIT_BRANCH == 'main'
    - if: $CI_COMMIT_BRANCH == 'develop'

package:
  stage: package
  tags:
    - sonarqube-runner
  script:
    - mvn clean package -DskipTests
  artifacts:
    paths:
      - target/*.jar
    expire_in: 1 week

build_deploy:
  stage: build_deploy
  tags:
    - sonarqube-runner
  script:
    - sudo podman build -t ms-astreint .
    - sudo podman run -d --replace --name ms-astreint -p 9007:9007 -v /mnt/nfs_storage:/app/storage:z localhost/ms-astreint:latest
    - sudo podman generate systemd --name ms-astreint --new --files
    - sudo mkdir -p /etc/systemd/system/
    - sudo mv container-ms-astreint.service /etc/systemd/system/ms-astreint.service
    - sudo systemctl daemon-reload
    - sudo systemctl enable ms-astreint.service
    - sudo systemctl start ms-astreint.service
    - sudo systemctl status ms-astreint.service

