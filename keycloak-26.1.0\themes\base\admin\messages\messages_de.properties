invalidPasswordMinLengthMessage=Ungültiges Passwort: muss mindestens {0} Zeichen beinhalten.
invalidPasswordMinLowerCaseCharsMessage=Ungültiges Passwort: muss mindestens {0} Kleinbuchstaben beinhalten.
invalidPasswordMinDigitsMessage=Ungültiges Passwort: muss mindestens {0} Ziffern beinhalten.
invalidPasswordMinUpperCaseCharsMessage=Ungültiges Passwort: muss mindestens {0} Großbuchstaben beinhalten.
invalidPasswordMinSpecialCharsMessage=Ungültiges Passwort: muss mindestens {0} Sonderzeichen beinhalten.
invalidPasswordNotUsernameMessage=Ungültiges Passwort: darf nicht identisch mit dem Benutzernamen sein.
invalidPasswordNotEmailMessage=Ungültiges Passwort: darf nicht identisch mit der E-Mail-Adresse sein.
invalidPasswordRegexPatternMessage=Ungültiges Passwort: stimmt nicht mit Regex-Muster überein.
invalidPasswordHistoryMessage=Ungültiges Passwort: darf nicht identisch mit einem der letzten {0} Passwörter sein.
invalidPasswordBlacklistedMessage=Ungültiges Passwort: Passwort ist zu bekannt und auf der schwarzen Liste.
invalidPasswordGenericMessage=Ungültiges Passwort: neues Passwort erfüllt die Passwort-Anforderungen nicht.
invalidPasswordMaxLengthMessage=Ungültiges Passwort: darf maximal {0} Zeichen beinhalten.
error-invalid-email=Ungültige E-Mail-Adresse.
error-invalid-number=Ungültige Nummer.
ldapErrorEditModeMandatory=Der Bearbeitungsmodus ist erforderlich
ldapErrorInvalidCustomFilter=Benutzerdefinierter konfigurierter LDAP-Filter beginnt nicht mit „(“ oder endet nicht mit „)“.
ldapErrorReadTimeoutNotNumber=Lese-Timeout muss eine Zahl sein
ldapErrorMissingClientId=Die Client-ID muss in der Konfiguration angegeben werden, wenn das Realm Rollen Mapping nicht verwendet wird.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Es ist nicht möglich, die Gruppenvererbung beizubehalten und gleichzeitig den Mitgliedschaftstyp UID zu verwenden.
ldapErrorMissingGroupsPathGroup=Der Gruppen Pfad existiert nicht - bitte erstellen Sie zuerst die Gruppe mit dem gewünschten Pfad.
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Passwortrichtlinie validieren ist nur im Bearbeitungsmodus WRITABLE anwendbar
ldapErrorCantWriteOnlyForReadOnlyLdap="nur-schreiben" kann nicht eingestellt werden, wenn der Bearbeitungsmodus nicht WRITABLE ist
ldapErrorCantEnableUnsyncedAndImportOff=Das Importieren von Benutzern kann nicht deaktiviert werden, wenn der Synchronisationsmodus UNSYNCED ist
backchannelLogoutUrlIllegalSchemeError=Backchannel-Logout-URL verwendet ein illegales Schema
clientBaseURLInvalid=Basis-URL ist keine gültige URL
clientRootURLInvalid=Root-URL ist keine gültige URL
clientRedirectURIsIllegalSchemeError=Eine Redirect-URI verwendet ein illegales Schema
backchannelLogoutUrlIsInvalid=Backchannel-Logout-URL ist keine gültige URL


pairwiseMalformedClientRedirectURI=Der Client enthielt eine ungültige Redirect-URI.
clientRedirectURIsInvalid=Eine Redirect-URI ist keine gültige URI
clientRedirectURIsFragmentError=Redirect URIs dürfen kein URI-Fragment enthalten
clientRootURLIllegalSchemeError=Root-URL verwendet ein illegales Schema
clientBaseURLIllegalSchemeError=Basis-URL verwendet ein illegales Schema
pairwiseClientRedirectURIsMissingHost=Client-Redirect-URIs müssen eine gültige Host-Komponente enthalten.
pairwiseClientRedirectURIsMultipleHosts=Ohne einen konfigurierten Sector-Identifier-URI dürfen Client-Redirect-URIs nicht mehrere Host-Komponenten enthalten.
pairwiseRedirectURIsMismatch=Die Client-Redirect-URIs stimmen nicht mit den Redirect-URIs überein, die aus dem Sector-Identifier-URI ermittelt wurden.
error-invalid-blank=Bitte Wert angeben.
error-number-out-of-range-too-small=Das Attribut {0} muss mindestens den Wert {1} haben.
error-number-out-of-range-too-big=Das Attribut {0} muss maximal den Wert {2} haben.
error-invalid-uri-fragment=Ungültiges URL-Fragment.
error-user-attribute-required=Bitte Attribut {0} angeben.
invalidPasswordNotContainsUsernameMessage=Ungültiges Passwort: darf nicht den Benutzernamen enthalten.
ldapErrorConnectionTimeoutNotNumber=Verbindungs-Timeout muss eine Zahl sein
ldapErrorCantWriteOnlyAndReadOnly="Nur schreiben" und "Nur lesen" können nicht gleichzeitig eingestellt werden
ldapErrorCantEnableStartTlsAndConnectionPooling=StartTLS und Verbindungspooling können nicht gleichzeitig aktiviert werden.
clientRootURLFragmentError=Root-URL darf kein URL-Fragment enthalten
pairwiseMalformedSectorIdentifierURI=Fehlerhafte Sector-Identifier-URI.
pairwiseFailedToGetRedirectURIs=Fehler bei der Ermittlung der Redirect URIs aus dem Sector-Identifier-URI.
duplicatedJwksSettings=Der Schalter „JWKS verwenden“ und der Schalter „JWKS URL verwenden“ können nicht gleichzeitig eingeschaltet sein.
error-invalid-value=Ungültiger Wert.
error-empty=Bitte Wert angeben.
error-invalid-length=Das Attribut {0} muss eine Länge zwischen {1} und {2} haben.
error-invalid-length-too-short=Das Attribut {0} muss mindestens die Länge {1} haben.
error-invalid-length-too-long=Das Attribut {0} muss eine maximale Länge von {2} haben.
error-number-out-of-range=Das Attribut {0} muss eine Zahl zwischen {1} und {2} sein.
error-pattern-no-match=Ungültiger Wert.
error-invalid-uri=Ungültige URL.
error-invalid-uri-scheme=Ungültiges URL-Schema.
error-invalid-date=Attribut {0} ist ungültiges Datum.
error-user-attribute-read-only=Attribut {0} ist schreibgeschützt.
error-username-invalid-character={0} enthält ungültiges Zeichen.
error-person-name-invalid-character={0} enthält ungültiges Zeichen.
error-invalid-multivalued-size=Attribut {0} muss mindestens {1} und höchstens {2} Wert(e) haben.
