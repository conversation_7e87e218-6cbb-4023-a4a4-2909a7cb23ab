emailVerificationSubject=Bevestig e-mailadres
emailVerificationBody=Iemand heeft een {2} account aangemaakt met dit e-mailadres. Als u dit was, klikt u op de onderstaande koppeling om uw e-mailadres te bevestigen \n\n{0}\n\nDeze koppeling zal binnen {3} vervallen.\n\nU kunt dit bericht negeren indien u dit account niet heeft aangemaakt.
emailVerificationBodyHtml=<p>Iemand heeft een {2} account aangemaakt met dit e-mailadres. Als u dit was, klikt u op de onderstaande koppeling om uw e-mailadres te bevestigen</p><p><a href="{0}">Koppeling naar e-mailadres bevestiging</a></p><p>Deze koppeling zal binnen {3} vervallen.</p<p>U kunt dit bericht negeren indien u dit account niet heeft aangemaakt.</p>
emailTestSubject=[KEYCLOAK] - SMTP testbericht
emailTestBody=Dit is een testbericht
emailTestBodyHtml=<p>Dit is een testbericht</p>
identityProviderLinkSubject=Koppel {0}
identityProviderLinkBody=Iemand wil uw "{1}" account koppelen met "{0}" account van gebruiker {2}. Als u dit was, klik dan op de onderstaande link om de accounts te koppelen\n\n{3}\n\nDeze link zal over {5} vervallen.\n\nAls u de accounts niet wilt koppelen, negeer dan dit bericht. Als u accounts koppelt, dan kunt u bij {1} inloggen via {0}.
identityProviderLinkBodyHtml=<p>Iemand wil uw "{1}" account koppelen met "{0}" account van gebruiker {2}. Als u dit was, klik dan op de onderstaande link om de accounts te koppelen</p><p><a href="{3}">Link om accounts te koppelen</a></p><p>Deze link zal over {5} vervallen.</p><p>Als u de accounts niet wilt koppelen, negeer dan dit bericht. Als u accounts koppelt, dan kunt u bij {1} inloggen via {0}.</p>
passwordResetSubject=Wijzig wachtwoord
passwordResetBody=Iemand verzocht de aanmeldgegevens van uw {2} account te wijzigen. Als u dit was, klik dan op de onderstaande koppeling om ze te wijzigen.\n\n{0}\n\nDe link en de code zullen binnen {3} vervallen.\n\nAls u uw aanmeldgegevens niet wilt wijzigen, negeer dan dit bericht en er zal niets gewijzigd worden.
passwordResetBodyHtml=<p>Iemand verzocht de aanmeldgegevens van uw {2} account te wijzigen. Als u dit was, klik dan op de onderstaande koppeling om ze te wijzigen.</p><p><a href="{0}">Wijzig aanmeldgegevens</a></p><p>De link en de code zullen binnen {3} vervallen.</p><p>Als u uw aanmeldgegevens niet wilt wijzigen, negeer dan dit bericht en er zal niets gewijzigd worden.</p>
executeActionsSubject=Wijzig uw account
executeActionsBody=Uw beheerder heeft u verzocht uw {2} account te wijzigen. Klik op de onderstaande koppeling om dit proces te starten. \n\n{0}\n\nDeze link zal over {4} vervallen. \n\nAls u niet over dit verzoek op de hoogte was, negeer dan dit bericht om uw account ongewijzigd te laten.
executeActionsBodyHtml=<p>Uw beheerder heeft u verzocht uw {2} account te wijzigen. Klik op de onderstaande koppeling om dit proces te starten.</p><p><a href="{0}">Link naar account wijziging</a></p><p>Deze link zal over {4} vervallen.</p><p>Als u niet over dit verzoek op de hoogte was, negeer dan dit bericht om uw account ongewijzigd te laten.</p>
eventLoginErrorSubject=Inlogfout
eventLoginErrorBody=Er is een foutieve inlogpoging gedetecteerd op uw account om {0} vanuit {1}. Als u dit niet was, neem dan contact op met de beheerder.
eventLoginErrorBodyHtml=<p>Er is een foutieve inlogpoging gedetecteerd op uw account om {0} vanuit {1}. Als u dit niet was, neem dan contact op met de beheerder.</p>
eventRemoveTotpSubject=OTP verwijderd
eventRemoveTotpBody=OTP is verwijderd van uw account om {0} vanuit {1}. Als u dit niet was, neem dan contact op met uw beheerder.
eventRemoveTotpBodyHtml=<p>OTP is verwijderd van uw account om {0} vanuit {1}. Als u dit niet was, neem dan contact op met uw beheerder.</p>
eventUpdatePasswordSubject=Wachtwoord gewijzigd
eventUpdatePasswordBody=Uw wachtwoord is gewijzigd om {0} door {1}. Als u dit niet was, neem dan contact op met uw beheerder.
eventUpdatePasswordBodyHtml=<p>Uw wachtwoord is gewijzigd om {0} door {1}. Als u dit niet was, neem dan contact op met uw beheerder.</p>
eventUpdateTotpSubject=OTP gewijzigd
eventUpdateTotpBody=OTP is gewijzigd voor uw account om {0} door {1}. Als u dit niet was, neem dan contact op met uw beheerder.
eventUpdateTotpBodyHtml=<p>OTP is gewijzigd voor uw account om {0} door {1}. Als u dit niet was, neem dan contact op met uw beheerder.</p>


# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#seconden|1#seconde|1<seconden}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minuten|1#minuut|1<minuten}
linkExpirationFormatter.timePeriodUnit.hours=uur
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dagen|1#dag|1<dagen}
eventRemoveCredentialBodyHtml=<p>Credential {0} is verwijderd van je account op {1} van {2}. Was jij dit niet? Neem contact op met een beheerder.</p>
requiredAction.UPDATE_PASSWORD=Wijzig wachtwoord
eventUserDisabledByTemporaryLockoutSubject=Gebruiker tijdelijk geblokkeerd
eventUserDisabledByPermanentLockoutSubject=Gebruiker permanent geblokkeerd
requiredAction.UPDATE_PROFILE=Wijzig profiel
requiredAction.VERIFY_EMAIL=Verifieer e-mailadres
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Genereer herstelcodes
emailUpdateConfirmationSubject=Verifieer nieuw e-mailadres
requiredAction.TERMS_AND_CONDITIONS=Algemene voorwaarden
emailVerificationBodyCodeHtml=<p>Verifieer je e-mailadres door de volgende code in te voeren: </p><p><b>{0}</b></p>
