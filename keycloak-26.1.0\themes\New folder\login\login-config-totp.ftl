<#import "template.ftl" as layout>
<#import "password-commons.ftl" as passwordCommons>
<@layout.registrationLayout displayRequiredFields=false displayMessage=!messagesPerField.existsError('totp','userLabel'); section>

    <#if section = "header">
        <!--${msg("loginTotpTitle")}-->
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form-wrapper">
                <div id="login-container">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("loginTotpTitle")}</h1>
                        <p class="form-subtitle">${msg("loginTotpStep3DeviceName")}</p>
                    </div>

                    <div class="totp-setup-container">
                        <ol class="totp-setup-steps">
                            <li>
                                <h3 class="step-title">${msg("loginTotpStep1")}</h3>
                                <div class="app-list">
                                    <#list totp.supportedApplications as app>
                                        <div class="app-item">
                                            <span class="app-icon">
                                                <i class="fa fa-mobile-alt" aria-hidden="true"></i>
                                            </span>
                                            <span class="app-name">${msg(app)}</span>
                                        </div>
                                    </#list>
                                </div>
                            </li>

                            <#if mode?? && mode = "manual">
                                <li>
                                    <h3 class="step-title">${msg("loginTotpManualStep2")}</h3>
                                    <div class="secret-key-container">
                                        <span class="secret-key">${totp.totpSecretEncoded}</span>
                                    </div>
                                    <div class="switch-mode">
                                        <a href="${totp.qrUrl}" class="mode-link">
                                            <i class="fa fa-qrcode" aria-hidden="true"></i>
                                            ${msg("loginTotpScanBarcode")}
                                        </a>
                                    </div>
                                </li>
                                <li>
                                    <h3 class="step-title">${msg("loginTotpManualStep3")}</h3>
                                    <div class="totp-details">
                                        <div class="totp-detail-item">
                                            <span class="detail-label">${msg("loginTotpType")}:</span>
                                            <span class="detail-value">${msg("loginTotp." + totp.policy.type)}</span>
                                        </div>
                                        <div class="totp-detail-item">
                                            <span class="detail-label">${msg("loginTotpAlgorithm")}:</span>
                                            <span class="detail-value">${totp.policy.getAlgorithmKey()}</span>
                                        </div>
                                        <div class="totp-detail-item">
                                            <span class="detail-label">${msg("loginTotpDigits")}:</span>
                                            <span class="detail-value">${totp.policy.digits}</span>
                                        </div>
                                        <#if totp.policy.type = "totp">
                                            <div class="totp-detail-item">
                                                <span class="detail-label">${msg("loginTotpInterval")}:</span>
                                                <span class="detail-value">${totp.policy.period}</span>
                                            </div>
                                        <#elseif totp.policy.type = "hotp">
                                            <div class="totp-detail-item">
                                                <span class="detail-label">${msg("loginTotpCounter")}:</span>
                                                <span class="detail-value">${totp.policy.initialCounter}</span>
                                            </div>
                                        </#if>
                                    </div>
                                </li>
                            <#else>
                                <li>
                                    <h3 class="step-title">${msg("loginTotpStep2")}</h3>
                                    <div class="qr-code-container">
                                        <img class="qr-code" src="data:image/png;base64, ${totp.totpSecretQrCode}" alt="QR Code">
                                    </div>
                                    <div class="switch-mode">
                                        <a href="${totp.manualUrl}" class="mode-link">
                                            <i class="fa fa-keyboard" aria-hidden="true"></i>
                                            ${msg("loginTotpUnableToScan")}
                                        </a>
                                    </div>
                                </li>
                            </#if>
                            <li>
                                <h3 class="step-title">${msg("loginTotpStep3")}</h3>
                                <p class="step-description">${msg("loginTotpStep3DeviceName")}</p>
                            </li>
                        </ol>
                    </div>

                    <form action="${url.loginAction}" id="kc-totp-settings-form" method="post">
                        <div class="form-floating">
                            <input type="text" id="totp" name="totp" autocomplete="off" class="form-control"
                                   aria-invalid="<#if messagesPerField.existsError('totp')>true</#if>"
                                   placeholder=" " />
                            <label for="totp">${msg("authenticatorCode")} <span class="required">*</span></label>
                            <span class="form-icon otp-icon"></span>

                            <#if messagesPerField.existsError('totp')>
                                <span id="input-error-otp-code" class="error-message" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('totp'))?no_esc}
                                </span>
                            </#if>
                        </div>

                        <div class="form-floating">
                            <input type="text" id="userLabel" name="userLabel" autocomplete="off" class="form-control"
                                   aria-invalid="<#if messagesPerField.existsError('userLabel')>true</#if>"
                                   placeholder=" " />
                            <label for="userLabel">${msg("loginTotpDeviceName")} <#if totp.otpCredentials?size gte 1><span class="required">*</span></#if></label>
                            <span class="form-icon device-icon"></span>

                            <#if messagesPerField.existsError('userLabel')>
                                <span id="input-error-otp-label" class="error-message" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('userLabel'))?no_esc}
                                </span>
                            </#if>
                        </div>

                        <div class="form-group">
                            <@passwordCommons.logoutOtherSessions/>
                        </div>

                        <input type="hidden" id="totpSecret" name="totpSecret" value="${totp.totpSecret}" />
                        <#if mode??><input type="hidden" id="mode" name="mode" value="${mode}"/></#if>

                        <div class="form-buttons">
                            <#if isAppInitiatedAction??>
                                <button class="login-button" id="saveTOTPBtn" type="submit">
                                    <span class="button-text">${msg("doSubmit")}</span>
                                    <span class="button-icon"></span>
                                </button>
                                <button class="cancel-button" id="cancelTOTPBtn" name="cancel-aia" value="true" type="submit">
                                    ${msg("doCancel")}
                                </button>
                            <#else>
                                <button class="login-button" id="saveTOTPBtn" type="submit">
                                    <span class="button-text">${msg("doSubmit")}</span>
                                    <span class="button-icon"></span>
                                </button>
                            </#if>
                        </div>
                    </form>
                </div>
            </div>
            <div class="login-illustration">
                <img src="${url.resourcesPath}/img/OTP9.jpg" alt="Login illustration">
            </div>
        </div>
    </#if>
</@layout.registrationLayout>
