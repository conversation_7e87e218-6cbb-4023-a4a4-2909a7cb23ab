<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('username') displayInfo=(realm.password && realm.registrationAllowed && !registrationDisabled??); section>
    <#if section = "header">
        ${msg("loginAccountTitle")}
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form">
                <div id="kc-form-wrapper">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("loginAccountTitle")}</h1>
                        <p class="form-subtitle">${msg("loginAccountSubtitle")}</p>
                    </div>

                    <#if realm.password>
                        <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}"
                              method="post">
                            <#if !usernameHidden??>
                                <div class="form-floating">
                                    <input tabindex="1" 
                                           id="username"
                                           class="form-control ${messagesPerField.existsError('username')?then('is-invalid', '')}"
                                           name="username"
                                           value="${(login.username!'')}"
                                           type="text"
                                           autofocus
                                           autocomplete="username"
                                           placeholder=" "
                                           aria-invalid="<#if messagesPerField.existsError('username')>true</#if>"
                                    />
                                    <#if !realm.loginWithEmailAllowed>
                                        <span class="form-icon username-icon" aria-hidden="true"></span>
                                    <#elseif !realm.registrationEmailAsUsername>
                                        <span class="form-icon username-icon" aria-hidden="true"></span>
                                    <#else>
                                        <span class="form-icon email-icon" aria-hidden="true"></span>
                                    </#if>
                                    <label for="username">
                                        <#if !realm.loginWithEmailAllowed>${msg("username")}
                                        <#elseif !realm.registrationEmailAsUsername>${msg("usernameOrEmail")}
                                        <#else>${msg("email")}
                                        </#if>
                                    </label>

                                    <#if messagesPerField.existsError('username')>
                                        <span id="input-error-username" class="error-message" aria-live="polite">
                                            ${kcSanitize(messagesPerField.get('username'))?no_esc}
                                        </span>
                                    </#if>
                                </div>
                            </#if>

                            <div class="form-group">
                                <div id="kc-form-options">
                                    <#if realm.rememberMe && !usernameHidden??>
                                        <div class="checkbox">
                                            <label>
                                                <#if login.rememberMe??>
                                                    <input tabindex="3" id="rememberMe" name="rememberMe" type="checkbox" checked> ${msg("rememberMe")}
                                                <#else>
                                                    <input tabindex="3" id="rememberMe" name="rememberMe" type="checkbox"> ${msg("rememberMe")}
                                                </#if>
                                            </label>
                                        </div>
                                    </#if>
                                </div>
                            </div>

                            <div class="form-group">
                                <input tabindex="4"
                                       class="btn btn-primary btn-block btn-lg"
                                       name="login"
                                       id="kc-login"
                                       type="submit"
                                       value="${msg("doLogIn")}"
                                />
                            </div>
                        </form>
                    </#if>
                </div>
            </div>
        </div>

    <#elseif section = "info">
        <#if realm.password && realm.registrationAllowed && !registrationDisabled??>
            <div id="kc-registration">
                <span>${msg("noAccount")} <a tabindex="6" href="${url.registrationUrl}">${msg("doRegister")}</a></span>
            </div>
        </#if>
    <#elseif section = "socialProviders" >
        <#if realm.password && social?? && social.providers?has_content>
            <div id="kc-social-providers" class="${properties.kcFormSocialAccountSectionClass!}">
                <hr/>
                <h4>${msg("identity-provider-login-label")}</h4>

                <ul class="${properties.kcFormSocialAccountListClass!} <#if social.providers?size gt 3>${properties.kcFormSocialAccountListGridClass!}</#if>">
                    <#list social.providers as p>
                        <a id="social-${p.alias}" class="${properties.kcFormSocialAccountListButtonClass!} <#if social.providers?size gt 3>${properties.kcFormSocialAccountGridItem!}</#if>"
                                type="button" href="${p.loginUrl}">
                            <#if p.iconClasses?has_content>
                                <i class="${properties.kcCommonLogoIdP!} ${p.iconClasses!}" aria-hidden="true"></i>
                                <span class="${properties.kcFormSocialAccountNameClass!} kc-social-icon-text">${p.displayName!}</span>
                            <#else>
                                <span class="${properties.kcFormSocialAccountNameClass!}">${p.displayName!}</span>
                            </#if>
                        </a>
                    </#list>
                </ul>
            </div>
        </#if>
    </#if>

</@layout.registrationLayout>
