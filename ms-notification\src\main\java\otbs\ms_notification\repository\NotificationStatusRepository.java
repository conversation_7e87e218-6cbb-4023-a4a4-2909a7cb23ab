package otbs.ms_notification.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import otbs.ms_notification.model.NotificationStatus;
import otbs.ms_notification.model.StatutNotification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface NotificationStatusRepository extends JpaRepository<NotificationStatus, Long> {

    List<NotificationStatus> findByDestinataireOrderByDateCreationDesc(String destinataire);

    Page<NotificationStatus> findByDestinataireOrderByDateCreationDesc(String destinataire, Pageable pageable);

    List<NotificationStatus> findByDestinataireAndStatutOrderByDateCreationDesc(String destinataire, StatutNotification statut);

    List<NotificationStatus> findByDestinataireAndStatutNotOrderByDateCreationDesc(String destinataire, StatutNotification statut);

    Page<NotificationStatus> findByDestinataireAndStatutNotOrderByDateCreationDesc(String destinataire, StatutNotification statut, Pageable pageable);

    Long countByDestinataireAndStatut(String destinataire, StatutNotification statut);

    Optional<NotificationStatus> findByNotificationIdAndDestinataire(Long notificationId, String destinataire);

    @Query("SELECT ns FROM NotificationStatus ns WHERE ns.destinataire = :destinataire AND ns.statut = :statut ORDER BY ns.dateCreation DESC")
    List<NotificationStatus> findArchivedNotifications(@Param("destinataire") String destinataire, @Param("statut") StatutNotification statut);

    @Modifying
    @Query("UPDATE NotificationStatus ns SET ns.statut = :newStatut, ns.dateLecture = CURRENT_TIMESTAMP WHERE ns.destinataire = :destinataire AND ns.statut = :currentStatut")
    int markAllAsReadForUser(@Param("destinataire") String destinataire, @Param("newStatut") StatutNotification newStatut, @Param("currentStatut") StatutNotification currentStatut);

    @Modifying
    @Query("UPDATE NotificationStatus ns SET ns.statut = :newStatut WHERE ns.destinataire = :destinataire AND ns.statut = :currentStatut")
    int archiveAllReadForUser(@Param("destinataire") String destinataire, @Param("newStatut") StatutNotification newStatut, @Param("currentStatut") StatutNotification currentStatut);

    @Modifying
    @Query("DELETE FROM NotificationStatus ns WHERE ns.dateCreation < :cutoffDate")
    int deleteOldNotificationStatuses(@Param("cutoffDate") LocalDateTime cutoffDate);

    List<NotificationStatus> findByNotificationId(Long notificationId);

    Long countByNotificationId(Long notificationId);

    @Query("SELECT ns FROM NotificationStatus ns WHERE ns.notification.id = :notificationId AND ns.statut = :statut")
    List<NotificationStatus> findUnreadStatusesForNotification(@Param("notificationId") Long notificationId, @Param("statut") StatutNotification statut);

    boolean existsByNotificationIdAndDestinataire(Long notificationId, String destinataire);
}
