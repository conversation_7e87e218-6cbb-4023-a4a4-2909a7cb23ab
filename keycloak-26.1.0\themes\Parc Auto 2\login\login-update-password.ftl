<#import "template.ftl" as layout>
<#import "password-commons.ftl" as passwordCommons>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('password','password-confirm'); section>
    <#if section = "header" >

        
        
    <#elseif section = "form">
        <div id="kc-form">
            <div id="kc-form-wrapper">
                <img src="${url.resourcesPath}/img/OTBS.png" alt="Onetech Logo" id="logo"><br>
                <span id="update-password-title">${msg("updatePasswordTitle")}</span>
                <form id="kc-passwd-update-form" class="${properties.kcFormClass!}" action="${url.loginAction}" method="post">
                    <div class="${properties.kcFormGroupClass!}">
                            <div class="pass">
                                <input 
                                    tabindex="3" 
                                    id="password-new" 
                                    class="${properties.kcInputClass!}" 
                                    name="password-new" 
                                    type="password"
                                    autofocus
                                    placeholder="Entrez un nouveau mot de passe"
                                    aria-invalid="<#if messagesPerField.existsError('password','password-confirm')>true</#if>"
                                />
                                <button class="eye" type="button" aria-label="${msg('showPassword')}"
                                        aria-controls="password-new"  data-password-toggle tabindex="4"
                                        data-icon-show="${properties.kcFormPasswordVisibilityIconShow!}" data-icon-hide="${properties.kcFormPasswordVisibilityIconHide!}"
                                        data-label-show="${msg('showPassword')}" data-label-hide="${msg('hidePassword')}">
                                    <i class="${properties.kcFormPasswordVisibilityIconShow!}" aria-hidden="true"></i>
                                </button>
                            </div>

                            <#if messagesPerField.existsError('password')>
                                <span id="input-error-password" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('password'))?no_esc}
                                </span>
                            </#if>
                    </div>
                    <div class="${properties.kcFormGroupClass!}">
                            <div class="pass">
                                <input type="password" id="password-confirm" name="password-confirm" tabindex="3" 
                                    class="${properties.kcInputClass!}" autofocus
                                    autocomplete="new-password"
                                    placeholder="Confirmer votre mot de passe"
                                    aria-invalid="<#if messagesPerField.existsError('password-confirm')>true</#if>"
                                />
                                <button class="eye" type="button" aria-label="${msg('showPassword')}"
                                        aria-controls="password-confirm"  data-password-toggle tabindex="4"
                                        data-icon-show="${properties.kcFormPasswordVisibilityIconShow!}" data-icon-hide="${properties.kcFormPasswordVisibilityIconHide!}"
                                        data-label-show="${msg('showPassword')}" data-label-hide="${msg('hidePassword')}">
                                    <i class="${properties.kcFormPasswordVisibilityIconShow!}" aria-hidden="true"></i>
                                </button>
                            </div>

                            <#if messagesPerField.existsError('password-confirm')>
                                <span id="input-error-password-confirm" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('password-confirm'))?no_esc}
                                </span>
                            </#if>
                    <@passwordCommons.logoutOtherSessions/>

                    </div>


                        <div id="kc-form-buttons" class="${properties.kcFormGroupClass!}">
                            <#if isAppInitiatedAction??>
                                <input tabindex="7" class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonLargeClass!}" name="login" id="kc-login" type="submit" value="${msg("doSubmit")}" />
                                <button class="${properties.kcButtonClass!} ${properties.kcButtonDefaultClass!} ${properties.kcButtonLargeClass!}" type="submit" name="cancel-aia" value="true" />${msg("doCancel")}</button>
                            <#else>
                                <input tabindex="7" class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonBlockClass!} ${properties.kcButtonLargeClass!}" name="login" id="kc-login" type="submit" value="${msg("doSubmit")}" />
                            </#if>
                        </div>

                </form>
            </div>
        </div>
        <script type="module" src="${url.resourcesPath}/js/passwordVisibility.js"></script>
    </#if>
</@layout.registrationLayout>
