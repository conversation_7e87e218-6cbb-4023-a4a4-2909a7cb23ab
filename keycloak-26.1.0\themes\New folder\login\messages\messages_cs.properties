doLogIn=Přihlásit se
doRegister=Registrovat se
doRegisterSecurityKey=Registrovat se
doCancel=Zrušit
doSubmit=Odeslat
doBack=Zpět
doYes=Ano
doNo=Ne
doContinue=Pokračovat
doIgnore=Ignorovat
doAccept=Potvrdit
doDecline=Zamítnout
doForgotPassword=Zapomenuté heslo?
doClickHere=Klikněte zde
doImpersonate=Zosobnit
doTryAgain=Zkusit znovu
doTryAnotherWay=Zkusit jiným způsobem
doConfirmDelete=Potvrdit odstranění
errorDeletingAccount=Nastala chyba při odstraňování účtu
deletingAccountForbidden=Nemáte dostatečná oprávnění k odstranění vašeho vlastního účtu, kontaktujte administrátora.
kerberosNotConfigured=Kerberos není nakonfigurován
kerberosNotConfiguredTitle=Kerberos není nakonfigurován
bypassKerberosDetail=Buď nejste přihlášeni přes Kerberos nebo váš prohlížeč není nastaven pro přihlášení Kerberos. Klepnutím na tlačítko pokračujte k přihlášení jinými způsoby
kerberosNotSetUp=Kerberos není nastaven. Nemůžete se přihlásit.
registerTitle=Registrovat
loginAccountTitle=Přihlásit k vašemu účtu
loginTitle=Přihlásit do {0}
loginTitleHtml={0}
impersonateTitle={0} Zosobnit uživatele
impersonateTitleHtml=<strong>{0}</strong> Zosobnit uživatele
realmChoice=Realm
unknownUser=Neznámý uživatel
loginTotpTitle=Nastavení autentikátoru OTP
loginProfileTitle=Aktualizovat informace o účtu
loginIdpReviewProfileTitle=Aktualizovat informace o účtu
loginTimeout=Přihlašování trvalo příliš dlouho. Přihlašovací proces začíná od začátku.
reauthenticate=Pro pokračování se prosím znovu přihlaste
oauthGrantTitle=Udělit přístup {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Ujistěte se, že důvěřujete {0}. Zjistěte, jak {0} nakládá s daty uživatelů.
oauthGrantReview=Můžete si přečíst
oauthGrantTos=podmínky používání služby.
oauthGrantPolicy=zásady ochrany osobních údajů.
errorTitle=Je nám líto...
errorTitleHtml=<strong>Omlouváme</strong> se ...
emailVerifyTitle=Ověření e-mailu
emailForgotTitle=Zapomněli jste heslo?
updateEmailTitle=Aktualizace e-mailu
emailUpdateConfirmationSentTitle=Potvrzovací e-mail odeslán
emailUpdateConfirmationSent=Potvrzovací e-mail byl odeslán na adresu {0}. Pro dokončení aktualizace e-mailu postupujte podle pokynů.
emailUpdatedTitle=E-mail byl aktualizován
emailUpdated=E-mail účtu byl úspěšně aktualizován na {0}.
updatePasswordTitle=Aktualizace hesla
codeSuccessTitle=Kód úspěchu 
codeErrorTitle=Kód chyby\: {0}
displayUnsupported=Požadovaný typ zobrazení není podporovaný
browserRequired=Pro přihlášení je vyžadován prohlížeč
browserContinue=Pro dokončení přihlášení je vyžadován prohlížeč
browserContinuePrompt=Otevřít prohlížeč a pokračovat v přihlášení? [a/n]:
browserContinueAnswer=a

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Interní
unknown=Neznámé

termsTitle=Smluvní podmínky
termsText=<p>Smluvní podmínky k odsouhlasení</p>
termsPlainText=Smluvní podmínky k odsouhlasení.
termsAcceptanceRequired=Musíte souhlasit s našimi smluvními podmínkami.
acceptTerms=Souhlasím se smluvními podmínkami

recaptchaFailed=Neplatná Recaptcha
recaptchaNotConfigured=Recaptcha je vyžadována, ale není nakonfigurována
consentDenied=Souhlas byl zamítnut.

noAccount=Nový uživatel?
username=Přihlašovací jméno
usernameOrEmail=Přihlašovací jméno nebo e-mail
firstName=Křestní jméno
givenName=Křestní jména
fullName=Celé jméno
lastName=Příjmení
familyName=Příjmení
email=E-mail
password=Heslo
passwordConfirm=Potvrdit heslo
passwordNew=Nové heslo
passwordNewConfirm=Potvrdit nové heslo
hidePassword=Skrýt heslo
showPassword=Zobrazit heslo
rememberMe=Pamatovat si mě
authenticatorCode=Jednorázový kód
address=Adresa
street=Ulice
locality=Město
region=Kraj
postal_code=PSČ
country=Stát
emailVerified=E-mail ověřen
website=Webová stránka
phoneNumber=Telefonní číslo
phoneNumberVerified=Telefonní číslo ověřeno
gender=Pohlaví
birthday=Datum narození
zoneinfo=Časová zóna
gssDelegationCredential=GSS Delegované Oprávnění
logoutOtherSessions=Odhlásit se z ostatních zařízení

profileScopeConsentText=Uživatelský profil
emailScopeConsentText=E-mailová adresa
addressScopeConsentText=Adresa
phoneScopeConsentText=Telefonní číslo
offlineAccessScopeConsentText=Přístup offline
samlRoleListScopeConsentText=Moje role
rolesScopeConsentText=Uživatelské role

restartLoginTooltip=Začít s přihlašováním od začátku

loginTotpIntro=Musíte si nakonfigurovat generátor jednorázových kódů (OTP) pro přístup k účtu
loginTotpStep1=Nainstalujte do mobilu jednu z následujících aplikací
loginTotpStep2=Otevřete aplikaci a naskenujte čárový kód
loginTotpStep3=Zadejte jednorázový kód poskytnutý aplikací a klepnutím na tlačítko Odeslat dokončete nastavení
loginTotpStep3DeviceName=Zadejte název zařízení pro jednodušší správu jednorázových kódů (OTP) zařízení.
loginTotpManualStep2=Otevřete aplikaci a zadejte klíč
loginTotpManualStep3=Použijte následující hodnoty konfigurace, pokud aplikace umožňuje jejich nastavení
loginTotpUnableToScan=Nelze skenovat?
loginTotpScanBarcode=Skenovat čárový kód?
loginCredential=Přihlašovací údaje
loginOtpOneTime=Jednorázový kód
loginTotpType=Typ
loginTotpAlgorithm=Algoritmus
loginTotpDigits=Číslice
loginTotpInterval=Interval
loginTotpCounter=Počítadlo
loginTotpDeviceName=Název zařízení

loginTotp.totp=Založeno na čase
loginTotp.hotp=Založeno na počítadle

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=Vyberte metodu přihlášení

oauthGrantRequest=Poskytujete tyto přístupová oprávnění?
inResource=v

oauth2DeviceVerificationTitle=Přihlášení na zařízení
verifyOAuth2DeviceUserCode=Zadejte kód z vašeho zařízení a klikněte na Odeslat
oauth2DeviceInvalidUserCodeMessage=Nesprávný kód, zkuste to prosím znovu.
oauth2DeviceExpiredUserCodeMessage=Platnost kódu vypršela. Vraťte se prosím do vašeho zařízení a zkuste se připojit znovu.
oauth2DeviceVerificationCompleteHeader=Úspěšné přihlášení v zařízení
oauth2DeviceVerificationCompleteMessage=Můžete zavřít toto okno prohlížeče a vrátit se do vašeho zařízení.
oauth2DeviceVerificationFailedHeader=Selhalo přihlášení v zařízení
oauth2DeviceVerificationFailedMessage=Můžete zavřít toto okno prohlížeče a vrátit se do vašeho zařízení a zkusit se znovu připojit.
oauth2DeviceConsentDeniedMessage=Připojení zařízení odmítnuto.
oauth2DeviceAuthorizationGrantDisabledMessage=Klient nemá povoleno iniciovat OAuth 2.0 Device Authorization Grant. Flow je pro klienta zakázáno.

emailVerifyInstruction1=Byl Vám zaslán e-mail s pokyny k ověření vaší e-mailové adresy.
emailVerifyInstruction2=Nezískali jste v e-mailu ověřovací kód?
emailVerifyInstruction3=znovu odeslat e-mail.

emailLinkIdpTitle=Odkaz {0}
emailLinkIdp1=Byl vám zaslán e-mail s pokyny k propojení {0} účtu {1} s vaším účtem {2}.
emailLinkIdp2=Nezískali jste v e-mailu ověřovací kód?
emailLinkIdp3=znovu odeslat e-mail.
emailLinkIdp4=Pokud jste již ověřili e-mail v jiném prohlížeči
emailLinkIdp5=pokračovat.

backToLogin=&laquo; Zpět k přihlášení

emailInstruction=Zadejte své uživatelské jméno nebo e-mailovou adresu a my vám zašleme pokyny k vytvoření nového hesla.
emailInstructionUsername=Zadejte své uživatelské jméno a my vám zašleme pokyny k vytvoření nového hesla.

copyCodeInstruction=Zkopírujte tento kód a vložte jej do své aplikace:

pageExpiredTitle=Vypršela platnost stránky
pageExpiredMsg1=Pro restart procesu přihlášení
pageExpiredMsg2=Pokračovat v procesu přihlášení

personalInfo=Osobní údaje:
role_admin=Administrátor realmu
role_realm-admin=Administrátor realmu
role_create-realm=Vytvořit realm
role_create-client=Vytvořit klienta
role_view-realm=Zobrazit realm
role_view-users=Zobrazit uživatele
role_view-applications=Zobrazit aplikace
role_view-clients=Zobrazit klienty
role_view-events=Zobrazit události
role_view-identity-providers=Zobrazit poskytovatele identity
role_manage-realm=Spravovat realm
role_manage-users=Spravovat uživatele
role_manage-applications=Spravovat aplikace
role_manage-identity-providers=Spravovat poskytovatele identity
role_manage-clients=Spravovat klienty
role_manage-events=Spravovat události
role_view-profile=Zobrazit profil
role_manage-account=Spravovat účet
role_manage-account-links=Spravovat odkazy na účet
role_read-token=Číst token
role_offline-access=Přístup offline
client_account=Účet
client_account-console=Uživatelská konzola
#client_security-admin-console=Security Admin Console
#client_admin-cli=Admin CLI
client_realm-management=Správa realmu
client_broker=Broker

requiredFields=Vyžadované položky

invalidUserMessage=Neplatné jméno nebo heslo.
invalidUsernameMessage=Neplatné jméno.
invalidUsernameOrEmailMessage=Neplatné jméno nebo e-mail.
invalidPasswordMessage=Neplatné heslo.
invalidEmailMessage=Neplatný e-mail.
accountDisabledMessage=Účet je neplatný, kontaktujte administrátora.
accountTemporarilyDisabledMessage=Neplatné jméno nebo heslo.
accountPermanentlyDisabledMessage=Neplatné jméno nebo heslo.
accountTemporarilyDisabledMessageTotp=Neplatný kód ověřování.
accountPermanentlyDisabledMessageTotp=Neplatný kód ověřování.
expiredCodeMessage=Platnost přihlášení vypršela. Přihlaste se znovu.
expiredActionMessage=Akce vypršela. Pokračujte přihlášením.
expiredActionTokenNoSessionMessage=Akce vypršela.
expiredActionTokenSessionExistsMessage=Akce vypršela. Začněte znovu
sessionLimitExceeded=Příliš mnoho navázaných spojení

missingFirstNameMessage=Zadejte prosím jméno.
missingLastNameMessage=Zadejte prosím příjmení.
missingEmailMessage=Zadejte prosím e-mail.
missingUsernameMessage=Zadejte prosím uživatelské jméno.
missingPasswordMessage=Zadejte prosím heslo.
missingTotpMessage=Zadejte prosím kód ověřovatele.
missingTotpDeviceNameMessage=Zadejte prosím jméno zařízení.
notMatchPasswordMessage=Hesla se neshodují.

error-invalid-value=Nesprávná hodnota.
error-invalid-blank=Zadejte prosím hodnotu.
error-empty=Zadejte prosím hodnotu.
error-invalid-length=Délka musí být mezi {1} a {2}.
error-invalid-length-too-short=Minimální délka je {1}.
error-invalid-length-too-long=Maximální délka je {2}.
error-invalid-email=Nesprávná e-mailová adresa.
error-invalid-number=Nesprávné číslo.
error-number-out-of-range=Číslo musí být mezi {1} a {2}.
error-number-out-of-range-too-small=Minimální hodnota čísla je {1}.
error-number-out-of-range-too-big=Maximální hodnota čísla je {2}.
error-pattern-no-match=Nesprávná hodnota.
error-invalid-uri=Nesprávná URL adresa.
error-invalid-uri-scheme=Nesprávné URL schéma.
error-invalid-uri-fragment=Nesprávný fragment URL.
error-user-attribute-required=Zadejte prosím tuto položku.
error-invalid-date=Nesprávné datum.
error-user-attribute-read-only=Tato položka je jen ke čtení.
error-username-invalid-character=Hodnota obsahuje nevalidní znak.
error-person-name-invalid-character=Hodnota obsahuje nevalidní znak.
error-reset-otp-missing-id=Vyberte prosím OTP konfiguraci.

invalidPasswordExistingMessage=Neplatné existující heslo.
invalidPasswordBlacklistedMessage=Neplatné heslo: heslo je na černé listině.
invalidPasswordConfirmMessage=Potvrzení hesla se neshoduje.
invalidTotpMessage=Neplatný kód ověřování.

usernameExistsMessage=Uživatelské jméno již existuje.
emailExistsMessage=E-mail již existuje.

federatedIdentityExistsMessage=Uživatel s {0} {1} již existuje. Přihlaste se ke správě účtu a propojte účet.
federatedIdentityUnavailableMessage=Uživatel {0} přihlášený poskytovatelem identit {1} neexistuje. Kontaktujte prosím administrátora.
federatedIdentityUnmatchedEssentialClaimMessage=Token identity vydaný poskytovatelem identity neodpovídá nakonfigurovanému essential claim. Kontaktujte prosím administrátora.

confirmLinkIdpTitle=Účet již existuje
federatedIdentityConfirmLinkMessage=Uživatel s {0} {1} již existuje. Jak chcete pokračovat?
federatedIdentityConfirmReauthenticateMessage=Ověřte jako {0} k propojení účtu {1}
nestedFirstBrokerFlowMessage={0} uživatel {1} není propojen s žádným známým uživatelem.
confirmLinkIdpReviewProfile=Zkontrolujte profil
confirmLinkIdpContinue=Přidat do existujícího účtu

configureTotpMessage=Chcete-li aktivovat účet, musíte nastavit službu Mobile Authenticator.
configureBackupCodesMessage=Pro aktivaci svého účtu musíte nakonfigurovat záložní kódy.
updateProfileMessage=Pro aktivaci účtu musíte aktualizovat svůj uživatelský profil.
updatePasswordMessage=Pro aktivaci účtu musíte provést aktualizaci hesla.
updateEmailMessage=Pro aktivaci účtu musíte aktualizovat svou e-mailovou adresu.
resetPasswordMessage=Je třeba změnit heslo.
verifyEmailMessage=Pro aktivaci účtu musíte ověřit vaši e-mailovou adresu.
linkIdpMessage=Potřebujete-li ověřit vaši e-mailovou adresu, propojte svůj účet s {0}.

emailSentMessage=Měli byste brzy obdržet e-mail s dalšími pokyny.
emailSendErrorMessage=Nepodařilo se odeslat e-mail, zkuste to prosím později.

accountUpdatedMessage=Váš účet byl aktualizován.
accountPasswordUpdatedMessage=Vaše heslo bylo aktualizováno.

delegationCompleteHeader=Přihlášení úspěšné
delegationCompleteMessage=Můžete zavřít toto okno prohlížeče a vrátit se do aplikace.
delegationFailedHeader=Přihlášení selhalo
delegationFailedMessage=Můžete zavřít toto okno prohlížeče a vrátit se do aplikace a zkusit se znovu přihlásit.

noAccessMessage=Žádný přístup

invalidPasswordMinLengthMessage=Neplatné heslo: minimální délka {0}.
invalidPasswordMaxLengthMessage=Neplatné heslo: maximální délka {0}.
invalidPasswordMinDigitsMessage=Neplatné heslo: musí obsahovat nejméně {0} číslic.
invalidPasswordMinLowerCaseCharsMessage=Neplatné heslo: musí obsahovat minimálně {0} malé znaky.
invalidPasswordMinUpperCaseCharsMessage=Neplatné heslo: musí obsahovat nejméně {0} velká písmena.
invalidPasswordMinSpecialCharsMessage=Neplatné heslo: musí obsahovat nejméně {0} speciální znaky.
invalidPasswordNotUsernameMessage=Neplatné heslo: nesmí být totožné s uživatelským jménem.
invalidPasswordNotEmailMessage=Neplatné heslo: nesmí být totožné s e-mailovou adresou.
invalidPasswordRegexPatternMessage=Neplatné heslo: neshoduje se vzorem regulérního výrazu.
invalidPasswordHistoryMessage=Neplatné heslo: Nesmí se rovnat žádnému z posledních {0} hesel.
invalidPasswordGenericMessage=Neplatné heslo: nové heslo neodpovídá pravidlům hesla.

failedToProcessResponseMessage=Nepodařilo se zpracovat odpověď
httpsRequiredMessage=Požadováno HTTPS
realmNotEnabledMessage=Realm není povolen
invalidRequestMessage=Neplatná žádost
successLogout=Odhlášení bylo úspěšné
failedLogout=Odhlášení se nezdařilo
unknownLoginRequesterMessage=Neznámý žadatel o přihlášení
loginRequesterNotEnabledMessage=Žadatel o přihlášení není povolen
bearerOnlyMessage=Aplikace bearer-only nemohou iniciovat přihlašování pomocí prohlížeče
standardFlowDisabledMessage=Klient nesmí iniciovat přihlašování prohlížeče s daným typem odpovědi. Standardní tok je pro klienta zakázán.
implicitFlowDisabledMessage=Klient nesmí iniciovat přihlašování prohlížeče s daným typem odpovědi. Implicitní tok je pro klienta zakázán.
invalidRedirectUriMessage=Neplatná adresa přesměrování
unsupportedNameIdFormatMessage=Nepodporovaný NameIDFormat
invalidRequesterMessage=Neplatný žadatel
registrationNotAllowedMessage=Registrace není povolena
resetCredentialNotAllowedMessage=Reset Credential není povoleno

permissionNotApprovedMessage=Oprávnění nebylo schváleno.
noRelayStateInResponseMessage=Chybí relay state v odpovědi od poskytovatele identity.
insufficientPermissionMessage=Nedostatečná oprávnění k propojení identit.
couldNotProceedWithAuthenticationRequestMessage=Nemohu pokračovat s žádostí o ověření poskytovateli identity.
couldNotObtainTokenMessage=Nelze získat token od poskytovatele identity.
unexpectedErrorRetrievingTokenMessage=Neočekávaná chyba při načítání tokenu od poskytovatele identity.
unexpectedErrorHandlingResponseMessage=Neočekávaná chyba při zpracování odpovědi od poskytovatele identity.
identityProviderAuthenticationFailedMessage=Ověření selhalo. Nelze ověřit s poskytovatelem identity.
couldNotSendAuthenticationRequestMessage=Nelze odeslat žádost o ověření poskytovateli identity.
unexpectedErrorHandlingRequestMessage=Neočekávaná chyba při zpracování požadavku na ověření poskytovateli identity.
invalidAccessCodeMessage=Neplatný přístupový kód.
sessionNotActiveMessage=Session není aktivní.
invalidCodeMessage=Došlo k chybě, přihlaste se znovu prostřednictvím své aplikace.
cookieNotFoundMessage=Soubor cookie nenalezen. Ujistěte se prosím, že máte v prohlížeči povolené cookies.
insufficientLevelOfAuthentication=Nebylo dosaženo požadované úrovně autentizace.
identityProviderUnexpectedErrorMessage=Neočekávaná chyba při ověřování s poskytovatelem identity
identityProviderMissingStateMessage=V odpovědi od poskytovatele identit chybí parametr state.
identityProviderMissingCodeOrErrorMessage=V odpovědi poskytovatele identit chybí parametr code nebo error.
identityProviderInvalidResponseMessage=Nevalidní odpověď od poskytovatele identity.
identityProviderInvalidSignatureMessage=Nevalidní podpis v odpovědi od poskytovatele identity.
identityProviderNotFoundMessage=Nelze najít poskytovatele identity s identifikátorem.
identityProviderLinkSuccess=Úspěšně jste ověřili svůj e-mail. Vraťte se prosím zpět do původního prohlížeče a pokračujte tam s přihlašovacími údaji.
staleCodeMessage=Tato stránka již není platná. Vraťte se zpět do aplikace a přihlaste se znovu
realmSupportsNoCredentialsMessage=Realm nepodporuje žádný typ pověření.
credentialSetupRequired=Není možné se přihlásit, je vyžadována konfigurace přístupových údajů.
identityProviderNotUniqueMessage=Realm podporuje více poskytovatelů identity. Nelze určit, s jakým zprostředkovatelem identity se má ověřit.
emailVerifiedMessage=Vaše e-mailová adresa byla ověřena.
emailVerifiedAlreadyMessage=Vaše e-mailová adresa již byla ověřena.
staleEmailVerificationLink=Odkaz, na který jste klikli, je starý odkaz a již není platný. Možná jste již ověřili svůj e-mail?
identityProviderAlreadyLinkedMessage=Federovaná identita vrácená {0} je již propojena s jiným uživatelem.
confirmAccountLinking=Potvrďte propojení účtu {0} poskytovatele identity {1} s vaším účtem.
confirmEmailAddressVerification=Potvrďte platnost e-mailové adresy {0}.
confirmExecutionOfActions=Proveďte následující akce

backToApplication=&laquo; Zpět na aplikaci
missingParameterMessage=Chybějící parametry\: {0}
clientNotFoundMessage=Klient nebyl nalezen.
clientDisabledMessage=Klient byl zneplatněn.
invalidParameterMessage=Neplatný parametr\: {0}
alreadyLoggedIn=Jste již přihlášeni.
differentUserAuthenticated=Jste již v této relaci ověřeni jako jiný uživatel '' {0} ''. Nejdříve se odhlaste.
brokerLinkingSessionExpired=Požadované propojení účtu brokerů, ale aktuální relace již není platná.
proceedWithAction=&raquo; Klikněte zde pro pokračování
acrNotFulfilled=Nebyly naplněny požadavky autentizace

requiredAction.CONFIGURE_TOTP=Konfigurovat OTP
requiredAction.TERMS_AND_CONDITIONS=Smluvní podmínky
requiredAction.UPDATE_PASSWORD=Aktualizace hesla
requiredAction.UPDATE_PROFILE=Aktualizovat profil
requiredAction.VERIFY_EMAIL=Ověřit e-mail
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generování kódů pro obnovení
#requiredAction.webauthn-register-passwordless=Webauthn Register Passwordless

invalidTokenRequiredActions=Požadované akce obsažené v daném odkazu nejsou validní

doX509Login=Budete přihlášeni jako\:
clientCertificate=Klientský X509 certifikát\:
noCertificate=[Žádný certifikát]


pageNotFound=Stránka nenalezena
internalServerError=Nastala interní chyba serveru

console-username=Jméno:
console-password=Heslo:
console-otp=Jednorázové heslo:
console-new-password=Nové heslo:
console-confirm-password=Potvrzení hesla:
console-update-password=Je vyžadována změna hesla.
console-verify-email=Musíte ověřit svou e-mailovou adresu. Odeslali jsme e-mail na {0}, který obsahuje ověřovací kód. Zadejte prosím tento kód do pole níže.
console-email-code=Kód z e-mailu:
console-accept-terms=Souhlasit s podmínkami? [a/n]:
console-accept=a

# Openshift messages
openshift.scope.user_info=Informace o uživateli
openshift.scope.user_check-access=Informace o přístupu uživatele
openshift.scope.user_full=Plný přístup
openshift.scope.list-projects=Seznam projektů

# SAML authentication
saml.post-form.title=Přesměrování přihlášení
saml.post-form.message=Přesměrovávám, čekejte prosím.
saml.post-form.js-disabled=JavaScript není povolený. Důrazně doporučujeme jej povolit. Pro pokračování stiskněte tlačítko níže.
saml.artifactResolutionServiceInvalidResponse=Nepovedlo se rozpoznat SAML artefakt.

#authenticators
otp-display-name=Autentizační Aplikace
otp-help-text=Zadejte ověřovací kód z aplikace.
otp-reset-description=Která konfigurace OTP má být odstraněna?
password-display-name=Heslo
password-help-text=Přihlaste se pomocí hesla.
auth-username-form-display-name=Jméno
auth-username-form-help-text=Začněte přihlášení zadáním svého uživatelského jména
auth-username-password-form-display-name=Jméno a heslo
auth-username-password-form-help-text=Přihlaste se pomocí jména a hesla.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Kód pro obnovu přístupu
auth-recovery-authn-code-form-help-text=Zadejte kód pro obnovu přístupu z dříve vygenerovaného seznamu.
auth-recovery-code-info-message=Zadejte specifikovaný kód pro obnovu.
auth-recovery-code-prompt=Kód pro obnovu #{0}
auth-recovery-code-header=Přihlaste se kódem pro obnovu přístupu
recovery-codes-error-invalid=Nesprávný kód pro obnovu přístupu
recovery-code-config-header=Kódy pro obnovu přístupu
recovery-code-config-warning-title=Tyto kódy pro obnovu nebudou znovu zobrazeny, když tuto stránku opustíte
recovery-code-config-warning-message=Kódy pro obnovu si vytiskněte, stáhněte nebo zkopírujte do správce hesel a držte je v bezpečí. Stornování této konfigurace odstraní kódy pro obnovu z vašeho účtu.
recovery-codes-print=Tisknout
recovery-codes-download=Stáhnout
recovery-codes-copy=Kopírovat
recovery-codes-copied=Zkopírováno
recovery-codes-confirmation-message=Mám tyto kódy bezpečně uložené
recovery-codes-action-complete=Dokončit konfiguraci
recovery-codes-action-cancel=Stornovat konfiguraci
recovery-codes-download-file-header=Uchovejte tyto kódy pro obnovu v bezpečí.
recovery-codes-download-file-description=Kódy pro obnovu jsou hesla pro jednorázové použití, pomocí kterých se můžete přihlásit bez přístupu k autentikátoru.
recovery-codes-download-file-date=Tyto kódy byly generovány
recovery-codes-label-default=Kódy pro obnovu

# WebAuthn
webauthn-display-name=Přístupový klíč
webauthn-help-text=Použijte k přihlášení přístupový klíč.
webauthn-passwordless-display-name=Přístupový klíč
webauthn-passwordless-help-text=Použijte přístupový klíč k přihlášení bez hesla.
webauthn-login-title=Přihlášení přístupovým klíčem
webauthn-registration-title=Registrace přístupového klíče
webauthn-available-authenticators=Dostupné přístupové klíče
webauthn-unsupported-browser-text=WebAuthn není v tomto prohlížeči podporováno. Zkuste jiný prohlížeč nebo kontaktujte svého administrátora.
webauthn-doAuthenticate=Přihlášení přístupovým klíčem
webauthn-createdAt-label=Vytvořeno

# WebAuthn Error
webauthn-error-title=Chyba přístupového klíče
webauthn-error-registration=Selhala registrace vašeho přístupového klíče.<br/> {0}
webauthn-error-api-get=Selhalo přihlášení pomocí přístupového klíče.<br/> {0}
webauthn-error-different-user=První přihlášený uživatel není totožný s uživatelem přihlášeným pomocí přístupového klíče.
webauthn-error-auth-verification=Nevalidní výsledek přihlášení pomocí přístupového klíče.<br/> {0}
webauthn-error-register-verification=Nevalidní výsledek registrace přístupového klíče.<br/> {0}
webauthn-error-user-not-found=Neznámý uživatel přihlášen pomocí přístupového klíče.

# Identity provider
identity-provider-redirector=Propojit s jiným poskytovatelem identit
identity-provider-login-label=Nebo se přihlaste pomocí
idp-email-verification-display-name=Ověření e-mailu
idp-email-verification-help-text=Proveďte spárování účtu ověřením e-mailu.
idp-username-password-form-display-name=Jméno a příjmení
idp-username-password-form-help-text=Proveďte spárování účtu přihlášením.

finalDeletionConfirmation=Pokud svůj účet odstraníte, nemůže být obnoven. Pro zachování účtu klikněte na tlačítko Zrušit.
irreversibleAction=Tuto akci nelze vzít zpět
deleteAccountConfirm=Potvrzení odstranění účtu

deletingImplies=Odstranění vašeho účtu znamená:
errasingData=Smazání všech vašich dat
loggingOutImmediately=Okamžité odhlášení
accountUnusable=Další použití aplikace s tímto účtem nebude možné
userDeletedSuccessfully=Uživatel úspěšně odstraněn

access-denied=Přístup odepřen
access-denied-when-idp-auth=Přístup odepřen při přihlášení pomocí {0}

frontchannel-logout.title=Odhlášení
frontchannel-logout.message=Odhlašujete se z následujících aplikací
logoutConfirmTitle=Odhlašování
logoutConfirmHeader=Chcete se odhlásit?
doLogout=Odhlásit

readOnlyUsernameMessage=Nemůžete aktualizovat své uživatelské jméno, protože je pouze pro čtení.
error-invalid-multivalued-size=Atribut {0} musí mít nejméně {1} {1,choice,0#hodnot|1#hodnotu|1<hodnoty|4<hodnot} a nejvíce {2} {2,choice,0#hodnot|1#hodnotu|1<hodnoty|4<hodnot}.