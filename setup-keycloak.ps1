# Script PowerShell pour configurer Keycloak automatiquement
# Assurez-vous que Keycloak est démarré sur http://localhost:8080

Write-Host "🔐 Configuration automatique de Keycloak pour le projet Parc Auto" -ForegroundColor Green

# Variables
$KEYCLOAK_URL = "http://localhost:8080"
$ADMIN_USER = "admin"
$ADMIN_PASSWORD = "admin"
$REALM_NAME = "parc-auto"

# Fonction pour obtenir un token d'accès admin
function Get-AdminToken {
    $body = @{
        username = $ADMIN_USER
        password = $ADMIN_PASSWORD
        grant_type = "password"
        client_id = "admin-cli"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response.access_token
    }
    catch {
        Write-Host "❌ Erreur lors de l'obtention du token admin: $_" -ForegroundColor Red
        return $null
    }
}

# Fonction pour créer le realm
function Create-Realm {
    param($token)
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $realmConfig = @{
        realm = $REALM_NAME
        enabled = $true
        displayName = "Parc Auto"
        accessTokenLifespan = 3600
        ssoSessionIdleTimeout = 1800
        ssoSessionMaxLifespan = 36000
    } | ConvertTo-Json
    
    try {
        Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms" -Method Post -Headers $headers -Body $realmConfig
        Write-Host "✅ Realm '$REALM_NAME' créé avec succès" -ForegroundColor Green
    }
    catch {
        if ($_.Exception.Response.StatusCode -eq 409) {
            Write-Host "ℹ️ Realm '$REALM_NAME' existe déjà" -ForegroundColor Yellow
        }
        else {
            Write-Host "❌ Erreur lors de la création du realm: $_" -ForegroundColor Red
        }
    }
}

# Fonction pour créer un client
function Create-Client {
    param($token, $clientId, $port)
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $clientConfig = @{
        clientId = $clientId
        enabled = $true
        publicClient = $false
        serviceAccountsEnabled = $true
        standardFlowEnabled = $true
        directAccessGrantsEnabled = $true
        redirectUris = @("http://localhost:$port/*", "http://localhost:$port/swagger-ui/*")
        webOrigins = @("http://localhost:$port")
        attributes = @{
            "access.token.lifespan" = "3600"
        }
    } | ConvertTo-Json -Depth 3
    
    try {
        Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" -Method Post -Headers $headers -Body $clientConfig
        Write-Host "✅ Client '$clientId' créé avec succès" -ForegroundColor Green
    }
    catch {
        if ($_.Exception.Response.StatusCode -eq 409) {
            Write-Host "ℹ️ Client '$clientId' existe déjà" -ForegroundColor Yellow
        }
        else {
            Write-Host "❌ Erreur lors de la création du client '$clientId': $_" -ForegroundColor Red
        }
    }
}

# Fonction pour créer des rôles
function Create-Roles {
    param($token)
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $roles = @(
        "DIRECTEUR_GENERAL",
        "RESPONSABLE_SERVICE_GENERAUX", 
        "CONSULTANT",
        "PROJECT_MANAGER",
        "FINANCE_MANAGER"
    )
    
    foreach ($role in $roles) {
        $roleConfig = @{
            name = $role
            description = "Rôle $role pour l'application Parc Auto"
        } | ConvertTo-Json
        
        try {
            Invoke-RestMethod -Uri "$KEYCLOAK_URL/admin/realms/$REALM_NAME/roles" -Method Post -Headers $headers -Body $roleConfig
            Write-Host "✅ Rôle '$role' créé avec succès" -ForegroundColor Green
        }
        catch {
            if ($_.Exception.Response.StatusCode -eq 409) {
                Write-Host "ℹ️ Rôle '$role' existe déjà" -ForegroundColor Yellow
            }
            else {
                Write-Host "❌ Erreur lors de la création du rôle '$role': $_" -ForegroundColor Red
            }
        }
    }
}

# Script principal
Write-Host "🚀 Début de la configuration..." -ForegroundColor Cyan

# Obtenir le token admin
$token = Get-AdminToken
if (-not $token) {
    Write-Host "❌ Impossible d'obtenir le token admin. Vérifiez que Keycloak est démarré et que les identifiants sont corrects." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Token admin obtenu" -ForegroundColor Green

# Créer le realm
Create-Realm -token $token

# Créer les clients pour chaque microservice
$clients = @{
    "ms_reservation" = 9006
    "ms_astreint" = 9007
    "ms_vehicule" = 9003
    "ms_incident" = 9002
    "ms_mission" = 9005
    "ms_notification" = 9004
}

foreach ($client in $clients.GetEnumerator()) {
    Create-Client -token $token -clientId $client.Key -port $client.Value
}

# Créer les rôles
Create-Roles -token $token

Write-Host "🎉 Configuration de Keycloak terminée !" -ForegroundColor Green
Write-Host "📝 Prochaines étapes:" -ForegroundColor Cyan
Write-Host "   1. Connectez-vous à http://localhost:8080/admin" -ForegroundColor White
Write-Host "   2. Créez des utilisateurs de test" -ForegroundColor White
Write-Host "   3. Assignez des rôles aux utilisateurs" -ForegroundColor White
Write-Host "   4. Récupérez les client secrets pour vos microservices" -ForegroundColor White
