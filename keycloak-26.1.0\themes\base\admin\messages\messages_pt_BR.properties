invalidPasswordMinLengthMessage=Senha inválida: deve conter ao menos {0} caracteres.
invalidPasswordMinLowerCaseCharsMessage=Senha inválida: deve conter ao menos {0} caracteres minúsculos.
invalidPasswordMinDigitsMessage=Senha inválida: deve conter ao menos {0} digitos numéricos.
invalidPasswordMinUpperCaseCharsMessage=Senha inválida: deve conter ao menos {0} caracteres maiúsculos.
invalidPasswordMinSpecialCharsMessage=Senha inválida: deve conter ao menos {0} caracteres especiais.
invalidPasswordNotUsernameMessage=Senha inválida: não deve ser igual ao nome de usuário.
invalidPasswordNotContainsUsernameMessage=Senha inválida\: não pode conter o nome de usuário.
invalidPasswordRegexPatternMessage=Senha inválida: falha ao passar por padrões.
invalidPasswordHistoryMessage=Senha inválida: não deve ser igual às últimas {0} senhas.

ldapErrorInvalidCustomFilter=Filtro LDAP não inicia com "(" ou não termina com ")".
ldapErrorMissingClientId=ID do cliente precisa ser definido na configuração quando mapeamentos de Roles do Realm não é utilizado.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Não é possível preservar herança de grupos e usar tipo de associação de UID ao mesmo tempo.
ldapErrorCantWriteOnlyForReadOnlyLdap=Não é possível definir modo de somente escrita quando o provedor LDAP não suporta escrita
ldapErrorCantWriteOnlyAndReadOnly=Não é possível definir somente escrita e somente leitura ao mesmo tempo

clientRedirectURIsFragmentError=URIs de redirecionamento não podem conter fragmentos
clientRootURLFragmentError=URL raiz não pode conter fragmentos