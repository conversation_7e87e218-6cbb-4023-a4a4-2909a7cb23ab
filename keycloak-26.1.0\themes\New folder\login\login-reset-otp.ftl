<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('totp'); section>
    <#if section="header">
        <!--${msg("doLogIn")}-->
    <#elseif section="form">
        <div id="kc-content">
            <div id="kc-form">
                <div id="kc-form-wrapper">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("doLogIn")}</h1>
                        <p class="form-subtitle">${msg("otp-reset-description")}</p>
                    </div>
                    
                    <form id="kc-otp-reset-form" action="${url.loginAction}" method="post">
                        <div class="form-group otp-credential-list">
                            <div class="otp-credential-title">Sélectionnez l'authentificateur à réinitialiser:</div>
                            <div class="otp-credential-options">
                                <#list configuredOtpCredentials.userOtpCredentials as otpCredential>
                                    <div class="otp-credential-option">
                                        <input id="kc-otp-credential-${otpCredential?index}" type="radio" name="selectedCredentialId" value="${otpCredential.id}" <#if otpCredential.id == configuredOtpCredentials.selectedCredentialId>checked="checked"</#if>>
                                        <label for="kc-otp-credential-${otpCredential?index}" tabindex="${otpCredential?index}">
                                            <span class="otp-credential-icon">
                                                <i class="fa fa-key" aria-hidden="true"></i>
                                            </span>
                                            <span class="otp-credential-label">${otpCredential.userLabel}</span>
                                        </label>
                                    </div>
                                </#list>
                            </div>
                        </div>

                        <div class="form-buttons">
                            <button id="kc-otp-reset-form-submit" class="login-button" type="submit">
                                <span class="button-text">${msg("doSubmit")}</span>
                                <span class="button-icon"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="login-illustration">
                <img src="${url.resourcesPath}/img/OTP11.jpg" alt="OTP Reset illustration">
            </div>
        </div>
    </#if>
</@layout.registrationLayout>
