package otbs.vehicule.controller;

import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import otbs.vehicule.dto.DocumentVehiculeDto;
import otbs.vehicule.model.VisiteTechnique;
import otbs.vehicule.service.DocumentVehiculeService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import otbs.vehicule.service.FileStorageService;


import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

@Setter
@RestController
@RequestMapping("/api/v1/visiteTechnique")
public class VisiteTechniqueController {
    @Value("${storage.path}")
    private String storagePath;
    private final FileStorageService fileStorageService;
    private final DocumentVehiculeService documentVehiculeService;
    private final NfsController nfsController;

    public VisiteTechniqueController(DocumentVehiculeService documentVehiculeService, FileStorageService fileStorageService,
                                     NfsController nfsController){
        this.documentVehiculeService=documentVehiculeService;
        this.fileStorageService=fileStorageService;
        this.nfsController=nfsController;
    }

    @PostMapping("/ajouterVisitetechnique/{vehiculeId}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX')")
    public ResponseEntity<DocumentVehiculeDto> addVisitetechnique(
            @PathVariable Long vehiculeId,
            @RequestPart("data") DocumentVehiculeDto documentVehiculeDto,
            @RequestPart(value = "file", required = false) MultipartFile file) {

        String extension = null;
        if (file != null && !file.isEmpty()) {
            extension = StringUtils.getFilenameExtension(file.getOriginalFilename());
            documentVehiculeDto.setFilePath(extension);
        }

        DocumentVehiculeDto savedVisiteTechnique = documentVehiculeService.addVisiteTechnique(documentVehiculeDto, vehiculeId, extension);

        if (file != null && !file.isEmpty()) {
            try {
                Path filePath = Paths.get(storagePath, "VisiteTechnique", savedVisiteTechnique.getId() + "." + extension);
                fileStorageService.save(filePath, file.getBytes());
            } catch (IOException e) {
                return new ResponseEntity<>(savedVisiteTechnique, HttpStatus.BAD_REQUEST);
            }
        }

        return new ResponseEntity<>(savedVisiteTechnique, HttpStatus.CREATED);
    }

    @PutMapping("/updateVisiteTechnique/{visiteTechniqueId}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX')")
    public ResponseEntity<DocumentVehiculeDto> updateVisiteTechnique(@PathVariable Long visiteTechniqueId, @RequestBody DocumentVehiculeDto documentVehiculeDto){
        DocumentVehiculeDto updatedVisiteTechnique = documentVehiculeService.updateDocumentVehicule(documentVehiculeDto, visiteTechniqueId);
        return new ResponseEntity<>(updatedVisiteTechnique, HttpStatus.OK);
    }
    @GetMapping("/listeVisiteTechnique/{idVehicule}")
    public ResponseEntity<List<DocumentVehiculeDto>> getListeVisiteTechniqe(@PathVariable Long idVehicule) {
        List<DocumentVehiculeDto> visiteTechniqueDtoList = documentVehiculeService.getListeDocumentVehicule(VisiteTechnique.class,idVehicule);
        return new ResponseEntity<>(visiteTechniqueDtoList, HttpStatus.OK);
    }
    @GetMapping("/getVisiteTechnique/{visiteId}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX') or hasRole('DIRECTEUR_GENERAL')")
    public ResponseEntity<DocumentVehiculeDto> getVisiteTechniqueById(@PathVariable Long visiteId) {
        DocumentVehiculeDto visiteTechniqueDto = documentVehiculeService.getDocumentVehiculeById(visiteId);
        return new ResponseEntity<>(visiteTechniqueDto, HttpStatus.OK);
    }
    @DeleteMapping("/deleteVisiteTechnique/{visiteId}")
    public ResponseEntity<String> deleteVisiteTechnique(@PathVariable Long visiteId) {
        return nfsController.deleteDocumentVehicule("VisiteTechnique", visiteId);
    }

    @PostMapping("/ajouterRéservation/{vehiculeId}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX')")
    public ResponseEntity<DocumentVehiculeDto> addReservation(@PathVariable Long vehiculeId, @RequestBody DocumentVehiculeDto documentVehiculeDto) {
        DocumentVehiculeDto reservation = documentVehiculeService.addReservation(vehiculeId, documentVehiculeDto);
        return new ResponseEntity<>(reservation, HttpStatus.OK);
    }

    @GetMapping("/getLastVisiteTechniqueByVehicule/{vehiculeId}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX') or hasRole('DIRECTEUR_GENERAL')")
    public ResponseEntity<DocumentVehiculeDto> getLastVisiteTechniqueByVehicule(@PathVariable Long vehiculeId) {
        DocumentVehiculeDto visiteTechniqueDto = documentVehiculeService.getLastDocumentByVehicule(vehiculeId, VisiteTechnique.class);
        return new ResponseEntity<>(visiteTechniqueDto, HttpStatus.OK);
    }

    @GetMapping("/getReservationByVehicule/{vehiculeId}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX') or hasRole('DIRECTEUR_GENERAL')")
    public ResponseEntity<DocumentVehiculeDto> getReservationByVehicule(@PathVariable Long vehiculeId) {
        DocumentVehiculeDto visiteTechniqueDto = documentVehiculeService.getReservationByVehicule(vehiculeId);
        return new ResponseEntity<>(visiteTechniqueDto, HttpStatus.OK);
    }

    @GetMapping("/getReservationInThreeDaysList")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX') or hasRole('DIRECTEUR_GENERAL')")
    public ResponseEntity<List<DocumentVehiculeDto>> getReservationInThreeDaysList() {
        List<DocumentVehiculeDto> visiteTechniqueDto = documentVehiculeService.getReservationInThreeDaysList();
        return new ResponseEntity<>(visiteTechniqueDto, HttpStatus.OK);
    }

    @GetMapping("/getReservationWaitingForResult")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX') or hasRole('DIRECTEUR_GENERAL')")
    public ResponseEntity<List<DocumentVehiculeDto>> getReservationWaitingForResult() {
        List<DocumentVehiculeDto> visiteTechniqueDto = documentVehiculeService.getReservationWaitingForResult();
        return new ResponseEntity<>(visiteTechniqueDto, HttpStatus.OK);
    }

    @GetMapping("/getReservationEnAttente")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX') or hasRole('DIRECTEUR_GENERAL')")
    public ResponseEntity<List<DocumentVehiculeDto>> getReservationEnAttente() {
        List<DocumentVehiculeDto> visiteTechniqueDto = documentVehiculeService.getReservationEnAttente();
        return new ResponseEntity<>(visiteTechniqueDto, HttpStatus.OK);
    }

    @PutMapping("/updateReservation/{vehiculeId}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX')")
    public ResponseEntity<DocumentVehiculeDto> updateReservation(@PathVariable Long vehiculeId, @RequestBody DocumentVehiculeDto documentVehiculeDto){
        DocumentVehiculeDto updatedReservation = documentVehiculeService.updateReservation(documentVehiculeDto, vehiculeId);
        return new ResponseEntity<>(updatedReservation, HttpStatus.OK);
    }

    @DeleteMapping("/deleteReservation/{vehiculeId}")
    public ResponseEntity<String> deleteReservation(@PathVariable Long vehiculeId) {
        documentVehiculeService.deleteReservation(vehiculeId);
        return ResponseEntity.ok("Visite technique supprimée avec succès");
    }

    @GetMapping("/getRefusedCars/")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX') or hasRole('DIRECTEUR_GENERAL')")
    public ResponseEntity<Map<String, Integer>> getRefusedCars() {
        Map<String, Integer> refusedCars = documentVehiculeService.getRefusedCars();
        return new ResponseEntity<>(refusedCars, HttpStatus.OK);
    }


}
