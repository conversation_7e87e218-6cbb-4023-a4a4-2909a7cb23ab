<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=true; section>
    <#if section = "header">
        <!--${msg("doLogIn")} -->
    <#elseif section = "form">
        <div id="kc-content">
            <div class="login-illustration">
                <img src="${url.resourcesPath}/img/OTP11.jpg" alt="Login illustration">
            </div>
          <div id="kc-form-wrapper">

            <div id="login-container">
                <div class="form-header">
                    <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                    <h1 class="form-title">${msg("doLogIn")}</h1>
                    <p class="form-subtitle">Authentification à deux facteurs</p>
                </div>

                <form id="kc-otp-login-form" action="${url.loginAction}" method="post">
                    <#if otpLogin.userOtpCredentials?size gt 1>
                        <div class="form-floating">
                            <select id="selected-credential" name="selectedCredentialId" class="form-select"
                                    aria-label="${msg("loginTotpDeviceName")}" required>
                                <option value="" disabled selected>Sélectionnez votre appareil</option>
                                <#list otpLogin.userOtpCredentials as otpCredential>
                                    <option value="${otpCredential.id}" <#if otpCredential.id == otpLogin.selectedCredentialId>selected</#if>>${otpCredential.userLabel}</option>
                                </#list>
                            </select>
                            <label for="selected-credential">${msg("loginTotpDeviceName")}</label>
                            <span class="form-icon device-icon"></span>
                        </div>
                    </#if>

                    <div class="form-floating otp-input-container">
                        <input type="text" id="otp" name="otp" autocomplete="off" class="form-control"
                               aria-invalid="<#if messagesPerField.existsError('totp')>true</#if>"
                               placeholder=" " autofocus />
                        <label for="otp">Code de vérification <span class="required">*</span></label>
                        <span class="form-icon otp-icon"></span>

                        <div class="otp-timer">
                            <svg class="timer-circle">
                                <circle cx="12" cy="12" r="10"></circle>
                                <circle class="progress" cx="12" cy="12" r="10"></circle>
                            </svg>
                            <span class="timer-text">30</span>
                        </div>

                        <#if messagesPerField.existsError('totp')>
                            <span id="input-error-otp-code" class="error-message" aria-live="polite">
                                ${kcSanitize(messagesPerField.get('totp'))?no_esc}
                            </span>
                        </#if>
                    </div>

                    <div class="form-buttons">
                        <button class="login-button" type="submit">
                            <span class="button-text">${msg("doSubmit")}</span>
                            <span class="button-icon"></span>
                        </button>
                        <#if isAppInitiatedAction??>
                            <button class="cancel-button" type="submit" name="cancel-aia" value="true">
                                ${msg("doCancel")}
                            </button>
                        </#if>
                    </div>

                    <div class="help-section">
                        <button type="button" class="help-button" onclick="toggleHelp()">
                            <span class="help-icon"></span>
                            <span>Besoin d'aide ?</span>
                        </button>
                        
                        <div id="helpContent" class="help-content" style="display: none;">
                            <div class="help-card">
                                <h3>Comment obtenir votre code de vérification</h3>
                                <ol>
                                    <li>Ouvrez votre application d'authentification sur votre téléphone</li>
                                    <li>Recherchez l'entrée pour <strong>ParcAuto</strong></li>
                                    <li>Saisissez le code à 6 chiffres affiché</li>
                                </ol>
                                <p class="help-text">Note : Le code change toutes les 30 secondes pour votre sécurité.</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

              </div>
        </div>
    </#if>
    
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion des options de credential
        const credentialOptions = document.querySelectorAll('.otp-credential-option');
        
        credentialOptions.forEach(option => {
            const radio = option.querySelector('input[type="radio"]');
            
            if (radio.checked) {
                option.classList.add('selected');
            }
            
            radio.addEventListener('change', function() {
                credentialOptions.forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                if (this.checked) {
                    option.classList.add('selected');
                }
            });
            
            option.addEventListener('click', function(e) {
                if (e.target !== radio) {
                    radio.checked = true;
                    const event = new Event('change');
                    radio.dispatchEvent(event);
                }
            });
        });

        // Timer functionality
        function startOTPTimer() {
            const timerText = document.querySelector('.timer-text');
            const progressCircle = document.querySelector('.timer-circle .progress');
            let timeLeft = 30;
            
            function updateTimer() {
                if (timeLeft >= 0) {
                    timerText.textContent = timeLeft;
                    const progress = (timeLeft / 30) * 63; // 63 is the circumference of the circle
                    progressCircle.style.strokeDashoffset = 63 - progress;
                    timeLeft--;
                    setTimeout(updateTimer, 1000);
                } else {
                    timeLeft = 30;
                    updateTimer();
                }
            }
            
            updateTimer();
        }

        // Start the timer when the page loads
        startOTPTimer();
    });

    function toggleHelp() {
        const helpContent = document.getElementById('helpContent');
        if (helpContent.style.display === 'none') {
            helpContent.style.display = 'block';
        } else {
            helpContent.style.display = 'none';
        }
    }
</script>
</@layout.registrationLayout>