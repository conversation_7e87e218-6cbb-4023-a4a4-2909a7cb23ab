package otbs.ms_astreint.client.reservation;

import feign.Logger;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

/**
 * Configuration pour le client Feign ReservationClient.
 * Cette classe configure les intercepteurs, le niveau de log et les retry policies.
 */
@Configuration
public class ReservationClientConfig {

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_TOKEN_TYPE = "Bearer";

    /**
     * Configure le niveau de journalisation pour le client Feign.
     *
     * @return Le niveau de journalisation pour le client Feign
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }

    /**
     * Configure l'intercepteur pour propager le token JWT aux services appelés via Feign.
     *
     * @return L'intercepteur Feign pour propager le token JWT
     */
    @Bean
    public RequestInterceptor feignRequestInterceptor() {
        return new JwtTokenInterceptor();
    }

    /**
     * Configure le Retryer pour le client Feign.
     * Cette configuration permet de réessayer les appels en cas d'échec.
     *
     * @return Le Retryer configuré
     */
    @Bean
    public Retryer retryer() {
        // Réessayer 2 fois, avec un délai initial de 1s et un multiplicateur de 1.5
        return new Retryer.Default(1000, 2000, 2);
    }

    /**
     * Intercepteur pour propager le token JWT dans les requêtes Feign.
     */
    public static class JwtTokenInterceptor implements RequestInterceptor {

        @Override
        public void apply(RequestTemplate template) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication instanceof JwtAuthenticationToken jwtAuthenticationToken) {
                String tokenValue = jwtAuthenticationToken.getToken().getTokenValue();
                template.header(AUTHORIZATION_HEADER, String.format("%s %s", BEARER_TOKEN_TYPE, tokenValue));
            }
        }
    }
}
