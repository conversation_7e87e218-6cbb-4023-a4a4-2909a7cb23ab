<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('password'); section>
    <#if section = "header">
        <!--${msg("doLogIn")}-->
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form">
                <div id="kc-form-wrapper">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("doLogIn")}</h1>
                        <p class="form-subtitle">${msg("passwordSubtitle")}</p>
                    </div>

                    <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}"
                          method="post">
                        <div class="form-floating">
                            <input tabindex="2" 
                                   id="password" 
                                   class="form-control ${messagesPerField.existsError('password')?then('is-invalid', '')}"
                                   name="password"
                                   type="password"
                                   autocomplete="current-password"
                                   placeholder=" "
                                   autofocus
                                   aria-invalid="<#if messagesPerField.existsError('password')>true</#if>"
                            />
                            <span class="form-icon password-icon" aria-hidden="true"></span>
                            <label for="password">${msg("password")}</label>
                            <button class="password-toggle" 
                                    type="button" 
                                    aria-label="${msg('showPassword')}"
                                    aria-controls="password"
                                    data-password-toggle>
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </button>

                            <#if messagesPerField.existsError('password')>
                                <span id="input-error-password" class="error-message" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('password'))?no_esc}
                                </span>
                            </#if>
                        </div>

                        <div class="form-group">
                            <input tabindex="4"
                                   class="btn btn-primary btn-block btn-lg"
                                   name="login"
                                   id="kc-login"
                                   type="submit"
                                   value="${msg("doLogIn")}"
                            />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </#if>
</@layout.registrationLayout>
