package otbs.ms_notification.client.keycloak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * DTO représentant un utilisateur Keycloak.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDTO {

    private String id;
    private String username;
    private String email;
    private String firstName;
    private String lastName;
    private boolean enabled;
    private boolean emailVerified;
    private LocalDateTime createdTimestamp;
    private Map<String, List<String>> attributes;
    private List<String> realmRoles;
    private List<String> clientRoles;

    /**
     * Retourne le nom complet de l'utilisateur.
     *
     * @return Le nom complet (prénom + nom) ou le nom d'utilisateur si les noms ne sont pas disponibles
     */
    public String getFullName() {
        if (firstName != null && lastName != null && !firstName.trim().isEmpty() && !lastName.trim().isEmpty()) {
            return firstName.trim() + " " + lastName.trim();
        } else if (firstName != null && !firstName.trim().isEmpty()) {
            return firstName.trim();
        } else if (lastName != null && !lastName.trim().isEmpty()) {
            return lastName.trim();
        }
        return username != null ? username : "Utilisateur inconnu";
    }

    /**
     * Vérifie si l'utilisateur a un rôle spécifique dans le realm.
     *
     * @param role Le nom du rôle à vérifier
     * @return true si l'utilisateur a le rôle, false sinon
     */
    public boolean hasRealmRole(String role) {
        return realmRoles != null && realmRoles.contains(role);
    }

    /**
     * Vérifie si l'utilisateur a un rôle spécifique pour un client.
     *
     * @param role Le nom du rôle à vérifier
     * @return true si l'utilisateur a le rôle client, false sinon
     */
    public boolean hasClientRole(String role) {
        return clientRoles != null && clientRoles.contains(role);
    }

    /**
     * Récupère une valeur d'attribut spécifique.
     *
     * @param attributeName Le nom de l'attribut
     * @return La première valeur de l'attribut ou null si l'attribut n'existe pas
     */
    public String getAttribute(String attributeName) {
        if (attributes != null && attributes.containsKey(attributeName)) {
            List<String> values = attributes.get(attributeName);
            return values != null && !values.isEmpty() ? values.get(0) : null;
        }
        return null;
    }

    /**
     * Récupère toutes les valeurs d'un attribut spécifique.
     *
     * @param attributeName Le nom de l'attribut
     * @return La liste des valeurs de l'attribut ou une liste vide si l'attribut n'existe pas
     */
    public List<String> getAttributeValues(String attributeName) {
        if (attributes != null && attributes.containsKey(attributeName)) {
            return attributes.get(attributeName);
        }
        return List.of();
    }

    /**
     * DTO simplifié pour retourner seulement l'ID et l'email
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserContactDTO {
        private String id;
        private String email;
    }
} 