/* Two-column layout */
.two-column-layout {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    justify-content: center;
}

.image-column {
    flex: 1 1 0%; /* Image column takes 40% of the width */
    max-width: 50%;
}

.image-column img {
    width: 100%;
    height: auto;
    border-radius: 8px; /* Optional: Add rounded corners */
}


.form-column {
    display: flex;
    justify-content: center; /* Centrer horizontalement */
    align-items: center; /* Centrer verticalement */
    height: 100%; /* Prendre toute la hauteur disponible */
}

#kc-form {
    width: 100%; /* Prendre toute la largeur disponible */
    max-width: 400px; /* Limiter la largeur pour une meilleure lisibilité */
    align-items: center; /* Centrer verticalement */

}

#kc-form-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}



/* Responsive adjustments */
@media (max-width: 768px) {
    .two-column-layout {
        flex-direction: column;
        justify-content: center; /* Center vertically */
        align-items: center; /* Center horizontally */
        height: 100vh; /* Full viewport height to center vertically */
    }

    .image-column {
        display: none; /* Hide the image on small screens */
    }

    .form-column {
        flex: 1 1 100%; /* Form column takes full width */
        max-width: 100%;
        display: flex;
        justify-content: center; /* Center horizontally */
        align-items: center; /* Center vertically */
    }

    .form-column form {
        width: 100%; /* Full width of the form column */
        max-width: 400px; /* Limit form width for better readability */
        padding: 20px; /* Add some padding for better spacing */
    }
}










.login-pf body {
    background: #F0F2F5 !important;  /* Définir l'arrière-plan sur blanc */
    height: 100%;
}


.card-pf {
    border: none !important;
    box-shadow: none !important;
    margin: 10 !important;
    padding: 90 !important;
    font-family: inherit !important;

    height: 100%;
    width: 80.66% !important; /* 2/3 de l'écran */

    position: absolute; /* Positionnement absolu */

    right: 10%; /* Ajouter un espace de 10px à droite */
    top: 50%; /* Centrer verticalement */
    
    transform: translateY(-50%); /* Ajuster pour centrer verticalement */
    background: #E5E8EC !important;

}



#title, #update-password-title  {
    color: #334C68;
    font-weight: bold;
    font-size: 24px;
    font-family: inter;
    margin-bottom: 20px;
}



#background {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    border-radius: 10px 0 0 10px;
    filter: blur(0.5px); /* Blur effect */
    z-index: 1; /* Place it below the second image */
}

#back {
    position: absolute;
    top: 10%; /* Adjust this to position it closer to the top */
    margin-bottom: 10%;
    left: 0%; /* Move to the left side */
    width: 50%;
    height: 80%;
    z-index: 2; /* Make sure it is above the blurred background */
}

#username{
    height: 45px;
    width: 100%; /* Make inputs take up 130% of the form's width */
    padding: 15px; /* Make inputs larger */
    font-size: 16px; /* Increase font size */
    margin-bottom: 20px; /* Add space between inputs */
    box-sizing: border-box; /* Ensures padding doesn't affect the width */
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
    margin-bottom: 10px;
}


#kc-form-buttons {
    width: 100%; /* Ensure button takes up full width */

}
#kc-otp-login-form{
    width: 100%;}

#kc-login {
    background-color: #334C68;
    width: 100%;
    font-size: 18px; /* Increase font size for the button */
    padding: 15px; /* Make button larger */
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
    }

#kc-login:disabled {
background-color: #ccc;
}

#kc-login:hover:enabled {
background-color: #2b3e50;
}
.MotOublié {
    margin-bottom: 0; /* Reset any margin to align properly */
    justify-content: space-between;
    align-self: center; /* Vertically center the "Mot de passe oublié" link */

}

.pass {
    display: flex;
    align-items: center;                /* Centrer verticalement */
    position: relative;
    width: 100%;
}

/* Style pour l'input du mot de passe */
#password, #password-new, #password-confirm {
    height: 45px;
    padding: 15px 15px 15px 15px;       /* Ajout de padding à gauche et à droite */
    font-size: 16px;
    width: 100%;
    padding-right: 40px;                 /* Espace à droite pour l'icône */
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
    margin-bottom: 10px;
    box-sizing: border-box;              /* Inclure padding dans la largeur totale */
}

/* Style pour l'icône */
.eye {
    top: 5px;
    position: absolute;
    right: 15px;                        /* Espacement à droite de l'input */
    font-size: 18px;
    color: gray;
    background-color: transparent;
    border: none;
    cursor: pointer;
    outline: none;
}

/* Ajouter un style pour le conteneur qui englobe l'input et l'icône */
.pass {
    position: relative;
    width: 100%;
}

.kc-form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.kc-form-options .checkbox {
    margin-right: 130px;
}




/**/
.subtitle {
    text-align: right;
    margin-top: 30px;
    color: #909090;
}


/*steps*/

/* password*/

ul#kc-totp-supported-apps {
    margin-bottom: 10px;
}
span#kc-totp-secret-key {
    margin: 0 ;
    padding: 0;
}
#kc-totp-secret-qr-code {
    max-width:150px;
    max-height:150px;
}

#kc-totp-secret-key {
    margin: 0 auto;
    padding: 40px; /* Add padding to give some space around the form */
    position: absolute;
    left: 52%; /* Align to the right side */
    top: 28%; /* Move form down slightly to provide space for the logo and title */
}

/* Conteneur pour centrer les boutons */
.button-container {
    display: flex;
    justify-content: center;
    gap: 10px; /* Espacement entre les boutons */
    margin-top: 15px; /* Marge pour l'espacement */
}

a#mode-manual{
    color: #EF9129;}

a#mode-barcode{color: #EF9129;}

#saveTOTPBtn {
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 14px;
    min-width: 120px; /* Largeur uniforme */
    text-align: center;
}
button#cancelTOTPBtn1.pf-c-button.pf-m-primary.btn-lg {
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 14px;
    min-width: 120px; /* Largeur uniforme */
    text-align: center;
    background-color : #EF9129;
}

#saveTOTPBtn {
    background-color: #030A23;
}

#cancelTOTPBtn {
    color: #EF9129;
    background-color : #EF9129;
}
span.required{
    color: #EF9129;}
/* Pour le message d'erreur */
#input-error-otp-label {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    text-align: center;
}

/* Style des labels */
.control-label {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.userLabel {
    font-size: 14px;
    color: #555;
}



    #title1{
        color: #334C68;
        font-weight: bold;
        position: absolute;
        right: 10%; /* Align title to the left */
        top: 16%; /* Position it below the logo with more space */
        font-size: 24px;
        font-family: inter;

    }
    label#kc-attempted-username{
        color: #334C68;
        font-weight: bold;
        right: 10%; /* Align title to the left */
        top: 16%; /* Position it below the logo with more space */
        font-size: 24px;
        font-family: inter;
    }
#kc-form-OTP {
    top: 19%;
    font-size: 1.1em;
    transform: scale(0.8); /* Réduit à 80% */
    transform-origin: top;
}

#kc-username {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
    padding: 15px;
    width: 100%;
}

#kc-attempted-username {
    color: #334C68;
    font-weight: 500;
    font-size: 14px;
}

#reset-login {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.kc-login-tooltip {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #334C68;
}

.kc-login-tooltip i {
    font-size: 16px;
}

.kc-tooltip-text {
    font-size: 14px;
}

i.pficon.pficon-arrow.fa{
    color: #EF9129;

}
/* Pour les caractéristiques du titre (à adapter avec le bon sélecteur) */

#kc-username .kc-login-tooltip {
    display: inline-flex;
    margin: 0 !important;
    order: 2; /* Icône après le texte */
}





#kc-otp-group {
    margin-bottom: 15px !important;
}



#kc-otp-input {
    width: 200px !important; /* Largeur fixe pour l'input */
}



#kc-login-submit {
    margin-left: auto !important; /* Alignement à droite */
}
#kc-otp-input {
    height: 45px !important;
    width: 100% !important;
    padding: 15px !important;
    font-size: 16px !important;
    border: 1px solid #ddd !important;
    border-radius: 5px !important;
    background: #f9f9f9 !important;
    margin-bottom: 10px !important;
    box-sizing: border-box !important;
}

#kc-login-submit {
    background-color: #334C68 !important;
    width: 100% !important;
    font-size: 18px !important;
    padding: 15px !important;
    color: #fff !important;
    border: none !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    font-weight: 600 !important;
    transition: background-color 0.3s ease !important;
}

#kc-login-submit:disabled {
    background-color: #ccc !important;
}

#kc-login-submit:hover:enabled {
    background-color: #2b3e50 !important;
}

/* Styles pour les alertes */
.pf-c-alert {
    width: 100%;
    max-width: 400px;
    padding: 15px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #fff;
    border: 1px solid;
    margin-left: auto; /* Aligner à droite */
    margin-right: 0;
    margin-bottom: 20px;
}

.alert-info.pf-c-alert {
    background-color: #E8F4FF;
    border-color: #004b88;
}

.alert-success.pf-c-alert {
    background-color: #E8F5E9;
    border-color: #4CAF50;
}

.alert-warning.pf-c-alert {
    background-color: #FFF3E0;
    border-color: #FF9800;
}

.alert-error.pf-c-alert {
    background-color: #FFEBEE;
    border-color: #F44336;
}

.pf-c-alert__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.pf-c-alert__icon .fa {
    font-size: 18px;
}

.alert-info .pf-c-alert__icon .fa {
    color: #2196F3;
}

.alert-success .pf-c-alert__icon .fa {
    color: #4CAF50;
}

.alert-warning .pf-c-alert__icon .fa {
    color: #FF9800;
}

.alert-error .pf-c-alert__icon .fa {
    color: #F44336;
}

.pf-c-alert__title {
    color: #334C68;
    font-size: 14px;
    font-weight: 500;
    flex-grow: 1;
}

/* Ajustement pour le conteneur des alertes */
#kc-content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-end; /* Aligner les éléments à droite */
    width: 100%;
    padding-right: 20px; /* Ajouter un peu d'espace à droite */
}

.form-column {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end; /* Aligner le contenu à droite */
}

/* Styles pour le sélecteur de credentials */
.credential-selector-label {
    margin-bottom: 10px;
}

.credential-selector-label label {
    font-weight: 600;
    color: #334C68;
    font-size: 14px;
}

.credential-selector-wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
}

.credential-selector {
    width: 100%;
    height: 45px;
    padding: 10px 15px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    color: #334C68;
}

.credential-selector:focus {
    border-color: #334C68;
    outline: none;
    box-shadow: 0 0 0 2px rgba(51, 76, 104, 0.2);
}

.credential-selector-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #334C68;
}

.hidden-credential-input {
    display: none;
}

/* Animation pour le sélecteur */
.credential-selector {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.credential-selector:hover {
    border-color: #bbb;
}

/* Style pour les options du sélecteur */
.credential-selector option {
    padding: 10px;
    background-color: #fff;
    color: #334C68;
}

/* Ajustement du groupe OTP */
#kc-otp-group {
    margin-bottom: 25px;
}

/* Styles pour les instructions OTP */
.otp-instructions {
    margin-bottom: 25px;
    padding: 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #334C68;
    border-radius: 4px;
}

.otp-instructions p {
    margin: 0;
    color: #334C68;
    font-size: 14px;
    line-height: 1.5;
}

/* Style pour le texte d'aide */
.otp-help-text {
    margin-top: 20px;
    text-align: center;
    padding: 10px;
}

.otp-help-text p {
    margin: 0;
    color: #666;
    font-size: 13px;
}

.otp-help-text a {
    color: #334C68;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.otp-help-text a:hover {
    color: #EF9129;
    text-decoration: underline;
}

/* Amélioration du champ de saisie OTP */
#kc-otp-input {
    text-align: center;
    letter-spacing: 2px;
    font-size: 18px;
    font-weight: 600;
}

#kc-otp-input::placeholder {
    letter-spacing: normal;
    font-size: 14px;
    font-weight: normal;
    color: #aaa;
}

/* Animation pour le bouton de vérification */
#kc-login-submit {
    transition: background-color 0.3s ease, transform 0.2s ease;
}

#kc-login-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#kc-login-submit:active {
    transform: translateY(0);
}

/* Styles pour les instructions de configuration TOTP */
.totp-setup-instructions {
    margin-bottom: 25px;
    padding: 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #334C68;
    border-radius: 4px;
}

.totp-setup-instructions p {
    margin: 0;
    color: #334C68;
    font-size: 14px;
    line-height: 1.5;
}

/* Amélioration de la liste des étapes */
#kc-totp-settings {
    padding-left: 20px;
    margin-bottom: 30px;
}

#kc-totp-settings li {
    margin-bottom: 20px;
    color: #334C68;
}

#kc-totp-settings p {
    margin: 5px 0;
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}

/* Style pour le QR code */
#kc-totp-secret-qr-code {
    display: block;
    margin: 20px auto;
    max-width: 200px;
    border: 1px solid #ddd;
    padding: 10px;
    background-color: white;
    border-radius: 5px;
}

/* Style pour les liens dans les instructions */
#kc-totp-settings a {
    color: #334C68;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

#kc-totp-settings a:hover {
    color: #EF9129;
    text-decoration: underline;
}

/* Style pour le code secret */
#kc-totp-settings span {
    display: inline-block;
    font-family: monospace;
    font-size: 16px;
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
    margin: 10px 0;
    word-break: break-all;
}

/* Style pour les textes d'aide des champs */
.field-help-text {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
    margin-bottom: 5px;
    font-style: italic;
}

/* Amélioration des placeholders */
input::placeholder {
    color: #aaa;
    font-style: italic;
}

/* Amélioration des champs requis */
.required {
    color: #EF9129;
    margin-left: 5px;
}

/* Amélioration des messages d'erreur */
.kc-feedback-text, 
.pf-c-alert__title,
.kc-input-error-message {
    font-size: 13px;
    font-weight: 500;
}

/* Amélioration du bouton Enregistrer */
#saveTOTPBtn {
    background-color: #334C68;
}

#saveTOTPBtn:hover {
    background-color: #2b3e50;
}

/* Amélioration du bouton Annuler */
#cancelTOTPBtn1 {
    background-color: #EF9129;
}

#cancelTOTPBtn1:hover {
    background-color: #e08420;
}


