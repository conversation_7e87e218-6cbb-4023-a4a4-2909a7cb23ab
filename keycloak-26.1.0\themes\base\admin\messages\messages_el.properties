error-invalid-date=Το πεδίο {0} δεν αποτελεί έγκυρη ημερομηνία.
error-invalid-length-too-long=Το πεδίο {0} μπορεί να περιέχει το πολύ {2} χαρακτήρες .
error-person-name-invalid-character=Το {0} περιέχει ένα μη έγκυρο χαρακτήρα.
error-username-invalid-character=Το {0} περιέχει ένα μη έγκυρο χαρακτήρα.
error-user-attribute-read-only=Το πεδίο {0} είναι για ανάγνωση μόνο.
error-user-attribute-required=Παρακαλώ ορίστε το πεδίο {0}.
error-invalid-uri-fragment=Μη έγκυρο κομμάτι URL.
error-invalid-uri-scheme=Μη έγκυρο σχήμα URL.
error-invalid-uri=Μη έγκυρο URL.
error-pattern-no-match=Μη έγκυρη τιμή.
error-number-out-of-range-too-big=Το πεδίο {0} μπορεί να έχει μέγιστη τιμή {2}.
error-number-out-of-range-too-small=Το πεδίο {0} μπορεί να έχει ελάχιστη τιμή {1}.
error-number-out-of-range=Το πεδίο {0} πρέπει να είναι ένας αριθμός μεταξύ {1} και {2}.
error-invalid-number=Μη έγκυρος αριθμός.
error-invalid-email=Μη έγκυρη διεύθυνση email.
error-invalid-length-too-short=Το πεδίο {0} πρέπει να περιέχει τουλάχιστον {1} χαρακτήρα.
error-invalid-length=Το πεδίο {0} μπορεί να περιέχει από {1} μέχρι {2} χαρακτήρες.
error-empty=Παρακαλώ ορίστε τιμή.
error-invalid-blank=Παρακαλώ ορίστε τιμή.
error-invalid-value=Μη έγκυρη τιμή.
duplicatedJwksSettings=Ο διακόπτης "Χρήση JWKS" και ο διακόπτης "Χρήση URL JWKS" δε μπορεί να είναι ενεργοί ταυτόχρονα.
pairwiseRedirectURIsMismatch=Τα URI αναδρομολόγησης του πελάτη δεν ταιριάζουν με αυτά από το URI Αναγνωριστικού Τομέα.
pairwiseFailedToGetRedirectURIs=Αποτυχία λήψης των URI αναδρομολόγησης από το URI Αναγνωριστικού Τομέα.
pairwiseMalformedSectorIdentifierURI=Κακοσχηματισμένο URI Αναγνωριστικό Τομέα.
pairwiseClientRedirectURIsMultipleHosts=Όταν δεν ορίζεται ένα URI Αναγνώρισης Τομέα, τα URI αναδρομολόγησης του πελάτη δε πρέπει να περιέχουν πολλαπλά στοιχεία διακομιστών.
pairwiseClientRedirectURIsMissingHost=Τα URI αναδρομολόγησης του πελάτη πρέπει να περιέχουν ένα έγκυρο στοιχείο διακομιστή.


pairwiseMalformedClientRedirectURI=Ο πελάτης περιείχε ένα μη έγκυρο URI αναδρομολόγησης.
backchannelLogoutUrlIsInvalid=Ένα URL ανατροφοδότησης εξόδου δεν είναι έγκυρο URL
clientRedirectURIsInvalid=Ένα URI αναδρομολόγησης δεν είναι έγκυρο URI
clientRootURLInvalid=Το ριζικό URL δεν είναι ένα έγκυρο URL
clientBaseURLInvalid=Το βασικό URL δεν είναι ένα έγκυρο URL
clientRedirectURIsIllegalSchemeError=Ένα URI αναδρομολόγησης έχει ένα μη επιτρεπόμενο σχήμα
backchannelLogoutUrlIllegalSchemeError=Το URL ανατροφοδότησης εξόδου δεν έχει ένα επιτρεπόμενο σχήμα
clientRootURLIllegalSchemeError=Το ριζικό URL δεν έχει ένα επιτρεπόμενο σχήμα
clientBaseURLIllegalSchemeError=Το βασικό URL δεν έχει ένα επιτρεπόμενο σχήμα
clientRootURLFragmentError=Το ριζικό URL δεν πρέπει να περιέχει ένα μέρος από URL
clientRedirectURIsFragmentError=Τα URI αναδρομολόγησης δεν πρέπει να περιέχουν άλλα μέρη URI
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Η Πολιτική Επικύρωσης Κωδικών πρόσβασης είναι εφαρμόσιμη μόνο με την ΕΓΓΡΑΨΙΜΗ λειτουργία
ldapErrorMissingGroupsPathGroup=Δεν υπάρχει η ομάδα διαδρομών Ομάδων - παρακαλώ δημιουργήστε πρώτα την ομάδα στην ορισμένη διαδρομή
ldapErrorCantEnableUnsyncedAndImportOff=Δεν είναι δυνατή η Εισαγωγή χρηστών όταν ο πάροχος LDAP είναι σε λειτουργία ΑΣΥΓΧΡΟΝΟΣ
ldapErrorCantEnableStartTlsAndConnectionPooling=Δεν είναι δυνατή η ταυτόχρονη ενεργοποίηση StartTLS και ομαδοποίησης συνδέσεων.
ldapErrorCantWriteOnlyAndReadOnly=Αδυναμία ταυτόχρονου ορισμού εγγραφής-μόνο και ανάγνωσης-μόνο
ldapErrorCantWriteOnlyForReadOnlyLdap=Αδυναμία ορισμού σε εγγραφή-μόνο, όταν ο πάροχος LDAP δεν είναι σε ΕΓΓΡΑΨΙΜΗ λειτουργία
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Δεν είναι δυνατή η ταυτόχρονη διατήρηση της κληρονομιάς ομάδας και χρήση του τύπου μελών UID.
ldapErrorMissingClientId=Το Client ID πρέπει να παρέχεται στις ρυθμίσεις όταν δε χρησιμοποιείται η Αντιστοίχηση Ρόλων Τομέα.
ldapErrorReadTimeoutNotNumber=Η Λήξη Χρόνου Ανάγνωσης πρέπει να είναι αριθμός
ldapErrorConnectionTimeoutNotNumber=Η Λήξη Χρόνου Σύνδεσης πρέπει να είναι αριθμός
ldapErrorInvalidCustomFilter=Το προσαρμοσμένο φίλτρο LDAP που ορίστηκε δεν ξεκινά με "(" ή δεν τελειώνει με ")".
ldapErrorEditModeMandatory=Η Λειτουργία Επεξεργασίας είναι υποχρεωτική
invalidPasswordGenericMessage=Μη έγκυρος κωδικός πρόσβασης: ο νέος κωδικός δε συμφωνεί με τις πολιτικές κωδικών.
invalidPasswordBlacklistedMessage=Μη έγκυρος κωδικός πρόσβασης: ο κωδικός είναι απαγορευμένος.
invalidPasswordHistoryMessage=Μη έγκυρος κωδικός πρόσβασης: δεν επιτρέπεται να είναι ο ίδιος με τους τελευταίους {0} κωδικούς.
invalidPasswordRegexPatternMessage=Μη έγκυρος κωδικός πρόσβασης: δεν ταιριάζει με τα μοτίβα regex.
invalidPasswordNotEmailMessage=Μη έγκυρος κωδικός πρόσβασης: πρέπει να μην είναι ίσο με το email.
invalidPasswordNotUsernameMessage=Μη έγκυρος κωδικός πρόσβασης: πρέπει να μην είναι ίσο με το όνομα χρήστη.
invalidPasswordMinSpecialCharsMessage=Μη έγκυρος κωδικός πρόσβασης: πρέπει να περιέχει τουλάχιστον {0} σύμβολα.
invalidPasswordMinUpperCaseCharsMessage=Μη έγκυρος κωδικός πρόσβασης: πρέπει να περιέχει τουλάχιστον {0} κεφαλαίους χαρακτήρες.
invalidPasswordMinDigitsMessage=Μη έγκυρος κωδικός πρόσβασης: πρέπει να περιέχει τουλάχιστον {0} ψηφία.
invalidPasswordMinLowerCaseCharsMessage=Μη έγκυρος κωδικός πρόσβασης: πρέπει να περιέχει τουλάχιστον {0} πεζούς χαρακτήρες.
invalidPasswordMaxLengthMessage=Μη έγκυρος κωδικός πρόσβασης: το ανώτατο όριο επιτρεπόμενων χαρακτήρων είναι {0}..
invalidPasswordMinLengthMessage=Μη έγκυρος κωδικός πρόσβασης: το κατώτατο όριο επιτρεπόμενων χαρακτήρων είναι {0}.
