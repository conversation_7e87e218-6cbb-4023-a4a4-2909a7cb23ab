package otbs.vehicule.scheduler;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import otbs.vehicule.config.RabbitMQPublisherConfig;
import otbs.vehicule.dto.DocumentVehiculeDto;
import otbs.vehicule.dto.notification.DocumentVehiculeEvent;
import otbs.vehicule.service.DocumentVehiculeService;
import otbs.vehicule.service.VehiculeService;

import java.time.format.DateTimeFormatter;
import java.util.List;

@Component
public class NotificationScheduler {

    private final DocumentVehiculeService documentVehiculeService;
    private final VehiculeService vehiculeService;
    private final RabbitTemplate rabbitTemplate;

    public NotificationScheduler(DocumentVehiculeService documentVehiculeService, RabbitTemplate rabbitTemplate,
                                 VehiculeService vehiculeService) {
        this.documentVehiculeService = documentVehiculeService;
        this.vehiculeService = vehiculeService;
        this.rabbitTemplate = rabbitTemplate;
    }

    @Scheduled(cron = "00 00 08 * * *") //tous les jours à 08:00:00
    public void checkDocumentVehiculesExpiring() throws InterruptedException {
        List<DocumentVehiculeDto> documentVehiculeDtoList = documentVehiculeService.getDocumentsExpiringInMonth();

        for (DocumentVehiculeDto documentVehicule : documentVehiculeDtoList) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String dateExpirationFormatted = documentVehicule.getDateExpiration().format(formatter);
            Long vehiculeId = vehiculeService.findByImmatriculation(documentVehicule.getImmatriculation()).getIdVehicule();
            DocumentVehiculeEvent event = new DocumentVehiculeEvent(
                    "EXPIRED",
                    documentVehicule.getId(),
                    vehiculeId,
                    documentVehicule.getType(),
                    "Rappel : Expiration d'un document véhicule",
                    documentVehicule.getType() + " à traiter avant le " + dateExpirationFormatted +
                            " pour le véhicule "
                            + documentVehicule.getImmatriculation()
            );

            if(documentVehicule.getType().equals("VisiteTechnique")){
                event.setMessage(
                        "Veuillez prendre un rendez-vous de Visite Technique avant le " + dateExpirationFormatted +
                                " pour le véhicule "
                                + documentVehicule.getImmatriculation()
                );
            }

            rabbitTemplate.convertAndSend(
                    RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                    RabbitMQPublisherConfig.DOCUMENT_EXPIRING_ROUTING_KEY,
                    event);
        }
        Thread.sleep(10000);
    }

    @Scheduled(cron = "00 10 08 * * *") //tous les jours à 08:10:00
    public void checkReservationsForVisiteTechnique() throws InterruptedException {
        List<DocumentVehiculeDto> documentVehiculeDtoList = documentVehiculeService.getReservationInThreeDaysList();

        for (DocumentVehiculeDto documentVehicule : documentVehiculeDtoList) {
            DateTimeFormatter formatterDay = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String dateExpirationFormatted = documentVehicule.getDateReservation().format(formatterDay);
            DateTimeFormatter formatterTime = DateTimeFormatter.ofPattern("hh-mm");
            String heureExpirationFormatted = documentVehicule.getDateReservation().format(formatterTime);
            Long vehiculeId = vehiculeService.findByImmatriculation(documentVehicule.getImmatriculation()).getIdVehicule();
            DocumentVehiculeEvent event = new DocumentVehiculeEvent(
                    "RESERVATION_VISITE_TECHNIQUE",
                    documentVehicule.getId(),
                    vehiculeId,
                    documentVehicule.getType(),
                    "Rappel : Réservation pour Visite Technique",
                    "Le véhicule immatriculé " +
                            documentVehicule.getImmatriculation() +
                            " a un rendez-vous pour la visite technique le " +
                            dateExpirationFormatted + " à " + heureExpirationFormatted + "."
            );

            rabbitTemplate.convertAndSend(
                    RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                    RabbitMQPublisherConfig.RESERVATION_VISITE_TECHNIQUE_ROUTING_KEY,
                    event);
            Thread.sleep(10000);
        }
    }

    @Scheduled(cron = "00 20 08 * * *") //tous les jours à 08:20:00
    public void checkReservationsWaitingForResult() throws InterruptedException {
        List<DocumentVehiculeDto> documentVehiculeDtoList = documentVehiculeService.getReservationWaitingForResult();

        for (DocumentVehiculeDto documentVehicule : documentVehiculeDtoList) {
            Long vehiculeId = vehiculeService.findByImmatriculation(documentVehicule.getImmatriculation()).getIdVehicule();
            DocumentVehiculeEvent event = new DocumentVehiculeEvent(
                    "RESULTAT_VISITE_TECHNIQUE",
                    documentVehicule.getId(),
                    vehiculeId,
                    documentVehicule.getType(),
                    "Rappel : En attente du résultat de la Visite Technique",
                    "Veuillez ajouter le résultat de la Visite Technique du véhicule immatriculé" +
                            documentVehicule.getImmatriculation() +"."
            );

            rabbitTemplate.convertAndSend(
                    RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                    RabbitMQPublisherConfig.RESULTAT_VISITE_TECHNIQUE_ROUTING_KEY,
                    event);
            Thread.sleep(10000);
        }
    }

    @Scheduled(cron = "00 30 08 * * *") //tous les jours à 08:00:00
    public void checkReservationsEnAttente() throws InterruptedException {
        List<DocumentVehiculeDto> documentVehiculeDtoList = documentVehiculeService.getReservationEnAttente();

        for (DocumentVehiculeDto documentVehicule : documentVehiculeDtoList) {
            DateTimeFormatter formatterDay = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String dateExpirationFormatted = documentVehicule.getDateReservation().plusDays(2L).format(formatterDay);
            DateTimeFormatter formatterTime = DateTimeFormatter.ofPattern("hh-mm");
            String heureExpirationFormatted = documentVehicule.getDateReservation().format(formatterTime);
            Long vehiculeId = vehiculeService.findByImmatriculation(documentVehicule.getImmatriculation()).getIdVehicule();
            DocumentVehiculeEvent event = new DocumentVehiculeEvent(
                    "EN_ATTENTE_VISITE_TECHNIQUE",
                    documentVehicule.getId(),
                    vehiculeId,
                    documentVehicule.getType(),
                    "Rappel : Refaire la Visite Technique dans les 48h",
                    "Veuillez refaire la visite technique et insérer le nouveau résultat " +
                            "pour le véhicule immatriculé " +
                            documentVehicule.getImmatriculation() + " " +
                            "avant le " +
                            dateExpirationFormatted + " à " + heureExpirationFormatted + "."
            );
            System.out.println(event);

            rabbitTemplate.convertAndSend(
                    RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                    RabbitMQPublisherConfig.EN_ATTENTE_VISITE_TECHNIQUE_ROUTING_KEY,
                    event);
            Thread.sleep(10000);
        }
    }

}

