// Variables
$primary-color: #3f51b5;
$primary-light: #e8eaf6;
$primary-dark: #303f9f;
$accent-color: #ff4081;
$success-color: #4caf50;
$warning-color: #ff9800;
$error-color: #f44336;
$text-primary: #212121;
$text-secondary: #757575;
$divider-color: #e0e0e0;
$background-color: #ffffff;
$background-alt: #f9fafb;
$shadow-color: rgba(0, 0, 0, 0.1);
$border-radius: 8px;
$transition-speed: 0.3s;

:host {
  display: block;
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
}

// Dialog Header
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--accent-color);
  color: white;
  position: relative;
  border-radius: $border-radius $border-radius 0 0;

  h2 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 500;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      margin-right: 12px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 4H4c-1.11 0-2 .9-2 2v12c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V6h16v12zM6 10h2v2H6zm0 4h8v2H6zm10 0h2v2h-2zm-6-4h8v2h-8z'/%3E%3C/svg%3E");
      background-size: contain;
    }
  }
}

.close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color $transition-speed;
  padding: 0;
  outline: none;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.4);
  }
}

.close-icon {
  width: 24px;
  height: 24px;
}

// Form Styles
.reservation-form {
  padding: 24px;
  background-color: $background-color;
  border-radius: 0 0 $border-radius $border-radius;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// Type Selection
.type-selection {
  display: flex;
  gap: 24px;
  margin-bottom: 8px;
  padding: 16px;
  background: $background-alt;
  border-radius: $border-radius;
  border: 1px solid $divider-color;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  padding: 12px 16px;
  border-radius: $border-radius;
  transition: all $transition-speed;
  border: 2px solid transparent;
  background: $background-color;
  flex: 1;

  &:hover {
    background: $primary-light;
    border-color: $primary-color;
  }

  &:has(.radio-input:checked) {
    background: $primary-light;
    border-color: $primary-color;
    box-shadow: 0 2px 8px $shadow-color;
  }
}

.radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.radio-checkmark {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid $text-secondary;
  border-radius: 50%;
  margin-right: 12px;
  transition: all $transition-speed;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: $primary-color;
    transform: translate(-50%, -50%) scale(0);
    transition: transform $transition-speed;
  }
}

.radio-input:checked + .radio-checkmark {
  border-color: $primary-color;

  &::after {
    transform: translate(-50%, -50%) scale(1);
  }
}

.radio-label {
  font-weight: 500;
  color: $text-primary;
  font-size: 1rem;
}

// Section Containers
.section-container {
  margin-bottom: 24px;
  padding: 20px;
  background: $background-alt;
  border-radius: $border-radius;
  border: 1px solid $divider-color;

  &.carte-section {
    border-left: 4px solid $primary-color;
  }

  &.frais-section {
    border-left: 4px solid $warning-color;
  }
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: $text-primary;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 8px;
    background-size: contain;
  }

  .carte-section & {
    &::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233f51b5'%3E%3Cpath d='M20 4H4c-1.11 0-2 .9-2 2v12c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V6h16v12zM6 10h2v2H6zm0 4h8v2H6zm10 0h2v2h-2zm-6-4h8v2h-8z'/%3E%3C/svg%3E");
    }
  }

  .frais-section & {
    &::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff9800'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z'/%3E%3C/svg%3E");
    }
  }
}

// Table Styles
.table-container {
  max-height: 300px;
  overflow-y: auto;
  border-radius: $border-radius;
  border: 1px solid $divider-color;
  background: $background-color;
}

.carte-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;

  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid $divider-color;
  }

  th {
    background: $background-alt;
    font-weight: 600;
    color: $text-primary;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  tr:last-child td {
    border-bottom: none;
  }
}

.carte-row {
  transition: background-color $transition-speed;
  cursor: pointer;

  &:hover {
    background: $primary-light;
  }

  &.selected {
    background: $primary-light;
    border-left: 4px solid $primary-color;
  }
}

.checkbox-column {
  width: 60px;
  text-align: center;
}

.custom-checkbox {
  position: relative;
  display: inline-block;
  cursor: pointer;

  input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }

  .checkmark {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid $text-secondary;
    border-radius: 4px;
    transition: all $transition-speed;

    &::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 6px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg) scale(0);
      transition: transform $transition-speed;
    }
  }

  input[type="checkbox"]:checked + .checkmark {
    background: $primary-color;
    border-color: $primary-color;

    &::after {
      transform: rotate(45deg) scale(1);
    }
  }
}

.estimation-help {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.75rem;
  background-color: #e3f2fd;
  border-radius: 4px;
  border-left: 4px solid #2196f3;

  i {
    color: #1976d2;
    font-size: 0.875rem;
  }

  span {
    color: #1565c0;
    font-size: 0.875rem;
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e0e0e0;
  margin: 0 -24px -24px -24px;

  .cancel-button {
    color: #666;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }

  .submit-button {
    background-color: #ff9800;
    color: white;

    &:disabled {
      background-color: #ccc;
      color: #999;
    }

    i {
      margin-right: 0.5rem;
    }

    .fa-spinner {
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Action Buttons
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid $divider-color;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: $border-radius;
  font-size: 0.95rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-speed;
  border: none;
  min-width: 120px;
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.btn-secondary {
    background: $background-color;
    color: $text-secondary;
    border: 2px solid $divider-color;

    &:hover:not(:disabled) {
      background: $background-alt;
      border-color: $text-secondary;
    }
  }

  &.btn-primary {
    background: $primary-color;
    color: white;
    box-shadow: 0 2px 8px rgba(63, 81, 181, 0.3);

    &:hover:not(:disabled) {
      background: $primary-dark;
      box-shadow: 0 4px 12px rgba(63, 81, 181, 0.4);
      transform: translateY(-1px);
    }
  }
}

.btn-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

.loader {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Input Styles
.input-group {
  position: relative;
  margin-bottom: 16px;

  input {
    width: 100%;
    padding: 16px 12px 8px 12px;
    border: 2px solid $divider-color;
    border-radius: $border-radius;
    font-size: 1rem;
    background: $background-color;
    transition: all $transition-speed;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: $primary-color;
      box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
    }

    &:focus + label,
    &:not(:placeholder-shown) + label {
      top: 8px;
      font-size: 0.75rem;
      color: $primary-color;
    }
  }

  label {
    position: absolute;
    top: 16px;
    left: 12px;
    font-size: 1rem;
    color: $text-secondary;
    pointer-events: none;
    transition: all $transition-speed;
    background: $background-color;
    padding: 0 4px;
  }
}

.error-message {
  color: $error-color;
  font-size: 0.875rem;
  margin-top: 8px;
  display: flex;
  align-items: center;

  &::before {
    content: '⚠';
    margin-right: 8px;
  }
}

// Responsive
@media (max-width: 768px) {
  .dialog-header {
    padding: 16px 20px;

    h2 {
      font-size: 1.2rem;
    }
  }

  .reservation-form {
    padding: 16px;
  }

  .type-selection {
    flex-direction: column;
    gap: 12px;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 12px;

    .btn {
      width: 100%;
    }
  }

  .table-container {
    font-size: 0.8rem;
  }

  .carte-table th,
  .carte-table td {
    padding: 8px 12px;
  }
}
