/* Modern Creative Theme for Keycloak */
:root {
    /* Theme 1 - Ocean Business */
    --theme1-primary: #55D5E0;
    --theme1-secondary: #335F8A;
    --theme1-dark: #2F4558;
    --theme1-accent1: #F6B12D;
    --theme1-accent2: #F26619;
    --theme1-background: linear-gradient(135deg, #55D5E0 0%, #335F8A 100%);
    --theme1-card-bg: rgba(255, 255, 255, 0.95);
    --theme1-text-primary: #2F4558;
    --theme1-text-secondary: #335F8A;

    /* Theme 2 - Fresh Pop */
    --theme2-primary: #5CAFE7;
    --theme2-secondary: #82CEF9;
    --theme2-light: #FBFBFB;
    --theme2-accent1: #FFE361;
    --theme2-accent2: #FF679D;
    --theme2-background: linear-gradient(135deg, #5CAFE7 0%, #82CEF9 100%);
    --theme2-card-bg: rgba(251, 251, 251, 0.95);
    --theme2-text-primary: #5CAFE7;
    --theme2-text-secondary: #82CEF9;

    /* Active Theme (Theme 1 by default) */
    --primary-color: var(--theme1-primary);
    --secondary-color: var(--theme1-secondary);
    --background-start: var(--theme1-primary);
    --background-end: var(--theme1-secondary);
    --accent-color1: var(--theme1-accent1);
    --accent-color2: var(--theme1-accent2);
    --text-primary: var(--theme1-text-primary);
    --text-secondary: var(--theme1-text-secondary);
    --card-background: var(--theme1-card-bg);
    --success-color: #10b981;
    --error-color: #ef4444;
    --header-height: 70px;
    --nav-background: rgba(255, 255, 255, 0.95);

    /* Nouvelles variables pour la mise en page à deux colonnes */
    --split-layout-gap: 2rem;
    --form-width: 45%;
    --image-width: 50%;
}

/* Base responsive settings */
html {
    font-size: 16px;
    min-height: 100%;
    height: 100%;
}

body {
    background: 
        linear-gradient(135deg, rgba(85, 213, 224, 0.1) 0%, rgba(51, 95, 138, 0.1) 100%),
        radial-gradient(circle at top right, rgba(85, 213, 224, 0.2) 0%, transparent 40%),
        radial-gradient(circle at bottom left, rgba(51, 95, 138, 0.2) 0%, transparent 40%),
        var(--theme1-background);
    min-height: 100%;
    height: 100%;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    position: relative;
}

.login-pf {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    height: auto;
    padding-bottom: 0;
    box-sizing: border-box;
    overflow-x: hidden;
    position: relative;
    width: 100%;
}

/* Enhanced Header Styles */
#kc-header {
    background: var(--nav-background);
    backdrop-filter: blur(10px);
    margin: 0;
    padding: 0 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Logo and Site Name Container */
.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-container {
    position: static;
    transform: none;
    display: flex;
    align-items: center;
}

.logo-container img {
    height: 40px;
    width: auto;
}

#kc-header-wrapper {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: left;
    text-shadow: none;
    margin: 0;
    padding: 0;
    letter-spacing: -0.5px;
}

/* User Info Container */
.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: rgba(79, 70, 229, 0.1);
    border-radius: 2rem;
    color: var(--primary-color);
    font-weight: 500;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

#kc-locale {
    position: static;
    margin: 0;
}

#kc-locale-dropdown {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
}

#kc-locale-dropdown:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: none;
}

/* Main Content Wrapper */
.card-pf {
    flex: 0 0 auto;
    width: calc(100% - 2rem);
    max-width: 460px;
    margin: calc(var(--header-height) + 2rem) auto 2rem;
    padding: 2rem;
    position: relative;
    background: var(--card-background);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    border: 1px solid rgba(85, 213, 224, 0.2);
    box-shadow: 
        0 25px 50px -12px rgba(47, 69, 88, 0.25),
        0 0 0 1px rgba(85, 213, 224, 0.1);
    z-index: 1;
}

/* Enhanced Form Inputs */
.form-control {
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 1rem;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 
        0 0 0 3px rgba(85, 213, 224, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.05);
    outline: none;
}

/* Enhanced Buttons */
.btn {
    padding: 1rem 1.75rem;
    border-radius: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 
        0 4px 6px -1px rgba(85, 213, 224, 0.2),
        0 2px 4px -1px rgba(85, 213, 224, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: 0.5s;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 6px 8px -1px rgba(85, 213, 224, 0.3),
        0 4px 6px -1px rgba(85, 213, 224, 0.2);
}

.btn-primary:hover::before {
    left: 100%;
}

/* Enhanced Form Groups */
.form-group {
    margin-bottom: 1.75rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* Enhanced Remember Me Checkbox */
.checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 1.5rem 0;
    padding: 0.5rem;
    border-radius: 0.75rem;
    transition: background-color 0.3s ease;
}

.checkbox:hover {
    background-color: rgba(85, 213, 224, 0.05);
}

.checkbox input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 0.375rem;
    border: 2px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;
    cursor: pointer;
}

.checkbox input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

/* Enhanced Alert Styles */
.alert {
    border-radius: 1rem;
    border: none;
    padding: 1.25rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.alert-success::before {
    background: var(--success-color);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.alert-error::before {
    background: var(--error-color);
}

/* Message Styles */
.message-text {
    display: block;
    margin: 1rem 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* Info Alert */
.alert-info {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

.alert-info::before {
    background: var(--primary-color);
}

.alert-warning {
    background: rgba(234, 179, 8, 0.1);
    color: #b45309;
}

.alert-warning::before {
    background: #b45309;
}

/* Reset Password and Info Links */
.form-group + .form-group {
    margin-top: 1.5rem;
}

#kc-form-options {
    margin: 1.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#kc-form-options .checkbox {
    margin: 0;
}

#kc-forgot-pw {
    font-size: 0.95rem;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

#kc-forgot-pw:hover {
    background: rgba(85, 213, 224, 0.1);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

/* Info Section */
#kc-info {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(226, 232, 240, 0.6);
}

#kc-info-wrapper {
    text-align: center;
}

.instruction {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

/* Reset Password Form */
.reset-password-form {
    margin-top: 1rem;
}

.reset-password-form .form-group {
    margin-bottom: 1.5rem;
}

/* Icons */
.icon {
    display: inline-block;
    width: 1.25rem;
    height: 1.25rem;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    vertical-align: middle;
}

.icon-login {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1'%3E%3C/path%3E%3C/svg%3E");
}

.icon-reset {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'%3E%3C/path%3E%3C/svg%3E");
}

.icon-success {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 13l4 4L19 7'%3E%3C/path%3E%3C/svg%3E");
}

.icon-warning {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'%3E%3C/path%3E%3C/svg%3E");
}

.icon-error {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'%3E%3C/path%3E%3C/svg%3E");
}

.icon-info {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'%3E%3C/path%3E%3C/svg%3E");
}

/* Links in Alerts */
.alert a {
    color: inherit;
    text-decoration: underline;
    text-underline-offset: 2px;
    transition: opacity 0.3s ease;
}

.alert a:hover {
    opacity: 0.8;
}

/* Additional Mobile Styles */
@media (max-width: 768px) {
    #kc-form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    #kc-form-options .checkbox {
        width: 100%;
    }
    
    #kc-forgot-pw {
        width: 100%;
        text-align: center;
        padding: 0.75rem;
    }
    
    #kc-info {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
    }
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 500;
    font-size: 0.95rem;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-color);
    background: transparent;
}

.nav-button:hover {
    background: rgba(85, 213, 224, 0.1);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-button i {
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-button.active {
    background: var(--primary-color);
    color: white;
}

.nav-button.active:hover {
    background: var(--secondary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-buttons {
        gap: 0.5rem;
    }

    .nav-button {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .nav-button i {
        width: 1rem;
        height: 1rem;
    }
}

/* Footer Styles */
.kc-footer {
    position: relative;
    bottom: auto;
    left: 0;
    right: 0;
    margin-top: 2rem;
    background: var(--nav-background);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
    z-index: 900;
}

.footer-wrapper {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 2rem 1rem;
    box-sizing: border-box;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section {
    padding: 0;
}

.footer-section h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    position: relative;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -0.5rem;
    width: 40px;
    height: 2px;
    background: var(--primary-color);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid rgba(85, 213, 224, 0.1);
}

.footer-bottom p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer-wrapper {
        padding: 2rem 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-section h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-section ul li a:hover {
        transform: none;
    }
}

/* Theme Switcher */
.theme-switcher {
    position: fixed;
    bottom: calc(400px + 1rem);
    right: 1rem;
    display: flex;
    gap: 1rem;
    z-index: 1000;
}

.theme-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid white;
    cursor: pointer;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.theme-button:hover {
    transform: scale(1.1);
}

.theme-button.theme1 {
    background: linear-gradient(135deg, #55D5E0, #335F8A);
}

.theme-button.theme2 {
    background: linear-gradient(135deg, #5CAFE7, #82CEF9);
}

/* Responsive Media Queries */
@media (max-width: 1024px) {
    html {
        font-size: 14px;
    }
    
    .login-pf {
        padding-bottom: 0;
    }
    
    .card-pf {
        width: 90%;
        max-width: 450px;
        margin: calc(var(--header-height) + 1rem) auto 2rem;
    }
}

@media (max-width: 768px) {
    html {
        font-size: 13px;
    }
    
    :root {
        --header-height: 60px;
    }
    
    #kc-header {
        padding: 0 0.5rem;
        flex-wrap: wrap;
    }
    
    .header-left {
        flex: 1;
        min-width: 200px;
    }
    
    .logo-container img {
        max-height: 40px;
        max-width: 100%;
    }
    
    #kc-header-wrapper {
        font-size: 1.2rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .header-right {
        justify-content: flex-end;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .card-pf {
        margin: calc(var(--header-height) + 1rem) auto 2rem;
    }
    
    .form-control {
        height: 40px;
        font-size: 0.9rem;
        padding: 0.5rem;
    }
    
    .btn {
        height: 40px;
        font-size: 0.9rem;
        padding: 0 1rem;
    }
    
    .login-pf {
        padding-bottom: 0;
    }
    
    .footer-wrapper {
        padding: 2rem 1rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 2rem;
    }
    
    .theme-switcher {
        bottom: 1rem;
        right: 1rem;
    }
    
    .nav-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
    }
    
    .nav-button {
        padding: 0.5rem;
        font-size: 0.8rem;
        min-width: 80px;
    }
    
    .nav-button i {
        margin-right: 0.25rem;
        font-size: 1rem;
    }
    
    #kc-form,
    #kc-form-login {
        width: 100%;
    }
}

@media (max-width: 480px) {
    html {
        font-size: 12px;
    }
    
    .card-pf {
        width: 95%;
        margin: calc(var(--header-height) + 0.5rem) auto 1rem;
        padding: 1rem;
    }
    
    .footer-wrapper {
        padding: 1.5rem 0.5rem;
    }
    
    .footer-content {
        gap: 1.5rem;
    }
    
    .nav-buttons {
        flex-direction: column;
        align-items: center;
        width: 100%;
    }
    
    .nav-button {
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
    
    .user-info {
        font-size: 0.8rem;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .user-avatar {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
        min-width: 30px;
    }
    
    #kc-locale-dropdown {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-group label {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
        display: block;
    }
    
    .alert {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    #kc-form-options {
        flex-direction: column;
        align-items: flex-start;
    }
    
    #kc-form-options .checkbox {
        margin-bottom: 0.5rem;
    }
    
    #kc-forgot-pw {
        margin-top: 0.5rem;
        align-self: flex-start;
    }
}

@media (max-width: 320px) {
    html {
        font-size: 11px;
    }
    
    .card-pf {
        padding: 0.75rem;
        width: 98%;
    }
    
    .header-right {
        flex-wrap: wrap;
        justify-content: center;
        padding-top: 0.25rem;
    }
    
    #kc-header-wrapper {
        font-size: 1rem;
        max-width: 180px;
    }
    
    .logo-container img {
        max-height: 30px;
    }
    
    .form-control {
        font-size: 0.85rem;
        height: 36px;
    }
    
    .btn {
        font-size: 0.85rem;
        height: 36px;
    }
    
    .form-group + .form-group {
        margin-top: 0.75rem;
    }
}

@media (min-width: 1440px) {
    html {
        font-size: 18px;
    }
    
    .card-pf {
        max-width: 550px;
    }
    
    .login-pf {
        padding-bottom: 350px;
    }
}

@media (max-height: 500px) and (orientation: landscape) {
    .login-pf {
        padding-bottom: 0;
    }
    
    #kc-header {
        position: absolute;
    }
    
    .card-pf {
        margin-top: calc(var(--header-height) + 0.5rem);
        margin-bottom: 2rem;
        padding: 1rem;
        max-height: none;
        overflow-y: visible;
    }
    
    .kc-footer {
        position: relative;
        margin-top: 2rem;
    }
    
    .form-group {
        margin-bottom: 0.5rem;
    }
    
    .btn {
        height: 36px;
    }
}

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card-pf {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .kc-footer {
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .logo-container img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

@supports not (display: flex) {
    .login-pf, .header-left, .header-right, .nav-buttons, .footer-content {
        display: block;
    }
    
    .card-pf {
        margin: 80px auto 20px;
    }
}

#kc-content {
    flex: 1;
    width: 100%;
    padding-bottom: 1rem;
}

.card-pf {
    margin-bottom: 2rem !important;
}

/* Styles généraux */
body {
    font-family: 'Roboto', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-primary);
}

/* Logo du site - à ajouter sur toutes les pages */
.site-logo {
    max-height: 50px;
    margin-right: 1rem;
    width: auto;
}

/* Conteneur pour la mise en page à deux colonnes */
.split-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
    width: 100%;
    margin: 0 auto;
    max-width: 1200px;
    min-height: 500px;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Colonne de formulaire */
.form-column {
    width: var(--form-width);
    padding: 2rem;
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    flex: 1 1 300px;
}

/* Colonne d'image */
.image-column {
    width: var(--image-width);
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    flex: 1 1 300px;
    min-height: 300px;
}

.image-column img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

/* Overlay pour le texte sur l'image */
.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    color: white;
    z-index: 1;
}

.image-overlay h2 {
    margin-top: 0;
    font-size: 1.8rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.image-overlay p {
    font-size: 1rem;
    line-height: 1.5;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Styles spécifiques pour les pages d'information et d'erreur */
.info-page, .error-page {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
}

.info-page .icon, .error-page .icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.error-page .icon {
    color: var(--error-color, #e74c3c);
}

/* Amélioration des formulaires */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--input-border-color, #ddd);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
    outline: none;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-color-dark, #0056b3);
    transform: translateY(-1px);
}

/* Styles pour les pages spécifiques */
/* Login page - formulaire à gauche, image à droite */
.login-page .split-container {
    flex-direction: row;
}

/* OTP page - image à gauche, formulaire à droite */
.otp-page .split-container {
    flex-direction: row-reverse;
}

/* Responsive design */
@media (max-width: 992px) {
    .split-container {
        flex-direction: column !important;
        max-width: 100%;
        padding: 1rem;
    }
    
    .form-column, .image-column {
        width: 100%;
        margin-bottom: 1.5rem;
        flex-basis: 100%;
    }
    
    .image-column {
        min-height: 250px;
        order: -1;
    }
    
    .image-overlay h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    :root {
        --header-height: 60px;
    }
    
    .image-column {
        min-height: 200px;
    }
    
    .image-overlay {
        padding: 1.5rem;
    }
    
    .image-overlay h2 {
        font-size: 1.3rem;
    }
    
    .image-overlay p {
        font-size: 0.9rem;
    }
    
    .form-column {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .image-column {
        min-height: 180px;
    }
    
    .image-overlay {
        padding: 1rem;
    }
    
    .image-overlay h2 {
        font-size: 1.1rem;
    }
    
    .image-overlay p {
        font-size: 0.8rem;
        line-height: 1.3;
    }
    
    .form-column {
        padding: 1rem;
    }
    
    .site-logo {
        max-height: 40px;
    }
}

/* Ajustements pour les écrans à haute densité de pixels */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .image-column img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Support pour les navigateurs plus anciens */
@supports not (display: flex) {
    .split-container {
        display: block;
    }
    
    .form-column, .image-column {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .image-column {
        height: 300px;
    }
}

/* Ajustements pour l'orientation paysage sur mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .split-container {
        flex-direction: row !important;
    }
    
    .form-column, .image-column {
        flex-basis: 48%;
    }
    
    .image-column {
        min-height: 100%;
        order: 0;
    }
}

/* Nouveaux styles pour le design amélioré */

/* Structure de la page */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header amélioré */
.page-header {
    padding: 1.5rem 2rem;
    background-color: var(--header-background, rgba(255, 255, 255, 0.9));
    border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.header-content {
    display: flex;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-wrapper {
    margin-right: 1.5rem;
}

.site-logo {
    max-height: 60px;
    width: auto;
}

.header-title {
    flex: 1;
}

.header-title h1 {
    margin: 0;
    font-size: 1.8rem;
    color: var(--text-primary);
}

.header-subtitle {
    margin: 0.25rem 0 0;
    font-size: 1rem;
    color: var(--text-secondary);
}

/* Structure à deux colonnes améliorée */
.split-container {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
    background-color: transparent;
    border-radius: 12px;
    overflow: hidden;
}

.form-column {
    flex: 1;
    min-width: 0; /* Pour éviter le débordement */
}

.unified-card {
    background-color: var(--card-background, #fff);
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    padding: 0;
}

.image-column {
    flex: 1;
    position: relative;
    min-height: 600px;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.login-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Card Header */
.card-header {
    display: flex;
    align-items: center;
    padding: 2rem 2rem 1.5rem;
    border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.05));
}

.card-icon {
    width: 48px;
    height: 48px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.card-icon i {
    color: white;
    font-size: 1.5rem;
}

.card-title {
    flex: 1;
}

.card-title h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.card-subtitle {
    margin: 0.25rem 0 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Formulaire intégré */
#kc-form {
    padding: 2rem;
}

.form-group-enhanced {
    margin-bottom: 1.5rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    z-index: 1;
}

.input-wrapper input {
    padding-left: 2.5rem !important;
    height: 50px;
    border-radius: 8px;
    border: 1px solid var(--input-border-color, #ddd);
    transition: all 0.3s ease;
}

.input-wrapper input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
}

.password-input-group {
    display: flex;
    align-items: center;
    width: 100%;
}

.password-input-group input {
    flex: 1;
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.password-input-group button {
    height: 50px;
    border: 1px solid var(--input-border-color, #ddd);
    border-left: none;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    background-color: white;
    padding: 0 1rem;
    cursor: pointer;
}

/* Options de formulaire améliorées */
.form-options-enhanced {
    margin: 1.5rem 0;
}

.options-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remember-me-option {
    display: flex;
    align-items: center;
}

.forgot-password-option a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.forgot-password-option a:hover {
    color: var(--primary-color-dark);
    text-decoration: underline;
}

/* Bouton de connexion amélioré */
.form-buttons-enhanced {
    margin-top: 2rem;
}

.login-button {
    height: 50px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    background-color: var(--primary-color);
    border: none;
    color: white;
}

.login-button:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

/* Section d'inscription améliorée */
.registration-container {
    margin-top: 2rem;
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px dashed var(--border-color, rgba(0, 0, 0, 0.1));
}

.registration-prompt {
    font-size: 0.95rem;
    color: var(--text-secondary);
}

.register-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: color 0.2s;
}

.register-link:hover {
    color: var(--primary-color-dark);
    text-decoration: underline;
}

/* Section sociale intégrée */
.social-section {
    padding: 2rem;
    background-color: var(--card-footer-bg, #f9f9f9);
    border-top: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

.social-divider {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.divider-line {
    flex: 1;
    height: 1px;
    background-color: var(--border-color, rgba(0, 0, 0, 0.1));
}

.divider-text {
    padding: 0 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.social-providers-list {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 0;
    list-style: none;
    margin: 0;
}

.social-provider-item {
    flex: 1;
    max-width: 180px;
}

.social-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background-color: white;
    border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s;
}

.social-button:hover {
    background-color: var(--hover-bg, #f5f5f5);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.provider-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

/* Overlay amélioré */
.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.overlay-content {
    max-width: 80%;
    text-align: center;
}

.overlay-title {
    color: white;
    font-size: 2.2rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.overlay-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.overlay-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
}

.feature-item i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Responsive design amélioré */
@media (max-width: 1200px) {
    .overlay-title {
        font-size: 1.8rem;
    }
    
    .overlay-description {
        font-size: 1rem;
    }
    
    .feature-item i {
        font-size: 1.5rem;
    }
}

@media (max-width: 992px) {
    .split-container {
        flex-direction: column;
    }
    
    .image-column {
        min-height: 400px;
        order: -1;
    }
    
    .overlay-features {
        gap: 1rem;
    }
    
    .unified-card {
        margin-top: 1.5rem;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 1rem;
    }
    
    .page-header {
        padding: 1rem;
    }
    
    .header-title h1 {
        font-size: 1.5rem;
    }
    
    .card-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    #kc-form, .social-section {
        padding: 1.5rem;
    }
    
    .image-column {
        min-height: 300px;
    }
    
    .overlay-title {
        font-size: 1.5rem;
    }
    
    .overlay-description {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
    
    .overlay-features {
        flex-wrap: wrap;
        gap: 1rem 2rem;
    }
    
    .feature-item {
        width: calc(50% - 1rem);
    }
    
    .social-providers-list {
        flex-wrap: wrap;
    }
    
    .social-provider-item {
        flex: 0 0 calc(50% - 0.5rem);
        max-width: none;
    }
}

@media (max-width: 576px) {
    .header-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .logo-wrapper {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .card-header {
        flex-direction: column;
        text-align: center;
    }
    
    .card-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .options-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .social-providers-list {
        flex-direction: column;
    }
    
    .social-provider-item {
        max-width: none;
        width: 100%;
    }
    
    .image-column {
        min-height: 250px;
    }
    
    .overlay-features {
        flex-direction: column;
        gap: 1rem;
    }
    
    .feature-item {
        width: 100%;
    }
    
    #kc-form, .social-section {
        padding: 1.25rem;
    }
}

/* Ajout d'icônes pour les fonctionnalités */
.icon-user:before {
    content: "\f007"; /* Utiliser la police d'icônes appropriée */
}

.icon-lock:before {
    content: "\f023";
}

.icon-secure:before {
    content: "\f13e";
}

.icon-fast:before {
    content: "\f135";
}

.icon-support:before {
    content: "\f095";
}

.icon-login:before {
    content: "\f090";
} 