package otbs.ms_notification.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import otbs.ms_notification.client.keycloak.UserDTO;
import otbs.ms_notification.client.keycloak.UserSelectorOptionDTO;
import otbs.ms_notification.client.keycloak.KeycloakUserService;

import java.util.List;
import java.util.Optional;


@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User Management", description = "API pour la gestion des utilisateurs via Keycloak")
public class UserController {

    private final KeycloakUserService userService;

    @Operation(summary = "Récupérer un utilisateur par ID", 
               description = "Récupère les informations complètes d'un utilisateur depuis Keycloak")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Utilisateur trouvé",
                     content = @Content(schema = @Schema(implementation = UserDTO.class))),
        @ApiResponse(responseCode = "404", description = "Utilisateur non trouvé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{userId}")
    public ResponseEntity<UserDTO> getUserById(
            @Parameter(description = "ID de l'utilisateur", required = true)
            @PathVariable String userId) {
        
        log.info("Récupération de l'utilisateur avec l'ID: {}", userId);
        
        Optional<UserDTO> user = userService.getUserInfo(userId);
        
        if (user.isPresent()) {
            log.info("Utilisateur trouvé: {}", user.get().getUsername());
            return ResponseEntity.ok(user.get());
        } else {
            log.warn("Utilisateur non trouvé avec l'ID: {}", userId);
            return ResponseEntity.notFound().build();
        }
    }

    @Operation(summary = "Récupérer l'email d'un utilisateur", 
               description = "Récupère l'email d'un utilisateur par son ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Email récupéré avec succès"),
        @ApiResponse(responseCode = "404", description = "Utilisateur non trouvé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{userId}/email")
    public ResponseEntity<String> getUserEmail(
            @Parameter(description = "ID de l'utilisateur", required = true)
            @PathVariable String userId) {
        
        log.info("Récupération de l'email pour l'utilisateur: {}", userId);
        
        if (!userService.userExists(userId)) {
            log.warn("Utilisateur non trouvé avec l'ID: {}", userId);
            return ResponseEntity.notFound().build();
        }
        
        String email = userService.getEmailFromUserId(userId);
        log.info("Email récupéré pour l'utilisateur {}: {}", userId, email);
        
        return ResponseEntity.ok(email);
    }

    @Operation(summary = "Rechercher des utilisateurs par email", 
               description = "Recherche des utilisateurs par leur adresse email")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Recherche effectuée avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètre email invalide"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/search/email")
    public ResponseEntity<List<UserDTO>> searchUsersByEmail(
            @Parameter(description = "Adresse email à rechercher", required = true)
            @RequestParam String email) {
        
        log.info("Recherche d'utilisateurs par email: {}", email);
        
        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        List<UserDTO> users = userService.searchUsersByEmail(email.trim());
        log.info("Trouvé {} utilisateur(s) avec l'email: {}", users.size(), email);
        
        return ResponseEntity.ok(users);
    }

    @Operation(summary = "Rechercher des utilisateurs par nom d'utilisateur", 
               description = "Recherche des utilisateurs par leur nom d'utilisateur")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Recherche effectuée avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètre username invalide"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/search/username")
    public ResponseEntity<List<UserDTO>> searchUsersByUsername(
            @Parameter(description = "Nom d'utilisateur à rechercher", required = true)
            @RequestParam String username) {
        
        log.info("Recherche d'utilisateurs par nom d'utilisateur: {}", username);
        
        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        List<UserDTO> users = userService.searchUsersByUsername(username.trim());
        log.info("Trouvé {} utilisateur(s) avec le nom d'utilisateur: {}", users.size(), username);
        
        return ResponseEntity.ok(users);
    }

    @Operation(summary = "Récupérer tous les utilisateurs actifs", 
               description = "Récupère la liste de tous les utilisateurs actifs")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Liste récupérée avec succès"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/active")
    public ResponseEntity<List<UserDTO>> getAllActiveUsers() {
        
        log.info("Récupération de tous les utilisateurs actifs");
        
        List<UserDTO> users = userService.getAllActiveUsers();
        log.info("Trouvé {} utilisateur(s) actif(s)", users.size());
        
        return ResponseEntity.ok(users);
    }

    @Operation(summary = "Vérifier l'existence d'un utilisateur", 
               description = "Vérifie si un utilisateur existe dans Keycloak")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Vérification effectuée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{userId}/exists")
    public ResponseEntity<Boolean> checkUserExists(
            @Parameter(description = "ID de l'utilisateur", required = true)
            @PathVariable String userId) {
        
        log.info("Vérification de l'existence de l'utilisateur: {}", userId);
        
        boolean exists = userService.userExists(userId);
        log.info("Utilisateur {} existe: {}", userId, exists);
        
        return ResponseEntity.ok(exists);
    }

    @Operation(summary = "Vérifier si un utilisateur est actif", 
               description = "Vérifie si un utilisateur est actif dans Keycloak")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Vérification effectuée"),
        @ApiResponse(responseCode = "404", description = "Utilisateur non trouvé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{userId}/active")
    public ResponseEntity<Boolean> checkUserIsActive(
            @Parameter(description = "ID de l'utilisateur", required = true)
            @PathVariable String userId) {
        
        log.info("Vérification du statut actif pour l'utilisateur: {}", userId);
        
        if (!userService.userExists(userId)) {
            log.warn("Utilisateur non trouvé avec l'ID: {}", userId);
            return ResponseEntity.notFound().build();
        }
        
        boolean isActive = userService.isUserActive(userId);
        log.info("Utilisateur {} est actif: {}", userId, isActive);
        
        return ResponseEntity.ok(isActive);
    }

    @Operation(summary = "Récupérer tous les utilisateurs",
               description = "Récupère la liste de tous les utilisateurs (actifs et inactifs)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Liste récupérée avec succès"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping
    public ResponseEntity<List<UserDTO>> getAllUsers() {
        
        log.info("Récupération de tous les utilisateurs");
        
        List<UserDTO> users = userService.getAllUsers();
        log.info("Trouvé {} utilisateur(s) au total", users.size());
        
        return ResponseEntity.ok(users);
    }

    @Operation(summary = "Récupérer les utilisateurs pour sélecteur",
               description = "Récupère les utilisateurs sous forme simplifiée pour les listes déroulantes")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Liste récupérée avec succès"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/selector")
    public ResponseEntity<List<UserSelectorOptionDTO>> getUsersForSelector() {
        
        log.info("Récupération des utilisateurs pour sélecteur");
        
        List<UserSelectorOptionDTO> users = userService.getUsersForSelector();
        log.info("Trouvé {} utilisateur(s) pour le sélecteur", users.size());
        
        return ResponseEntity.ok(users);
    }

    @Operation(summary = "Rechercher des utilisateurs par nom",
               description = "Recherche des utilisateurs par prénom, nom ou nom d'utilisateur")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Recherche effectuée avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètre name invalide"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/search/name")
    public ResponseEntity<List<UserDTO>> searchUsersByName(
            @Parameter(description = "Nom à rechercher", required = true)
            @RequestParam String name) {
        
        log.info("Recherche d'utilisateurs par nom: {}", name);
        
        if (name == null || name.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        List<UserDTO> users = userService.searchUsersByName(name.trim());
        log.info("Trouvé {} utilisateur(s) avec le nom: {}", users.size(), name);
        
        return ResponseEntity.ok(users);
    }


    @GetMapping("/contacts-by-role/{role}")
    public ResponseEntity<List<UserDTO.UserContactDTO>> getUserContactsByRole(
            @PathVariable String role,
            @RequestParam(defaultValue = "true") boolean activeOnly) {
        return ResponseEntity.ok(userService.getUserContactsByRole(role, activeOnly));
    }


    @GetMapping("/contacts/{role}")
    public List<UserDTO.UserContactDTO> getActiveUserContactsByRole(@PathVariable String role) {
        return userService.getUserContactsByRole(role);
    }
} 