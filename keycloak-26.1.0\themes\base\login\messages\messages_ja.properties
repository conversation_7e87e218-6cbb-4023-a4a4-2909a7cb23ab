doLogIn=ログイン
doRegister=登録
doRegisterSecurityKey=登録
doCancel=キャンセル
doSubmit=送信
doBack=戻る
doYes=はい
doNo=いいえ
doContinue=続ける
doIgnore=無視
doAccept=承諾
doDecline=却下
doForgotPassword=パスワードをお忘れですか?
doClickHere=クリックしてください
doImpersonate=代理ログイン
doTryAgain=再試行してください
doTryAnotherWay=別の方法を試してください
kerberosNotConfigured=Kerberosは設定されていません
kerberosNotConfiguredTitle=Kerberosは設定されていません
bypassKerberosDetail=Kerberosでログインしていないか、ブラウザーでKerberosログインの設定がされていません。他の手段でログインするには「続ける」をクリックしてください。
kerberosNotSetUp=Kerberosが設定されていません。ログインできません。
registerTitle=登録
loginAccountTitle=アカウントにログイン
loginTitle={0}にログイン
loginTitleHtml={0}
impersonateTitle={0}ユーザーの代理
impersonateTitleHtml=<strong>{0}</strong>ユーザーの代理
realmChoice=レルム
unknownUser=不明なユーザー
loginTotpTitle=モバイル・オーセンティケーターのセットアップ
loginProfileTitle=アカウント情報の更新
loginTimeout=ログイン試行がタイムアウトしました。ログインは最初から開始されます。
oauthGrantTitle={0}へのアクセスを許可
oauthGrantTitleHtml={0}
errorTitle=申し訳ございません
errorTitleHtml=<strong>申し訳ございません</strong>
emailVerifyTitle=Eメール確認
emailForgotTitle=パスワードをお忘れですか?
updatePasswordTitle=パスワードの更新
codeSuccessTitle=成功コード
codeErrorTitle=エラーコード\: {0}
displayUnsupported=要求された表示タイプがサポートされていません
browserRequired=ログインに必要なブラウザー
browserContinue=ログインを完了するために必要なブラウザー
browserContinuePrompt=ブラウザーを開いてログインを続行しますか？ [y/n]:
browserContinueAnswer=y


termsTitle=利用規約
termsText=<p>利用規約はここで設定する必要があります</p>
termsPlainText=定義される利用規約。
recaptchaFailed=無効なreCAPTCHA
recaptchaNotConfigured=reCAPTCHAが必須ですが、設定されていません
consentDenied=同意が拒否されました。
noAccount=新規ユーザーですか?
username=ユーザー名
usernameOrEmail=ユーザー名またはメールアドレス
firstName=名
givenName=名
fullName=氏名
lastName=姓
familyName=姓
email=Eメール
password=パスワード
passwordConfirm=パスワード（確認）
passwordNew=新しいパスワード
passwordNewConfirm=新しいパスワード（確認）
rememberMe=ログイン状態の保存
authenticatorCode=ワンタイムコード
address=住所
street=番地
locality=市区町村
region=都道府県
postal_code=郵便番号
country=国
emailVerified=確認済みEメール
gssDelegationCredential=GSS委譲クレデンシャル
profileScopeConsentText=ユーザー・プロファイル
emailScopeConsentText=メールアドレス
addressScopeConsentText=アドレス
phoneScopeConsentText=電話番号
offlineAccessScopeConsentText=オフライン・アクセス
samlRoleListScopeConsentText=ロール
rolesScopeConsentText=ユーザーロール
restartLoginTooltip=ログインを再開
loginTotpIntro=このアカウントにアクセスするには、ワンタイム・パスワード・ジェネレーターを設定する必要があります
loginTotpStep1=次のいずれかのアプリケーションをモバイルにインストールします。
loginTotpStep2=アプリケーションを開き、バーコードをスキャンします。
loginTotpStep3=アプリケーションから提供されたワンタイムコードを入力し、送信をクリックしてセットアップを終了します。
loginTotpStep3DeviceName=OTPデバイスの管理に役立つデバイス名を指定します。
loginTotpManualStep2=アプリケーションを開き、キーを入力します：
loginTotpManualStep3=アプリケーションで設定できる場合は、次の設定値を使用します。
loginTotpUnableToScan=スキャンできませんか？
loginTotpScanBarcode=バーコードをスキャンしますか？
loginCredential=クレデンシャル
loginOtpOneTime=ワンタイムコード
loginTotpType=タイプ
loginTotpAlgorithm=アルゴリズム
loginTotpDigits=桁
loginTotpInterval=間隔
loginTotpCounter=カウンター
loginTotpDeviceName=デバイス名
loginTotp.totp=時間ベース
loginTotp.hotp=カウンターベース
loginChooseAuthenticator=ログイン方法を選択してください
oauthGrantRequest=これらのアクセス権限を付与しますか？
inResource=in
emailVerifyInstruction1=メールアドレスを確認する手順を記載したEメールを次のアドレス {0} に送信しました。
emailVerifyInstruction2=Eメールで確認コードを受け取っていませんか?
emailVerifyInstruction3=Eメールを再送信します。
emailLinkIdpTitle=リンク {0}
emailLinkIdp1={0}の{1}アカウントをあなたの{2}アカウントとリンクするための手順を記載したEメールを送信しました。
emailLinkIdp2=Eメールで確認コードを受け取っていませんか?
emailLinkIdp3=Eメールを再送信します。
emailLinkIdp4=別のブラウザーでメールを確認済みの場合
emailLinkIdp5=続けるには
backToLogin=&laquo; ログインに戻る
emailInstruction=ユーザー名またメールアドレスを入力してください。新しいパスワードの設定方法をご案内いたします。
copyCodeInstruction=このコードをコピーし、あなたのアプリケーションにペーストしてください：
pageExpiredTitle=ページの有効期限が切れています
pageExpiredMsg1=ログインプロセスを再開するには
pageExpiredMsg2=ログイン処理を続行するには
personalInfo=個人情報:
role_admin=管理者
role_realm-admin=レルム管理者
role_create-realm=レルムの作成
role_create-client=クライアントの作成
role_view-realm=レルムの参照
role_view-users=ユーザーの参照
role_view-applications=アプリケーションの参照
role_view-clients=クライアントの参照
role_view-events=イベントの参照
role_view-identity-providers=アイデンティティー・プロバイダーの参照
role_manage-realm=レルムの管理
role_manage-users=ユーザーの管理
role_manage-applications=アプリケーションの管理
role_manage-identity-providers=アイデンティティー・プロバイダーの管理
role_manage-clients=クライアントの管理
role_manage-events=イベントの管理
role_view-profile=プロファイルの参照
role_manage-account=アカウントの管理
role_manage-account-links=アカウントリンクの管理
role_read-token=トークンの参照
role_offline-access=オフライン・アクセス
client_account=アカウント
client_account-console=アカウント・コンソール
client_security-admin-console=セキュリティー管理コンソール
client_admin-cli=管理CLI
client_realm-management=レルム管理
client_broker=ブローカー
requiredFields=必須フィールド
invalidUserMessage=無効なユーザー名またはパスワードです。
invalidUsernameMessage=ユーザー名が無効です。
invalidUsernameOrEmailMessage=ユーザー名またはメールアドレスが無効です。
invalidPasswordMessage=パスワードが無効です。
invalidEmailMessage=無効なメールアドレスです。
accountDisabledMessage=アカウントが無効です。管理者に連絡してください。
accountTemporarilyDisabledMessage=無効なユーザー名またはパスワードです。
accountPermanentlyDisabledMessage=無効なユーザー名またはパスワードです。
accountTemporarilyDisabledMessageTotp=無効なオーセンティケーター・コードです。
accountPermanentlyDisabledMessageTotp=無効なオーセンティケーター・コードです。
expiredCodeMessage=ログイン・タイムアウトが発生しました。再度ログインしてください。
expiredActionMessage=アクションは期限切れです。今すぐログインしてください。
expiredActionTokenNoSessionMessage=アクションは期限切れです。
expiredActionTokenSessionExistsMessage=アクションは期限切れです。もう一度やり直してください。
missingFirstNameMessage=名を指定してください。
missingLastNameMessage=姓を指定してください。
missingEmailMessage=Eメールを指定してください。
missingUsernameMessage=ユーザー名を指定してください。
missingPasswordMessage=パスワードを指定してください。
missingTotpMessage=オーセンティケーター・コードを指定してください。
missingTotpDeviceNameMessage=デバイス名を指定してください。
notMatchPasswordMessage=パスワードが一致していません。
invalidPasswordExistingMessage=既存のパスワードが不正です。
invalidPasswordBlacklistedMessage=無効なパスワード: パスワードがブラックリストに含まれています。
invalidPasswordConfirmMessage=パスワード確認が一致していません。
invalidTotpMessage=無効なオーセンティケーター・コードです。
usernameExistsMessage=既に存在するユーザー名です。
emailExistsMessage=既に存在するEメールです。
federatedIdentityExistsMessage={0}{1}のユーザーは既に存在します。そのアカウントをリンクするにはアカウント管理にログインしてください。
confirmLinkIdpTitle=既に存在するアカウントです。
federatedIdentityConfirmLinkMessage={0}{1}のユーザーは既に存在します。継続しますか?
federatedIdentityConfirmReauthenticateMessage={1}でアカウントをリンクするために{0}として認証します
nestedFirstBrokerFlowMessage={0}ユーザー{1}は既知のユーザーにリンクされていません。
confirmLinkIdpReviewProfile=プロファイルの確認
confirmLinkIdpContinue=既存のアカウントに追加する
configureTotpMessage=アカウントを有効にするにはモバイル・オーセンティケーターのセットアップが必要です。
updateProfileMessage=アカウントを有効にするにはユーザー・プロファイルの更新が必要です。
updatePasswordMessage=アカウントを有効にするにはパスワードの更新が必要です。
resetPasswordMessage=パスワードを変更する必要があります。
verifyEmailMessage=アカウントを有効にするにはメールアドレスの確認が必要です。
linkIdpMessage=アカウントを{0}とリンクするにはメールアドレスの確認が必要です。
emailSentMessage=詳細な手順を記載したEメールをすぐに受信してください。
emailSendErrorMessage=Eメールの送信に失敗しました。しばらく時間をおいてから再度お試しください。
accountUpdatedMessage=アカウントが更新されました。
accountPasswordUpdatedMessage=パスワードが更新されました。
delegationCompleteHeader=ログインに成功しました
delegationCompleteMessage=このブラウザーのウィンドウを閉じて、コンソール・アプリケーションに戻ることができます。
delegationFailedHeader=ログインに失敗しました
delegationFailedMessage=このブラウザー・ウィンドウを閉じてコンソール・アプリケーションに戻り、再度ログインを試みることができます。
noAccessMessage=アクセスがありません
invalidPasswordMinLengthMessage=無効なパスワード: 最小{0}の長さが必要です。
invalidPasswordMinDigitsMessage=無効なパスワード: 少なくとも{0}文字の数字を含む必要があります。
invalidPasswordMinLowerCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の小文字を含む必要があります。
invalidPasswordMinUpperCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の大文字を含む必要があります。
invalidPasswordMinSpecialCharsMessage=無効なパスワード: 少なくとも{0}文字の特殊文字を含む必要があります。
invalidPasswordNotUsernameMessage=無効なパスワード: ユーザー名と同じパスワードは禁止されています。
invalidPasswordRegexPatternMessage=無効なパスワード: 正規表現パターンと一致しません。
invalidPasswordHistoryMessage=無効なパスワード: 最近の{0}パスワードのいずれかと同じパスワードは禁止されています。
invalidPasswordGenericMessage=無効なパスワード: 新しいパスワードはパスワード・ポリシーと一致しません。
failedToProcessResponseMessage=応答を処理できませんでした
httpsRequiredMessage=HTTPSが必須です
realmNotEnabledMessage=レルムが有効ではありません
invalidRequestMessage=無効なリクエストです
failedLogout=ログアウトに失敗しました
unknownLoginRequesterMessage=不明なログイン要求元です
loginRequesterNotEnabledMessage=ログイン要求元は有効ではありません
bearerOnlyMessage=bearer-onlyのアプリケーションはブラウザー・ログインを開始することが許可されていません
standardFlowDisabledMessage=与えられたresponse_typeでクライアントはブラウザー・ログインを開始することが許可されていません。標準フローは無効です。
implicitFlowDisabledMessage=与えられたresponse_typeでクライアントはブラウザー・ログインを開始することが許可されていません。インプリシット・フローは無効です。
invalidRedirectUriMessage=無効なリダイレクトURIです
unsupportedNameIdFormatMessage=サポートされていないNameID Formatです
invalidRequesterMessage=無効な要求元です
registrationNotAllowedMessage=登録は許可されていません
resetCredentialNotAllowedMessage=クレデンシャルのリセットは許可されていません
permissionNotApprovedMessage=パーミッションは承認されていません。
noRelayStateInResponseMessage=アイデンティティー・プロバイダーからの応答にRelayStateがありません。
insufficientPermissionMessage=アイデンティティーにリンクするには不十分なパーミッションです。
couldNotProceedWithAuthenticationRequestMessage=アイデンティティー・プロバイダーへの認証リクエストを続行できませんでした。
couldNotObtainTokenMessage=アイデンティティー・プロバイダーからトークンを取得できませんでした。
unexpectedErrorRetrievingTokenMessage=アイデンティティー・プロバイダーからのトークン取得で予期せぬエラーが発生しました。
unexpectedErrorHandlingResponseMessage=アイデンティティー・プロバイダーからの応答を処理する際に予期せぬエラーが発生しました。
identityProviderAuthenticationFailedMessage=認証に失敗しました。アイデンティティー・プロバイダーを使用して認証できませんでした。
couldNotSendAuthenticationRequestMessage=アイデンティティー・プロバイダーに認証リクエストを送信することができませんでした。
unexpectedErrorHandlingRequestMessage=アイデンティティー・プロバイダーへの認証リクエストを処理する際に予期せぬエラーが発生しました。
invalidAccessCodeMessage=無効なアクセスコードです。
sessionNotActiveMessage=セッションが有効ではありません。
invalidCodeMessage=エラーが発生しました。アプリケーションを介して再度ログインしてください。
identityProviderUnexpectedErrorMessage=アイデンティティー・プロバイダーによる認証の際に予期せぬエラーが発生しました
identityProviderNotFoundMessage=該当の識別子を持つアイデンティティー・プロバイダーが見つかりませんでした。
identityProviderLinkSuccess=Eメールを正常に確認しました。元のブラウザーに戻ってログインしてください。
staleCodeMessage=このページはもはや有効ではありませんので、アプリケーションに戻り再度ログインしてください
realmSupportsNoCredentialsMessage=レルムはクレデンシャル・タイプをサポートしていません。
credentialSetupRequired=ログインできません。クレデンシャルのセットアップが必要です。
identityProviderNotUniqueMessage=レルムは複数のアイデンティティー・プロバイダーをサポートしています。どのアイデンティティー・プロバイダーが認証に使用されるべきか判断できませんでした。
emailVerifiedMessage=メールアドレスが確認できました。
staleEmailVerificationLink=クリックしたリンクは古いリンクであり、有効ではありません。おそらく、すでにメールを確認しています。
identityProviderAlreadyLinkedMessage={0}によって返された連携済みアイデンティティーは、すでに別のユーザーにリンクされています。
confirmAccountLinking=アイデンティティー・プロバイダー{1}のアカウント{0}とあなたのアカウントとのリンクを確認してください。
confirmEmailAddressVerification=Eメールアドレス{0}の有効性を確認してください。
confirmExecutionOfActions=次の操作を実行します。
backToApplication=&laquo; アプリケーションに戻る
missingParameterMessage=不足パラメーター\: {0}
clientNotFoundMessage=クライアントが見つかりません。
clientDisabledMessage=クライアントが無効になっています。
invalidParameterMessage=無効なパラメーター\: {0}
alreadyLoggedIn=既にログインしています。
differentUserAuthenticated=すでにこのセッションで異なるユーザー''{0}''として認証されています。まずログアウトしてください。
brokerLinkingSessionExpired=要求されたブローカー・アカウントのリンクは、現在のセッションでは有効ではありません。
proceedWithAction=&raquo; 続行するにはここをクリックしてください
requiredAction.CONFIGURE_TOTP=OTPの設定
requiredAction.TERMS_AND_CONDITIONS=利用規約
requiredAction.UPDATE_PASSWORD=パスワードの更新
requiredAction.UPDATE_PROFILE=プロファイルの更新
requiredAction.VERIFY_EMAIL=Eメールの確認
doX509Login=次のユーザーとしてログインします\:
clientCertificate=X509クライアント証明書\:
noCertificate=[証明書なし]


pageNotFound=ページが見つかりません
internalServerError=内部サーバーエラーが発生しました
console-username=ユーザー名:
console-password=パスワード:
console-otp=ワンタイム・パスワード:
console-new-password=新しいパスワード:
console-confirm-password=パスワードの確認:
console-update-password=パスワードの更新が必要です。
console-verify-email=メールアドレスを確認する必要があります。確認コードを含むメールを{0}に送信しました。このコードを以下に入力してください。
console-email-code=Eメールコード：
console-accept-terms=利用規約に同意しますか？ [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=ユーザー情報
openshift.scope.user_check-access=ユーザーアクセス情報
openshift.scope.user_full=フルアクセス
openshift.scope.list-projects=プロジェクトの一覧表示

# SAML authentication
saml.post-form.title=認証リダイレクト
saml.post-form.message=リダイレクトしています。お待ちください。
saml.post-form.js-disabled=JavaScriptが無効になっています。有効にすることを強くお勧めします。継続するには、下のボタンをクリックしてください。

#authenticators
otp-display-name=オーセンティケーター・アプリケーション
otp-help-text=オーセンティケーター・アプリケーションから取得した確認コードを入力してください。
password-display-name=パスワード
password-help-text=パスワードを入力してログインします。
auth-username-form-display-name=ユーザー名
auth-username-form-help-text=ユーザー名を入力してログインを開始します
auth-username-password-form-display-name=ユーザー名とパスワード
auth-username-password-form-help-text=ユーザー名とパスワードを入力してログインしてください。

# WebAuthn
webauthn-display-name=セキュリティーキー
webauthn-help-text=セキュリティーキーを使用してログインしてください。
webauthn-passwordless-display-name=セキュリティーキー
webauthn-passwordless-help-text=パスワードレス・ログインにセキュリティーキーを使用します。
webauthn-login-title=セキュリティーキー・ログイン
webauthn-registration-title=セキュリティーキーの登録
webauthn-available-authenticators=利用可能なオーセンティケーター

# WebAuthn Error
webauthn-error-title=セキュリティーキー・エラー
webauthn-error-registration=セキュリティーキーを登録できませんでした。
webauthn-error-api-get=セキュリティーキーによる認証に失敗しました。
webauthn-error-different-user=最初に認証されたユーザーは、セキュリティーキーによって認証されたユーザーではありません。
webauthn-error-auth-verification=セキュリティーキーの認証結果が無効です。
webauthn-error-register-verification=セキュリティーキーの登録結果が無効です。
webauthn-error-user-not-found=セキュリティーキーで認証された不明なユーザー。
identity-provider-redirector=別のアイデンティティー・プロバイダーと接続する
frontchannel-logout.title=ログアウト
frontchannel-logout.message=以下のアプリケーションからログアウトしました。
logoutConfirmTitle=ログアウト
logoutConfirmHeader=ログアウトしますか？
doLogout=ログアウト
readOnlyUsernameMessage=読み取り専用のため、ユーザー名を更新することはできません。
deletingAccountForbidden=アカウントを削除するために必要な権限がありません。管理者に連絡してください。
loginIdpReviewProfileTitle=アカウント情報の更新
reauthenticate=続行するためには再ログインしてください
oauthGrantTos=利用規約
oauthGrantPolicy=プライバシーポリシー
emailUpdateConfirmationSentTitle=確認Eメールが送信されました
termsAcceptanceRequired=利用規約に同意する必要があります。
acceptTerms=利用規約に同意します
deleteCredentialTitle={0}の削除
deleteCredentialMessage={0}を削除しますか？
hidePassword=パスワードの非表示
showPassword=パスワードの表示
website=Webページ
phoneNumber=電話番号
gender=性別
birthday=誕生日
zoneinfo=タイムゾーン
logoutOtherSessions=他のデバイスからのサインアウト
totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
verifyOAuth2DeviceUserCode=デバイスによるコードを入力し、送信をクリックしてください
oauth2DeviceInvalidUserCodeMessage=無効なコードです、再実行してください。
errorDeletingAccount=アカウントの削除中にエラーが発生しました
emailUpdateConfirmationSent=確認Eメールが{0}に送信されました。指示に従い、Eメールの更新を完了してください。
usb=USB
internal=Internal
emailUpdatedTitle=Eメールが更新されました
unknown=Unknown
emailUpdated=アカウントのEメールが{0}に更新されました。
nfc=NFC
oauth2DeviceExpiredUserCodeMessage=コードの有効期限が切れてします。デバイスに戻り、もう一度お試し下さい。
bluetooth=Bluetooth
organizationScopeConsentText=組織
phoneNumberVerified=電話番号（確認済）
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
oauth2DeviceAuthorizationGrantDisabledMessage=クライアントはOAuth 2.0 デバイス認可グラントフローを開始することが許可されていません。フローはクライアントに対して無効です。
userDeletedSuccessfully=ユーザーの削除に成功しました
