package otbs.ms_astreint.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;

import java.util.Optional;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class AuditConfig {

    @Bean
    public AuditorAware<String> auditorProvider() {
        return new SpringSecurityAuditorAware();
    }

    public static class SpringSecurityAuditorAware implements AuditorAware<String> {

        @Override
        public Optional<String> getCurrentAuditor() {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated()) {
                return Optional.of("system");
            }

            // Si c'est un token JWT, extraire le nom d'utilisateur préféré
            if (authentication.getPrincipal() instanceof Jwt jwt) {
                String preferredUsername = jwt.getClaimAsString("preferred_username");
                if (preferredUsername != null) {
                    return Optional.of(preferredUsername);
                }
                
                String subject = jwt.getSubject();
                if (subject != null) {
                    return Optional.of(subject);
                }
            }

            // Fallback sur le nom de l'authentification
            String name = authentication.getName();
            return Optional.of(name != null ? name : "unknown");
        }
    }
}
