# Server configuration
server.port=9005
spring.application.name=ms-mission

# Configuration des services externes
reservation.service.url=${RESERVATION_SERVICE_URL:http://localhost:9006}

# Configuration de la DB
spring.datasource.url=${DB_URL:*****************************************}
spring.datasource.username=${DB_USERNAME:parcauto}
spring.datasource.password=${DB_PASSWORD:parcauto}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Hikari Connection Pool
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000

# EasyProject API Configuration
easyproject.api.url=https://1e86801239.bigde3.easyproject.com
easyproject.api.key=e4f6d505dd5c8b44e108ca26834f62775aee8671

# Permettre l'écrasement des définitions de beans
spring.main.allow-bean-definition-overriding=true

# Configuration de journalisation
logging.level.otbs.ms_mission.client.easyproject=DEBUG
logging.level.feign=DEBUG

# Configuration Swagger UI
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.docExpansion=none
springdoc.swagger-ui.defaultModelsExpandDepth=-1
springdoc.swagger-ui.displayRequestDuration=true
springdoc.swagger-ui.filter=true

# Eureka client configuration
eureka.client.service-url.defaultZone=${Eureka-url:http://localhost:9102/eureka}
eureka.client.enabled=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true

# Logging configuration
logging.level.root=INFO
logging.level.OTBS.ms-mission=DEBUG
logging.level.com.netflix.discovery=DEBUG
logging.level.com.netflix.eureka=DEBUG
logging.level.org.springframework.security=DEBUG
