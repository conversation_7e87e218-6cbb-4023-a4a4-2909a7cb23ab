<#import "footer.ftl" as loginFooter>
<#macro registrationLayout bodyClass="" displayInfo=false displayMessage=true displayRequiredFields=false>
<!DOCTYPE html>
<html class="${properties.kcHtmlClass!}" lang="${lang}"<#if realm.internationalizationEnabled> dir="${(locale.rtl)?then('rtl','ltr')}"</#if>>

<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#4318FF">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <#if properties.meta?has_content>
        <#list properties.meta?split(' ') as meta>
            <meta name="${meta?split('==')[0]}" content="${meta?split('==')[1]}"/>
        </#list>
    </#if>
    <title>${msg("loginTitle",(realm.displayName!''))} - Parc Auto</title>
    <link rel="icon" href="${url.resourcesPath}/img/favicon.ico" />
    <#if properties.stylesCommon?has_content>
        <#list properties.stylesCommon?split(' ') as style>
            <link href="${url.resourcesCommonPath}/${style}" rel="stylesheet" />
        </#list>
    </#if>
    <#if properties.styles?has_content>
        <#list properties.styles?split(' ') as style>
            <link href="${url.resourcesPath}/${style}" rel="stylesheet" />
        </#list>
    </#if>
    <#if properties.scripts?has_content>
        <#list properties.scripts?split(' ') as script>
            <script src="${url.resourcesPath}/${script}" type="text/javascript"></script>
        </#list>
    </#if>
    <script type="importmap">
        {
            "imports": {
                "rfc4648": "${url.resourcesCommonPath}/vendor/rfc4648/rfc4648.js"
            }
        }
    </script>
    <script src="${url.resourcesPath}/js/menu-button-links.js" type="module"></script>
    <#if scripts??>
        <#list scripts as script>
            <script src="${script}" type="text/javascript"></script>
        </#list>
    </#if>
    <script type="module">
        import { startSessionPolling } from "${url.resourcesPath}/js/authChecker.js";

        startSessionPolling(
          "${url.ssoLoginInOtherTabsUrl?no_esc}"
        );
    </script>
    <#if authenticationSession??>
        <script type="module">
            import { checkAuthSession } from "${url.resourcesPath}/js/authChecker.js";

            checkAuthSession(
                "${authenticationSession.authSessionIdHash}"
            );
        </script>
    </#if>
</head>

<body class="${properties.kcBodyClass!}">
<div class="${properties.kcLoginClass!}">
    <div id="kc-header" class="${properties.kcHeaderClass!}">
        <div id="kc-header-wrapper" class="${properties.kcHeaderWrapperClass!}">
        <div class="logo-area">
            <img src="${url.resourcesPath}/img/car.png" alt="OTBS Logo" class="brand-logo responsive-img">
            <div class="company-logo"><span class="parc">Parc</span><span class="auto">Auto</span></div>
        </div>
            <img src="${url.resourcesPath}/img/OTBS.png" alt="OTBS Logo" class="brand-logo responsive-img">

            

            <!-- Information utilisateur dans la navbar -->
            <#if auth?has_content && auth.showUsername() && !auth.showResetCredentials()>
                <div class="user-info-content">
                    <div class="user-avatar">
                        <img src="${url.resourcesPath}/img/utilisateur.png" alt="User Icon" class="user-icon">
                    </div>
                    <div class="user-details">
                        <span class="welcome-text">Bienvenue</span>
                        <span class="username">${auth.attemptedUsername}</span>
                    </div>
                    <a href="${url.loginRestartFlowUrl}" class="switch-account" aria-label="${msg("restartLoginTooltip")}">
                        <i class="switch-icon"></i>
                        <span>Changer</span>
                    </a>
                </div>
            </#if>
        </div>
    </div>

    <div class="${properties.kcFormCardClass!}">
        <header class="${properties.kcFormHeaderClass!}">
            <#if realm.internationalizationEnabled && locale.supported?size gt 1>
                <div class="${properties.kcLocaleMainClass!}" id="kc-locale">
                    <div id="kc-locale-wrapper" class="${properties.kcLocaleWrapperClass!}">
                        <div id="kc-locale-dropdown" class="menu-button-links ${properties.kcLocaleDropDownClass!}">
                            <button tabindex="1" id="kc-current-locale-link" aria-label="${msg("languages")}" aria-haspopup="true" aria-expanded="false" aria-controls="language-switch1">${locale.current}</button>
                            <ul role="menu" tabindex="-1" aria-labelledby="kc-current-locale-link" aria-activedescendant="" id="language-switch1" class="${properties.kcLocaleListClass!}">
                                <#assign i = 1>
                                <#list locale.supported as l>
                                    <li class="${properties.kcLocaleListItemClass!}" role="none">
                                        <a role="menuitem" id="language-${i}" class="${properties.kcLocaleItemClass!}" href="${l.url}">${l.label}</a>
                                    </li>
                                    <#assign i++>
                                </#list>
                            </ul>
                        </div>
                    </div>
                </div>
            </#if>

            <#if !(auth?has_content && auth.showUsername() && !auth.showResetCredentials())>
                <#if displayRequiredFields>
                    <div class="${properties.kcContentWrapperClass!}">
                        <div class="${properties.kcLabelWrapperClass!} subtitle">
                            <span class="subtitle"><span class="required">*</span> ${msg("requiredFields")}</span>
                        </div>
                        <div class="col-md-10">
                            <h1 id="kc-page-title"><#nested "header"></h1>
                        </div>
                    </div>
                <#else>
                    <h1 id="kc-page-title"><#nested "header"></h1>
                </#if>
            <#else>
                <#if displayRequiredFields>
                    <div class="${properties.kcContentWrapperClass!}">
                        <div class="${properties.kcLabelWrapperClass!} subtitle">
                            <span class="subtitle"><span class="required">*</span> ${msg("requiredFields")}</span>
                        </div>
                        <div class="col-md-10">
                            <#nested "show-username">
                            <div id="kc-username" class="${properties.kcFormGroupClass!}">
                                <label id="kc-attempted-username">${auth.attemptedUsername}</label>
                                <a id="reset-login" href="${url.loginRestartFlowUrl}" aria-label="${msg("restartLoginTooltip")}">
                                    <div class="kc-login-tooltip">
                                        <i class="${properties.kcResetFlowIcon!}"></i>
                                        <span class="kc-tooltip-text">${msg("restartLoginTooltip")}</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                <#else>
                    <#nested "show-username">
                    <div id="kc-username" class="${properties.kcFormGroupClass!}">
                        <label id="kc-attempted-username">${auth.attemptedUsername}</label>
                        <a id="reset-login" href="${url.loginRestartFlowUrl}" aria-label="${msg("restartLoginTooltip")}">
                            <div class="kc-login-tooltip">
                                <i class="${properties.kcResetFlowIcon!}"></i>
                                <span class="kc-tooltip-text">${msg("restartLoginTooltip")}</span>
                            </div>
                        </a>
                    </div>
                </#if>
            </#if>
        </header>

        <div id="kc-content">
            <div id="kc-content-wrapper">
                <!-- AFFICHAGE DES MESSAGES AVEC ANIMATION DE FONDU ET DÉPLACEMENT -->
                <#if message?? && message?has_content>
                    <div class="alert-system">
                        <div class="alert alert-${message.type}">
                            <span class="alert-icon"></span>
                            <div class="alert-content">
                                <div class="alert-title">
                                    <#if message.type = 'success'>
                                        ${msg("successTitle")}
                                    <#elseif message.type = 'warning'>
                                        ${msg("warningTitle")}
                                    <#elseif message.type = 'error'>
                                        ${msg("errorTitle")}
                                    <#else>
                                        ${msg("infoTitle")}
                                    </#if>
                                </div>
                                <p class="alert-text">
                                    ${kcSanitize(message.summary)?no_esc}
                                </p>
                            </div>
                            <button type="button" class="alert-close" aria-label="Close"></button>
                            <div class="alert-progress">
                                <div class="alert-progress-bar"></div>
                            </div>
                        </div>
                    </div>
                </#if>

                <#nested "form">

                <#if auth?has_content && auth.showTryAnotherWayLink()>
                    <form id="kc-select-try-another-way-form" action="${url.loginAction}" method="post">
                        <div class="${properties.kcFormGroupClass!}">
                            <input type="hidden" name="tryAnotherWay" value="on"/>
                            <a href="#" id="try-another-way"
                               onclick="document.forms['kc-select-try-another-way-form'].requestSubmit();return false;">${msg("doTryAnotherWay")}</a>
                        </div>
                    </form>
                </#if>

                <#nested "socialProviders">

                <#-- Commenté pour masquer la section info
                <#if displayInfo>
                    <#assign infoContent><#nested "info"></#assign>
                    <#if infoContent?has_content>
                        <div id="kc-info" class="${properties.kcSignUpClass!}">
                            <div id="kc-info-wrapper" class="${properties.kcInfoAreaWrapperClass!}">
                                ${infoContent}
                            </div>
                        </div>
                    </#if>
                </#if>
                -->
            </div>
        </div>

        <@loginFooter.content/>
    </div>
</div>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion des alertes
        function initializeAlerts() {
            const alerts = document.querySelectorAll('.alert');
            const ALERT_DURATION = 5000; // 5 secondes

            alerts.forEach(alert => {
                // Auto-dismiss après 5 secondes
                setTimeout(() => {
                    dismissAlert(alert);
                }, ALERT_DURATION);

                // Gestion du bouton de fermeture
                const closeButton = alert.querySelector('.alert-close');
                if (closeButton) {
                    closeButton.addEventListener('click', () => {
                        dismissAlert(alert);
                    });
                }
            });
        }

        function dismissAlert(alert) {
            if (!alert.classList.contains('hiding')) {
                alert.classList.add('hiding');
                setTimeout(() => {
                    const alertSystem = alert.closest('.alert-system');
                    if (alert && alert.parentNode) {
                        alert.remove();
                    }
                    // Si c'était la dernière alerte, supprimer le conteneur
                    if (alertSystem && alertSystem.children.length === 0) {
                        alertSystem.remove();
                    }
                }, 500); // Correspond à la durée de l'animation
            }
        }

        // Initialiser les alertes au chargement
        initializeAlerts();

        // Observer les changements dans le DOM pour les nouvelles alertes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.classList && node.classList.contains('alert-system')) {
                            initializeAlerts();
                        }
                    });
                }
            });
        });

        // Observer le conteneur principal pour les nouvelles alertes
        const container = document.querySelector('.${properties.kcFormCardClass!}');
        if (container) {
            observer.observe(container, { childList: true, subtree: true });
        }
    });
</script>
</body>
</html>
</#macro>


