emailVerificationSubject=ตรวจสอบอีเมล
emailVerificationBody=มีคนสร้างบัญชี {2} ด้วยที่อยู่อีเมลนี้ หากคุณเป็นผู้สร้างบัญชี คลิกที่ลิงก์ด้านล่างเพื่อยืนยันที่อยู่อีเมลของคุณ\n\n{0}\n\nลิงก์นี้จะหมดอายุภายใน {3}.\n\nถ้าคุณไม่ได้สร้างบัญชี ไม่ต้องสนใจข้อความนี้
emailVerificationBodyHtml=<p>มีคนสร้างบัญชี {2} ด้วยที่อยู่อีเมลนี้ หากคุณเป็นผู้สร้างบัญชี คลิกที่ลิงก์ด้านล่างเพื่อยืนยันที่อยู่อีเมลของคุณ</p><p><a href="{0}">ลิงก์สำหรับการตรวจสอบที่อยู่อีเมล</a></p><p>ลิงก์นี้จะหมดอายุภายใน {3}.</p><p>ถ้าคุณไม่ได้สร้างบัญชี ไม่ต้องสนใจข้อความนี้</p>
emailUpdateConfirmationSubject=ตรวจสอบอีเมลใหม่
emailUpdateConfirmationBody=คลิกที่ลิงก์ด้านล่างเพื่อปรับปรุงบัญชี {2} ของคุณด้วยที่อยู่อีเมล {1}\n\n{0}\n\nลิงก์นี้จะหมดอายุภายใน {3}.\n\nหากคุณไม่ต้องการดำเนินการแก้ไข ไม่ต้องสนใจข้อความนี้ จะไม่มีสิ่งใดถูกเปลี่ยนแปลง
emailUpdateConfirmationBodyHtml=<p>คลิกที่ลิงก์ด้านล่างเพื่อปรับปรุงบัญชี {2} ของคุณด้วยที่อยู่อีเมล {1}</p><p><a href="{0}">{0}</a></p><p>ลิงก์นี้จะหมดอายุภายใน {3}.</p><p>หากคุณไม่ต้องการดำเนินการแก้ไข ไม่ต้องสนใจข้อความนี้ จะไม่มีสิ่งใดถูกเปลี่ยนแปลง</p>
emailTestSubject=[KEYCLOAK] - ข้อความทดสอบ SMTP
emailTestBody=นี่คือข้อความทดสอบ
emailTestBodyHtml=<p>นี่คือข้อความทดสอบ</p>
identityProviderLinkSubject=เชื่อมโยง {0}
identityProviderLinkBody=มีผู้ต้องการเชื่อมโยงบัญชี "{1}" ของคุณกับบัญชี "{0}" ของผู้ใช้งาน {2} หากคุณเป็นผู้ดำเนินการ กรุณาคลิกที่ลิงก์ด้านล่างเพื่อเชื่อมโยงบัญชี\n\n{3}\n\nลิงก์นี้จะหมดอายุภายใน {5}.\n\nหากคุณไม่ต้องการเชื่อมโยงบัญชี ไม่ต้องสนใจข้อความนี้ ถ้าคุณเชื่อมโยงบัญชี คุณจะสามารถเข้าสู่ระบบ {1} ผ่าน {0} ได้
identityProviderLinkBodyHtml=<p>มีผู้ต้องการเชื่อมโยงบัญชี <b>{1}</b> ของคุณกับบัญชี <b>{0}</b> ของผู้ใช้งาน {2} หากคุณเป็นผู้ดำเนินการ กรุณาคลิกที่ลิงก์ด้านล่างเพื่อเชื่อมโยงบัญชี</p><p><a href="{3}">ลิงก์สำหรับการยืนยันการเชื่อมโยงบัญชี</a></p><p>ลิงก์นี้จะหมดอายุภายใน {5}.</p><p>หากคุณไม่ต้องการเชื่อมโยงบัญชี ไม่ต้องสนใจข้อความนี้ ถ้าคุณเชื่อมโยงบัญชี คุณจะสามารถเข้าสู่ระบบ {1} ผ่าน {0} ได้</p>
passwordResetSubject=รีเซ็ตรหัสผ่าน
passwordResetBody=มีการร้องขอเปลี่ยนข้อมูลการเข้าสู่ระบบของบัญชี {2} ของคุณ หากคุณเป็นผู้ดำเนินการ กรุณาคลิกที่ลิงก์ด้านล่างเพื่อรีเซ็ต\n\n{0}\n\nลิงก์และรหัสนี้จะหมดอายุภายใน {3}\n\nหากคุณไม่ต้องการรีเซ็ตข้อมูลการเข้าสู่ระบบ ไม่ต้องสนใจข้อความนี้ จะไม่มีสิ่งใดถูกเปลี่ยนแปลง
passwordResetBodyHtml=<p>มีการร้องขอเปลี่ยนข้อมูลการเข้าสู่ระบบของบัญชี {2} ของคุณ หากคุณเป็นผู้ดำเนินการ กรุณาคลิกที่ลิงก์ด้านล่างเพื่อรีเซ็ต</p><p><a href="{0}">ลิงก์สำหรับการรีเซ็ตข้อมูล</a></p><p>ลิงก์นี้จะหมดอายุภายใน {3}</p><p>หากคุณไม่ต้องการรีเซ็ตข้อมูลการเข้าสู่ระบบ ไม่ต้องสนใจข้อความนี้ จะไม่มีสิ่งใดถูกเปลี่ยนแปลง</p>
executeActionsSubject=ปรับปรุงบัญชีของคุณ
executeActionsBody=ผู้ดูแลระบบของคุณได้ร้องขอให้คุณปรับปรุงบัญชี {2} ของคุณโดยการดำเนินการต่อไปนี้: {3} คลิกที่ลิงก์ด้านล่างเพื่อเริ่มกระบวนการนี้\n\n{0}\n\nลิงก์นี้จะหมดอายุภายใน {4}\n\nหากคุณไม่ทราบว่าผู้ดูแลระบบของคุณได้ร้องขอ ไม่ต้องสนใจข้อความนี้ จะไม่มีสิ่งใดถูกเปลี่ยนแปลง
executeActionsBodyHtml=<p>ผู้ดูแลระบบของคุณได้ร้องขอให้คุณปรับปรุงบัญชี {2} ของคุณโดยการดำเนินการต่อไปนี้: {3} คลิกที่ลิงก์ด้านล่างเพื่อเริ่มกระบวนการนี้</p><p><a href="{0}">ลิงก์สำหรับการปรับปรุงบัญชี</a></p><p>ลิงก์นี้จะหมดอายุภายใน {4}.</p><p>หากคุณไม่ทราบว่าผู้ดูแลระบบของคุณได้ร้องขอ ไม่ต้องสนใจข้อความนี้ จะไม่มีสิ่งใดถูกเปลี่ยนแปลง</p>
eventLoginErrorSubject=ข้อผิดพลาดในการเข้าสู่ระบบ
eventLoginErrorBody=ตรวจพบความพยายามเข้าสู่ระบบที่ไม่สำเร็จด้วยบัญชีของคุณใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ
eventLoginErrorBodyHtml=<p>ตรวจพบความพยายามเข้าสู่ระบบที่ไม่สำเร็จด้วยบัญชีของคุณใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ</p>
eventRemoveTotpSubject=ลบ OTP
eventRemoveTotpBody=OTP ถูกลบออกจากบัญชีของคุณใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ
eventRemoveTotpBodyHtml=<p>OTP ถูกลบออกจากบัญชีของคุณใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ</p>
eventUpdatePasswordSubject=ปรับปรุงรหัสผ่าน
eventUpdatePasswordBody=รหัสผ่านของคุณถูกเปลี่ยนใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ
eventUpdatePasswordBodyHtml=<p>รหัสผ่านของคุณถูกเปลี่ยนใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ</p>
eventUpdateTotpSubject=ปรับปรุง OTP
eventUpdateTotpBody=OTP สำหรับบัญชีของคุณถูกปรับปรุงใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ
eventUpdateTotpBodyHtml=<p>OTP สำหรับบัญชีของคุณถูกปรับปรุงใน {0} จาก {1} หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดติดต่อผู้ดูแลระบบ</p>

requiredAction.CONFIGURE_TOTP=กำหนดค่า OTP
requiredAction.TERMS_AND_CONDITIONS=ข้อกำหนดและเงื่อนไข
requiredAction.UPDATE_PASSWORD=ปรับปรุงรหัสผ่าน
requiredAction.UPDATE_PROFILE=ปรับปรุงโปรไฟล์
requiredAction.VERIFY_EMAIL=ตรวจสอบอีเมล
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=สร้างรหัสการกู้คืน

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#วินาที|1#วินาที|1<วินาที}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#นาที|1#นาที|1<นาที}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#ชั่วโมง|1#ชั่วโมง|1<ชั่วโมง}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#วัน|1#วัน|1<วัน}

emailVerificationBodyCode=กรุณายืนยันที่อยู่อีเมลของคุณโดยการใส่รหัสต่อไปนี้\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>กรุณายืนยันที่อยู่อีเมลของคุณโดยการใส่รหัสต่อไปนี้</p><p><b>{0}</b></p>
