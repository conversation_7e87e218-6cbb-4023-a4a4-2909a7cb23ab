emailVerificationSubject=驗證電子信箱
emailVerificationBody=有人使用了這個電子信箱建立了一個 {2} 帳號。如果這是您的操作，請點擊下列連結驗證您的電子信箱\n\n{0}\n\n此連結將在 {3} 內失效。\n\n如果這不是您的操作，請忽略此訊息。
emailVerificationBodyHtml=<p>有人使用了這個電子信箱建立了一個 {2} 帳號。如果這是您的操作，請點擊下列連結驗證您的電子信箱</p><p><a href="{0}">驗證電子信箱連結</a></p><p>此連結將在 {3} 內失效。</p><p>如果您沒有建立帳號，請忽略此訊息。</p>
emailUpdateConfirmationSubject=驗證新的電子信箱
emailUpdateConfirmationBody=為了更新您的 {2} 帳號的電子信箱 {1}，請點擊下列連結\n\n{0}\n\n此連結將在 {3} 內失效。\n\n如果您不想進行這個修改，請忽略此訊息。
emailUpdateConfirmationBodyHtml=<p>為了更新您的 {2} 帳號的電子信箱 {1}，請點擊下列連結</p><p><a href="{0}">{0}</a></p><p>此連結將在 {3} 內失效。</p><p>如果您不想進行這個修改，請忽略此訊息。</p>
emailTestSubject=[KEYCLOAK] - SMTP 測試訊息
emailTestBody=這是一個測試訊息
emailTestBodyHtml=<p>這是一個測試訊息</p>
identityProviderLinkSubject=連結到 {0}
identityProviderLinkBody=有人試圖連結您的 "{1}" 帳號與使用者 {2} 的 "{0}" 帳號。如果這是您的操作，請點擊下列連結連結帳號\n\n{3}\n\n此連結將在 {5} 內失效。\n\n如果這不是您的操作，請忽略此訊息。如果您連結帳號，您將能夠透過 {0} 登入 {1}。
identityProviderLinkBodyHtml=<p>有人試圖連結您的 <b>{1}</b> 帳號與使用者 {2} 的 <b>{0}</b> 帳號。如果這是您的操作，請點擊下列連結連結帳號</p><p><a href="{3}">確認連結帳號</a></p><p>此連結將在 {5} 內失效。</p><p>如果這不是您的操作，請忽略此訊息。如果您連結帳號，您將能夠透過 {0} 登入 {1}。</p>
passwordResetSubject=重設密碼
passwordResetBody=有人剛剛要求重設您 {2} 的密碼。如果這是您的操作，請點擊下列連結重設它們。\n\n{0}\n\n此連結和代碼將在 {3} 內失效。\n\n如果您不想重設您的密碼，請忽略此訊息。
passwordResetBodyHtml=<p>有人剛剛要求重設您 {2} 的密碼。如果這是您的操作，請點擊下列連結重設它們。</p><p><a href="{0}">重設密碼連結</a></p><p>此連結和代碼將在 {3} 內失效。</p><p>如果您不想重設您的密碼，請忽略此訊息，甚麼都不會改變。</p>
executeActionsSubject=更新您的帳號
executeActionsBody=您的管理員剛剛要求您更新您的 {2} 帳號，透過執行以下動作：{3}。點擊下列連結開始這個流程。\n\n{0}\n\n此連結將在 {4} 內失效。\n\n如果您不知道您的管理員剛剛要求這個，請忽略此訊息，甚麼都不會改變。
executeActionsBodyHtml=<p>您的管理員剛剛要求您更新您的 {2} 帳號，透過執行以下動作：{3}。點擊下列連結開始這個流程。</p><p><a href="{0}">更新帳號連結</a></p><p>此連結將在 {4} 內失效。</p><p>如果您不知道您的管理員剛剛要求這個，請忽略此訊息，甚麼都不會改變。</p>
eventLoginErrorSubject=登入錯誤
eventLoginErrorBody=登入失敗，偵測到您的帳號在 {0} 從 {1} 進行登入。如果這不是您的操作，請聯絡系統管理員。
eventLoginErrorBodyHtml=<p>登入失敗，偵測到您的帳號在 {0} 從 {1} 進行登入。如果這不是您的操作，請聯絡系統管理員。</p>
eventRemoveTotpSubject=移除 OTP 設定
eventRemoveTotpBody=在 {0} OTP 設定已從您的帳號 {1} 移除。如果這不是您的操作，請聯絡系統管理員。
eventRemoveTotpBodyHtml=<p>在 {0} OTP 設定已從您的帳號 {1} 移除。如果這不是您的操作，請聯絡系統管理員。</p>
eventUpdatePasswordSubject=更新密碼
eventUpdatePasswordBody=您的密碼已在 {0} 從 {1} 更改。如果這不是您的操作，請聯絡系統管理員。
eventUpdatePasswordBodyHtml=<p>您的密碼已在 {0} 從 {1} 更改。如果這不是您的操作，請聯絡系統管理員。</p>
eventUpdateTotpSubject=更新 OTP 設定
eventUpdateTotpBody=您帳號的 OTP 設定已在 {0} 從 {1} 更新。如果這不是您的操作，請聯絡系統管理員。
eventUpdateTotpBodyHtml=<p>您帳號的 OTP 設定已在 {0} 從 {1} 更新。如果這不是您的操作，請聯絡系統管理員。</p>

requiredAction.CONFIGURE_TOTP=設定 OTP
requiredAction.TERMS_AND_CONDITIONS=服務條款
requiredAction.UPDATE_PASSWORD=更新密碼
requiredAction.UPDATE_PROFILE=更新個人資訊
requiredAction.VERIFY_EMAIL=驗證電子信箱
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=產生復原代碼

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds=秒
linkExpirationFormatter.timePeriodUnit.minutes=分
linkExpirationFormatter.timePeriodUnit.hours=小時
linkExpirationFormatter.timePeriodUnit.days=天

emailVerificationBodyCode=驗證您的電子信箱，請輸入以下代碼。\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>驗證您的電子信箱，請輸入以下代碼。</p><p><b>{0}</b></p>

