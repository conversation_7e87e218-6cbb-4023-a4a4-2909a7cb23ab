doSave=Lagre
doCancel=Avbryt
doLogOutAllSessions=Logg ut av alle sesjoner
doRemove=Fjern
doAdd=Legg til
doSignOut=Logg ut

editAccountHtmlTitle=Rediger konto
federatedIdentitiesHtmlTitle=Federerte identiteter
accountLogHtmlTitle=Kontologg
changePasswordHtmlTitle=Endre passord
sessionsHtmlTitle=Sesjoner
accountManagementTitle=Keycloak kontoadministrasjon
authenticatorTitle=Autentikator
applicationsHtmlTitle=Applikasjoner

authenticatorCode=Engangskode
email=E-post
firstName=Fornavn
givenName=Fornavn
fullName=Fullt navn
lastName=Etternavn
familyName=Etternavn
password=Passord
passwordConfirm=Bekreftelse
passwordNew=Nytt passord
username=Brukernavn
address=Adresse
street=Gate-/veinavn + husnummer
locality=By
region=Fylke
postal_code=Postnummer
country=Land
emailVerified=E-post bekreftet
gssDelegationCredential=GSS legitimasjonsdelegering

role_admin=Administrator
role_realm-admin=Administrator for sikkerhetsdomene
role_create-realm=Opprette sikkerhetsdomene
role_view-realm=Se sikkerhetsdomene
role_view-users=Se brukere
role_view-applications=Se applikasjoner
role_view-clients=Se klienter
role_view-events=Se hendelser
role_view-identity-providers=Se identitetsleverandører
role_manage-realm=Administrere sikkerhetsdomene
role_manage-users=Administrere brukere
role_manage-applications=Administrere applikasjoner
role_manage-identity-providers=Administrere identitetsleverandører
role_manage-clients=Administrere klienter
role_manage-events=Administrere hendelser
role_view-profile=Se profil
role_manage-account=Administrere konto
role_read-token=Lese token
role_offline-access=Frakoblet tilgang
role_uma_authorization=Skaffe tillatelser
client_account=Konto
client_security-admin-console=Sikkerhetsadministrasjonskonsoll
client_admin-cli=Kommandolinje-grensesnitt for administrator
client_realm-management=Sikkerhetsdomene-administrasjon
client_broker=Broker


requiredFields=Obligatoriske felt
allFieldsRequired=Alle felt må fylles ut

backToApplication=&laquo; Tilbake til applikasjonen
backTo=Tilbake til {0}

date=Dato
event=Hendelse
ip=IP
client=Klient
clients=Klienter
details=Detaljer
started=Startet
lastAccess=Sist benyttet
expires=Utløper
applications=Applikasjoner

account=Konto
federatedIdentity=Federert identitet
authenticator=Autentikator
sessions=Sesjoner
log=Logg

application=Applikasjon
availablePermissions=Tilgjengelige rettigheter
grantedPermissions=Innvilgede rettigheter
grantedPersonalInfo=Innvilget personlig informasjon
additionalGrants=Ekstra rettigheter
action=Handling
inResource=i
fullAccess=Full tilgang
offlineToken=Offline token
revoke=Opphev rettighet

configureAuthenticators=Konfigurerte autentikatorer
mobile=Mobiltelefon
totpStep1=Installer ett av følgende programmer på  mobilen din.
totpStep2=Åpne applikasjonen og skann strekkoden eller skriv inn koden.
totpStep3=Skriv inn engangskoden gitt av applikasjonen og klikk Lagre for å fullføre.

missingUsernameMessage=Vennligst oppgi brukernavn.
missingFirstNameMessage=Vennligst oppgi fornavn.
invalidEmailMessage=Ugyldig e-postadresse.
missingLastNameMessage=Vennligst oppgi etternavn.
missingEmailMessage=Vennligst oppgi e-postadresse.
missingPasswordMessage=Vennligst oppgi passord.
notMatchPasswordMessage=Passordene er ikke like.

missingTotpMessage=Vennligst oppgi engangskode.
invalidPasswordExistingMessage=Ugyldig eksisterende passord.
invalidPasswordConfirmMessage=Passordene er ikke like.
invalidTotpMessage=Ugyldig engangskode.

usernameExistsMessage=Brukernavnet finnes allerede.
emailExistsMessage=E-postadressen finnes allerede.

readOnlyUserMessage=Du kan ikke oppdatere kontoen din ettersom den er skrivebeskyttet.
readOnlyPasswordMessage=Du kan ikke oppdatere passordet ditt ettersom kontoen din er skrivebeskyttet.

successTotpMessage=Autentikator for mobiltelefon er konfigurert.
successTotpRemovedMessage=Autentikator for mobiltelefon er fjernet.

successGrantRevokedMessage=Vellykket oppheving av rettighet.

accountUpdatedMessage=Kontoen din har blitt oppdatert.
accountPasswordUpdatedMessage=Ditt passord har blitt oppdatert.

missingIdentityProviderMessage=Identitetsleverandør er ikke spesifisert.
invalidFederatedIdentityActionMessage=Ugyldig eller manglende handling.
identityProviderNotFoundMessage=Spesifisert identitetsleverandør ikke funnet.
federatedIdentityLinkNotActiveMessage=Denne identiteten er ikke lenger aktiv.
federatedIdentityRemovingLastProviderMessage=Du kan ikke fjerne siste federerte identitet ettersom du ikke har et passord.
identityProviderRedirectErrorMessage=Redirect til identitetsleverandør feilet.
identityProviderRemovedMessage=Fjerning av identitetsleverandør var vellykket.
identityProviderAlreadyLinkedMessage=Federert identitet returnert av {0} er allerede koblet til en annen bruker.
staleCodeAccountMessage=Siden har utløpt. Vennligst prøv en gang til.
consentDenied=Samtykke avslått.

accountDisabledMessage=Konto er deaktivert, kontakt administrator.

accountTemporarilyDisabledMessage=Konto er midlertidig deaktivert, kontakt administrator eller prøv igjen senere.
invalidPasswordMinLengthMessage=Ugyldig passord: minimum lengde {0}.
invalidPasswordMinLowerCaseCharsMessage=Ugyldig passord: må inneholde minimum {0} små bokstaver.
invalidPasswordMinDigitsMessage=Ugyldig passord: må inneholde minimum {0} sifre.
invalidPasswordMinUpperCaseCharsMessage=Ugyldig passord: må inneholde minimum {0} store bokstaver.
invalidPasswordMinSpecialCharsMessage=Ugyldig passord: må inneholde minimum {0} spesialtegn.
invalidPasswordNotUsernameMessage=Ugyldig passord: kan ikke være likt brukernavn.
invalidPasswordRegexPatternMessage=Ugyldig passord: tilfredsstiller ikke kravene for passord-mønster.
invalidPasswordHistoryMessage=Ugyldig passord: kan ikke være likt noen av de {0} foregående passordene.