invalidPasswordMinLengthMessage=Невірний пароль: довжина пароля повинна бути не менше {0} символів(а).
invalidPasswordMaxLengthMessage=Невірний пароль: довжина пароля повинна бути не більше {0} символів(а).
invalidPasswordMinLowerCaseCharsMessage=Невірний пароль: пароль має містити щонайменше {0} символів у нижньому регістрі.
invalidPasswordMinDigitsMessage=Невірний пароль: пароль має містити принаймні {0} цифр.
invalidPasswordMinUpperCaseCharsMessage=Невірний пароль: пароль має містити щонайменше {0} символів у верхньому регістрі.
invalidPasswordMinSpecialCharsMessage=Невірний пароль: пароль має містити щонайменше {0} спеціальних символів.
invalidPasswordNotUsernameMessage=Невірний пароль: пароль не повинен збігатися з іменем користувача.
invalidPasswordNotEmailMessage=Невірний пароль: пароль не повинен збігатися з адресою електронної пошти.
invalidPasswordRegexPatternMessage=Невірний пароль: пароль не відповідає шаблону(ам) регулярного виразу.
invalidPasswordHistoryMessage=Недійсний пароль: пароль не повинен збігатися з жодним з останніх {0} паролів.
invalidPasswordBlacklistedMessage=Невірний пароль: пароль у чорному списку.
invalidPasswordGenericMessage=Невірний пароль: новий пароль не відповідає політиці паролів.

ldapErrorEditModeMandatory=Режим редагування є обов''язковим
ldapErrorInvalidCustomFilter=Налаштований власноруч фільтр LDAP не починається з "(" або не закінчується на ")".
ldapErrorConnectionTimeoutNotNumber=Час очікування підключення має бути числом
ldapErrorReadTimeoutNotNumber=Час очікування читання має бути числом
ldapErrorMissingClientId=В конфігурації потрібно вказати ідентифікатор клієнта, якщо не використовується відображення ролей realm.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Не вдалося успадкувати групу та використати тип членства UID разом.
ldapErrorCantWriteOnlyForReadOnlyLdap=Неможливо встановити "тільки запис", коли LDAP-провайдер налаштований в режимі відмінному від "WRITABLE"
ldapErrorCantWriteOnlyAndReadOnly=Неможливо встановити тільки запис і тільки читання разом
ldapErrorCantEnableStartTlsAndConnectionPooling=Неможливо ввімкнути одночасно StartTLS і пул з''єднань.
ldapErrorCantEnableUnsyncedAndImportOff=Неможливо вимкнути імпорт користувачів, коли режим LDAP-провайдера - "UNSYNCED"
ldapErrorMissingGroupsPathGroup=Групи не існує - спочатку створіть групу за вказаним шляхом
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Політика перевірки паролів застосовна лише в режимі редагування WRITABLE

clientRedirectURIsFragmentError=URI перенаправлення не повинні містити фрагмент URI
clientRootURLFragmentError=Коренева URL-адреса не повинна містити фрагмент URL-адреси
clientRootURLIllegalSchemeError=Коренева URL-адреса містить невірну схему
clientBaseURLIllegalSchemeError=Базова URL-адреса містить невірну схему
backchannelLogoutUrlIllegalSchemeError="BackChannel logout URL" містить невірну схему
clientRedirectURIsIllegalSchemeError=URI перенаправлення містить невірну схему
clientBaseURLInvalid=Невірна базова URL-адреса
clientRootURLInvalid=Невірна коренева URL-адреса
clientRedirectURIsInvalid=Невірний URI перенаправлення
backchannelLogoutUrlIsInvalid=Невірний "BackChannel logout URL"


pairwiseMalformedClientRedirectURI=Клієнт містить невірний URI перенаправлення.
pairwiseClientRedirectURIsMissingHost=URI перенаправлення клієнта повинні містити вірний компонент хоста.
pairwiseClientRedirectURIsMultipleHosts=Без налаштованого URI ідентифікатора сектору, URI перенаправлення клієнта не повинні містити кілька компонентів хоста.
pairwiseMalformedSectorIdentifierURI=Невірний URI ідентифікатор.
pairwiseFailedToGetRedirectURIs=Не вдалося отримати URI перенаправлення з частини ідентифікатора URI.
pairwiseRedirectURIsMismatch=URI перенаправлення клієнта не відповідає URI перенаправлення, отриманим з URI ідентифікатора сектора.

duplicatedJwksSettings=Перемикач "Використовувати JWKS" і перемикач "Використовувати JWKS URL" не можуть бути ввімкнені одночасно.

error-invalid-value=Невірне значення.
error-invalid-blank=Будь ласка, вкажіть значення.
error-empty=Будь ласка, вкажіть значення.
error-invalid-length=Довжина атрибуту {0} повинна бути між {1} і {2}.
error-invalid-length-too-short=Довжина атрибуту {0} повинна бути не менше ніж {1} символ(а).
error-invalid-length-too-long=Довжина атрибуту {0} повинна бути не більше ніж {2} символ(а).
error-invalid-email=Невірна адреса електронної пошти.
error-invalid-number=Невірний номер.
error-number-out-of-range=Атрибут {0} має бути числом між {1} і {2}.
error-number-out-of-range-too-small=Значення атрибуту {0} повинно бути не менше ніж {1}.
error-number-out-of-range-too-big=Значення атрибуту {0} повинно бути не більше ніж {2}.
error-pattern-no-match=Невірне значення.
error-invalid-uri=Невірний URL.
error-invalid-uri-scheme=Невірна схема URL.
error-invalid-uri-fragment=Невірний фрагмент URL.
error-user-attribute-required=Будь ласка, вкажіть атрибут {0}.
error-invalid-date=Вказана невірна дата в атрибуті {0}.
error-user-attribute-read-only=Атрибут {0} лише для читання.
error-username-invalid-character={0} містить невірний символ.
error-person-name-invalid-character={0} містить невірний символ.