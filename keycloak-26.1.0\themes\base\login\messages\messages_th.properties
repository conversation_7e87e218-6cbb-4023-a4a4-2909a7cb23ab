doLogIn=เข้าสู่ระบบ
doRegister=ลงทะเบียน
doRegisterSecurityKey=ลงทะเบียน
doCancel=ยกเลิก
doSubmit=ส่ง
doBack=ย้อนกลับ
doYes=ใช่
doNo=ไม่ใช่
doContinue=ดำเนินการต่อ
doIgnore=ไม่สนใจ
doAccept=ยอมรับ
doDecline=ปฏิเสธ
doForgotPassword=ลืมรหัสผ่าน?
doClickHere=คลิกที่นี่
doImpersonate=สวมบทบาท
doTryAgain=ลองอีกครั้ง
doTryAnotherWay=ลองวิธีอื่น
doConfirmDelete=ยืนยันการลบ
errorDeletingAccount=เกิดข้อผิดพลาดขณะลบบัญชี
deletingAccountForbidden=คุณไม่มีสิทธิ์เพียงพอในการลบบัญชีของคุณเอง ติดต่อผู้ดูแลระบบ
kerberosNotConfigured=Kerberos ไม่ได้ถูกกำหนดค่า
kerberosNotConfiguredTitle=Kerberos ไม่ได้ถูกกำหนดค่า
bypassKerberosDetail=คุณไม่ได้เข้าสู่ระบบด้วย Kerberos หรือเบราว์เซอร์ของคุณไม่ได้ถูกตั้งค่าให้เข้าสู่ระบบด้วย Kerberos กรุณาคลิกที่ "ดำเนินการต่อ" เพื่อเข้าสู่ระบบโดยวิธีอื่น
kerberosNotSetUp=Kerberos ไม่ได้ถูกตั้งค่า คุณไม่สามารถเข้าสู่ระบบได้
registerTitle=ลงทะเบียน
loginAccountTitle=เข้าสู่ระบบด้วยบัญชีของคุณ
loginTitle=เข้าสู่ระบบไปยัง {0}
loginTitleHtml={0}
impersonateTitle={0} สวมบทบาทผู้ใช้งาน
impersonateTitleHtml=<strong>{0}</strong> สวมบทบาทผู้ใช้งาน
realmChoice=Realm
unknownUser=ผู้ใช้งานที่ไม่รู้จัก
loginTotpTitle=การตั้งค่า Mobile Authenticator
loginProfileTitle=ปรับปรุงข้อมูลบัญชีผู้ใช้งาน
loginIdpReviewProfileTitle=ปรับปรุงข้อมูลบัญชีผู้ใช้งาน
loginTimeout=การพยายามเข้าสู่ระบบของคุณหมดเวลา การเข้าสู่ระบบจะเริ่มต้นใหม่
reauthenticate=กรุณาพิสูจน์ตัวจริงอีกครั้งเพื่อดำเนินการต่อ
oauthGrantTitle=ให้อนุญาต {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=ตรวจสอบว่าคุณไว้วางใจ {0} โดยการทำความเข้าใจว่า {0} จะจัดการข้อมูลของคุณอย่างไร
oauthGrantReview=คุณสามารถทบทวน
oauthGrantTos=ข้อตกลงการใช้งาน
oauthGrantPolicy=นโยบายคุ้มครองข้อมูลส่วนบุคคล
errorTitle=เราขอโทษ...
errorTitleHtml=เรา<strong>ขอโทษ</strong> ...
emailVerifyTitle=การยืนยันอีเมล
emailForgotTitle=ลืมรหัสผ่านของคุณ?
updateEmailTitle=ปรับปรุงอีเมล
emailUpdateConfirmationSentTitle=ส่งอีเมลยืนยันแล้ว
emailUpdateConfirmationSent=อีเมลยืนยันถูกส่งไปยัง {0} คุณต้องปฏิบัติตามคำแนะนำในอีเมลเพื่อสิ้นสุดการปรับปรุงอีเมล
emailUpdatedTitle=อีเมลถูกปรับปรุงแล้ว
emailUpdated=อีเมลของบัญชีถูกปรับปรุงเป็น {0} เรียบร้อยแล้ว
updatePasswordTitle=ปรับปรุงรหัสผ่าน
codeSuccessTitle=รหัสสำเร็จ
codeErrorTitle=รหัสข้อผิดพลาด\: {0}
displayUnsupported=ไม่สนับสนุนการแสดงผลที่ร้องขอ
browserRequired=ต้องใช้เบราว์เซอร์ในการเข้าสู่ระบบ
browserContinue=ต้องใช้เบราว์เซอร์ในการเข้าสู่ระบบต่อ
browserContinuePrompt=เปิดเบราว์เซอร์และเข้าสู่ระบบต่อ? [y/n]:
browserContinueAnswer=y

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=ภายใน
unknown=ไม่ทราบ

termsTitle=ข้อกำหนดและเงื่อนไข
termsText=<p>ข้อกำหนดและเงื่อนไขที่จะกำหนด</p>
termsPlainText=ข้อกำหนดและเงื่อนไขที่จะกำหนด
termsAcceptanceRequired=คุณต้องยอมรับข้อกำหนดและเงื่อนไขของเรา
acceptTerms=ฉันยอมรับข้อกำหนดและเงื่อนไข

recaptchaFailed=Recaptcha ไม่ถูกต้อง
recaptchaNotConfigured=ต้องใช้ Recaptcha แต่ไม่ได้กำหนดค่า
consentDenied=การขอความยินยอมถูกปฏิเสธ

noAccount=ผู้ใช้งานใหม่?
username=ชื่อผู้ใช้งาน
usernameOrEmail=ชื่อผู้ใช้งานหรืออีเมล
firstName=ชื่อ
givenName=ชื่อ
fullName=ชื่อ-นามสกุล
lastName=นามสกุล
familyName=นามสกุล
email=อีเมล
password=รหัสผ่าน
passwordConfirm=ยืนยันรหัสผ่าน
passwordNew=รหัสผ่านใหม่
passwordNewConfirm=ยืนยันรหัสผ่านใหม่
rememberMe=จดจำฉันไว้
authenticatorCode=รหัสสำหรับใช้ครั้งเดียว
address=ที่อยู่
street=ถนน
locality=อำเภอ/เขต
region=จังหวัด
postal_code=รหัสไปรษณีย์
country=ประเทศ
emailVerified=อีเมลที่ยืนยันแล้ว
website=เว็บไซต์
phoneNumber=หมายเลขโทรศัพท์
phoneNumberVerified=หมายเลขโทรศัพท์ที่ยืนยันแล้ว
gender=เพศ
birthday=วันเกิด
zoneinfo=เขตเวลา
gssDelegationCredential=GSS Delegation Credential
logoutOtherSessions=ออกจากระบบจากอุปกรณ์อื่น

profileScopeConsentText=โปรไฟล์ผู้ใช้งาน
emailScopeConsentText=ที่อยู่อีเมล
addressScopeConsentText=ที่อยู่
phoneScopeConsentText=หมายเลขโทรศัพท์
offlineAccessScopeConsentText=การเข้าถึงแบบออฟไลน์
samlRoleListScopeConsentText=บทบาทของฉัน
rolesScopeConsentText=บทบาทผู้ใช้งาน

restartLoginTooltip=เริ่มการเข้าสู่ระบบใหม่

loginTotpIntro=คุณต้องตั้งค่าอุปกรณ์สำหรับสร้างรหัสแบบใช้ครั้งเดียวเพื่อเข้าถึงบัญชีนี้
loginTotpStep1=ติดตั้งแอปพลิเคชันใดแอปพลิเคชันหนึ่งจากรายการต่อไปนี้ลงในโทรศัพท์ของคุณ:
loginTotpStep2=เปิดแอปพลิเคชันและสแกนบาร์โค้ด:
loginTotpStep3=ใส่รหัสผ่านครั้งเดียวที่แอปพลิเคชันให้และคลิก "ส่ง" เพื่อสิ้นสุดการตั้งค่า
loginTotpStep3DeviceName=กำหนดชื่ออุปกรณ์เพื่อช่วยจัดการอุปกรณ์ OTP ของคุณ
loginTotpManualStep2=เปิดแอปพลิเคชันและใส่คีย์:
loginTotpManualStep3=ใช้การตั้งค่าต่อไปนี้หากแอปพลิเคชันสามารถระบุการตั้งค่าได้:
loginTotpUnableToScan=ไม่สามารถสแกนได้?
loginTotpScanBarcode=สแกนบาร์โค้ด?
loginCredential=Credential ข้อมูลประจำตัว
loginOtpOneTime=รหัสสำหรับใช้ครั้งเดียว
loginTotpType=ประเภท
loginTotpAlgorithm=ขั้นตอนวิธี
loginTotpDigits=หลัก
loginTotpInterval=ช่วงเวลา
loginTotpCounter=Counter
loginTotpDeviceName=ชื่ออุปกรณ์

loginTotp.totp=Time-based
loginTotp.hotp=Counter-based

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=เลือกวิธีการเข้าสู่ระบบ

oauthGrantRequest=คุณอนุญาตให้ใช้สิทธิ์ในการเข้าถึงเหล่านี้หรือไม่?
inResource=ใน

oauth2DeviceVerificationTitle=การเข้าสู่ระบบของอุปกรณ์
verifyOAuth2DeviceUserCode=ระบุรหัสที่ได้รับจากอุปกรณ์ของคุณและคลิก "ส่ง"
oauth2DeviceInvalidUserCodeMessage=รหัสไม่ถูกต้อง โปรดลองอีกครั้ง
oauth2DeviceExpiredUserCodeMessage=รหัสหมดอายุแล้ว โปรดกลับไปที่อุปกรณ์ของคุณและลองเชื่อมต่ออีกครั้ง
oauth2DeviceVerificationCompleteHeader=การเข้าสู่ระบบของอุปกรณ์สำเร็จ
oauth2DeviceVerificationCompleteMessage=คุณสามารถปิดหน้าต่างเบราว์เซอร์นี้และกลับไปที่อุปกรณ์ของคุณ
oauth2DeviceVerificationFailedHeader=การเข้าสู่ระบบของอุปกรณ์ล้มเหลว
oauth2DeviceVerificationFailedMessage=คุณสามารถปิดหน้าต่างเบราว์เซอร์นี้และกลับไปที่อุปกรณ์ของคุณและลองเชื่อมต่ออีกครั้ง
oauth2DeviceConsentDeniedMessage=การขอความยินยอมให้เชื่อมต่อกับอุปกรณ์ถูกปฏิเสธ
oauth2DeviceAuthorizationGrantDisabledMessage=ไคลเอนต์ไม่ได้รับการยินยอมให้เริ่ม OAuth 2.0 Device Authorization Grant การทำงานนี้ถูกปิดใช้งานสำหรับไคลเอนต์

emailVerifyInstruction1=ได้ส่งอีเมลที่มีคำแนะนำในการยืนยันที่อยู่อีเมลของคุณไปยังที่อยู่อีเมลของคุณ {0}
emailVerifyInstruction2=ไม่ได้รับรหัสยืนยันในอีเมลของคุณ?
emailVerifyInstruction3=เพื่อส่งอีเมลอีกครั้ง

emailLinkIdpTitle=ลิงก์ {0}
emailLinkIdp1=ได้ส่งอีเมลที่มีคำแนะนำในการเชื่อมโยงบัญชี {0} {1} กับ {2} บัญชีของคุณ
emailLinkIdp2=ไม่ได้รับรหัสยืนยันในอีเมลของคุณ?
emailLinkIdp3=เพื่อส่งอีเมลอีกครั้ง
emailLinkIdp4=หากคุณได้ยืนยันอีเมลในบราวเซอร์อื่นแล้ว
emailLinkIdp5=เพื่อดำเนินการต่อ

backToLogin=&laquo; กลับไปที่การเข้าสู่ระบบ

emailInstruction=ระบุชื่อผู้ใช้งานหรือที่อยู่อีเมลของคุณ เราจะส่งคำแนะนำในการสร้างรหัสผ่านใหม่ให้คุณ
emailInstructionUsername=ระบุชื่อผู้ใช้งานของคุณ เราจะส่งคำแนะนำในการสร้างรหัสผ่านใหม่ให้คุณ

copyCodeInstruction=โปรดคัดลอกและวางรหัสนี้ในแอปพลิเคชันของคุณ:

pageExpiredTitle=หน้านี้หมดเวลาแล้ว
pageExpiredMsg1=เพื่อเริ่มกระบวนการเข้าสู่ระบบอีกครั้ง
pageExpiredMsg2=เพื่อดำเนินการเข้าสู่ระบบต่อ

personalInfo=ข้อมูลส่วนตัว:
role_admin=ผู้ดูแลระบบ
role_realm-admin=ผู้ดูแลระบบ realm
role_create-realm=สร้าง realm
role_create-client=สร้างไคลเอนต์
role_view-realm=ดู realm
role_view-users=ดูผู้ใช้งาน
role_view-applications=ดูแอปพลิเคชัน
role_view-clients=ดูไคลเอนต์
role_view-events=ดูเหตุการณ์
role_view-identity-providers=ดูผู้ให้บริการตัวตน
role_manage-realm=จัดการ realm
role_manage-users=จัดการผู้ใช้งาน
role_manage-applications=จัดการแอปพลิเคชัน
role_manage-identity-providers=จัดการผู้ให้บริการตัวตน
role_manage-clients=จัดการไคลเอนต์
role_manage-events=จัดการเหตุการณ์
role_view-profile=ดูโปรไฟล์
role_manage-account=จัดการบัญชี
role_manage-account-links=จัดการการเชื่อมโยงบัญชี
role_read-token=อ่านโทเค็น
role_offline-access=การเข้าถึงแบบออฟไลน์
client_account=บัญชี
client_account-console=คอนโซลบัญชี
client_security-admin-console=คอนโซลผู้ดูแลระบบความปลอดภัย
client_admin-cli=CLI สำหรับผู้ดูแลระบบ
client_realm-management=การจัดการ realm
client_broker=ตัวแทน

requiredFields=ฟิลด์ที่ต้องระบุ

invalidUserMessage=ชื่อผู้ใช้งานหรือรหัสผ่านไม่ถูกต้อง
invalidUsernameMessage=ชื่อผู้ใช้งานไม่ถูกต้อง
invalidUsernameOrEmailMessage=ชื่อผู้ใช้งานหรืออีเมลไม่ถูกต้อง
invalidPasswordMessage=รหัสผ่านไม่ถูกต้อง
invalidEmailMessage=ที่อยู่อีเมลไม่ถูกต้อง
accountDisabledMessage=บัญชีถูกปิดใช้งาน ติดต่อผู้ดูแลระบบของคุณ
accountTemporarilyDisabledMessage=ชื่อผู้ใช้งานหรือรหัสผ่านไม่ถูกต้อง
accountPermanentlyDisabledMessage=ชื่อผู้ใช้งานหรือรหัสผ่านไม่ถูกต้อง
accountTemporarilyDisabledMessageTotp=รหัส Authenticator ไม่ถูกต้อง
accountPermanentlyDisabledMessageTotp=รหัส Authenticator ไม่ถูกต้อง
expiredCodeMessage=การเข้าสู่ระบบหมดเวลา กรุณาเข้าสู่ระบบอีกครั้ง
expiredActionMessage=การกระทำหมดเวลา กรุณาเข้าสู่ระบบในตอนนี้
expiredActionTokenNoSessionMessage=การกระทำหมดเวลา
expiredActionTokenSessionExistsMessage=การกระทำหมดเวลา กรุณาเริ่มต้นใหม่
sessionLimitExceeded=มีเซสชันมากเกินไป

missingFirstNameMessage=กรุณาระบุชื่อ
missingLastNameMessage=กรุณาระบุนามสกุล
missingEmailMessage=กรุณาระบุอีเมล
missingUsernameMessage=กรุณาระบุชื่อผู้ใช้งาน
missingPasswordMessage=กรุณาระบุรหัสผ่าน
missingTotpMessage=กรุณาระบุรหัสของ authenticator
missingTotpDeviceNameMessage=กรุณาระบุชื่ออุปกรณ์
notMatchPasswordMessage=รหัสผ่านไม่ตรงกัน

error-invalid-value=ค่าไม่ถูกต้อง
error-invalid-blank=กรุณาระบุค่า
error-empty=กรุณาระบุค่า
error-invalid-length=ความยาวต้องอยู่ระหว่าง {1} และ {2} อักษร
error-invalid-length-too-short=ความยาวขั้นต่ำคือ {1} อักษร
error-invalid-length-too-long=ความยาวสูงสุดคือ {2} อักษร
error-invalid-email=ที่อยู่อีเมลไม่ถูกต้อง
error-invalid-number=จำนวนไม่ถูกต้อง
error-number-out-of-range=จำนวนต้องอยู่ระหว่าง {1} และ {2}
error-number-out-of-range-too-small=จำนวนต้องมีค่าขั้นต่ำ {1}
error-number-out-of-range-too-big=จำนวนต้องมีค่าสูงสุด {2}
error-pattern-no-match=ค่าไม่ถูกต้อง
error-invalid-uri=URL ไม่ถูกต้อง
error-invalid-uri-scheme=scheme URL ไม่ถูกต้อง
error-invalid-uri-fragment=fragment URL ไม่ถูกต้อง
error-user-attribute-required=กรุณาระบุฟิลด์นี้
error-invalid-date=วันที่ไม่ถูกต้อง
error-user-attribute-read-only=ฟิลด์นี้เป็นแบบอ่านอย่างเดียว
error-username-invalid-character=ค่ามีอักขระที่ไม่ถูกต้อง
error-person-name-invalid-character=ค่ามีอักขระที่ไม่ถูกต้อง
error-reset-otp-missing-id=กรุณาเลือกการตั้งค่า OTP

invalidPasswordExistingMessage=รหัสผ่านเดิมไม่ถูกต้อง
invalidPasswordBlacklistedMessage=รหัสผ่านไม่ถูกต้อง: รหัสผ่านอยู่ในบัญชีดำ
invalidPasswordConfirmMessage=การยืนยันรหัสผ่านไม่ตรงกัน
invalidTotpMessage=รหัส Authenticator ไม่ถูกต้อง

usernameExistsMessage=มีชื่อผู้ใช้งานนี้อยู่แล้ว
emailExistsMessage=มีอีเมลอยู่แล้ว

federatedIdentityExistsMessage=ผู้ใช้งานที่มี {0} {1} มีอยู่แล้ว กรุณาเข้าสู่ระบบจัดการบัญชีเพื่อเชื่อมโยงบัญชี
federatedIdentityUnavailableMessage=ผู้ใช้งาน {0} ที่ได้รับการตรวจสอบจากผู้ให้บริการตัวตน {1} ไม่มีอยู่ กรุณาติดต่อผู้ดูแลระบบของคุณ
federatedIdentityUnmatchedEssentialClaimMessage=ไอดีโทเค็นที่ออกโดยผู้ให้บริการตัวตนไม่ตรงกับการอ้างสิทธิ์ที่จำเป็นที่กำหนดไว้ กรุณาติดต่อผู้ดูแลระบบของคุณ

confirmLinkIdpTitle=มีบัญชีอยู่แล้ว
federatedIdentityConfirmLinkMessage=ผู้ใช้งานที่มี {0} {1} มีอยู่แล้ว คุณต้องการดำเนินการอย่างไรต่อ?
federatedIdentityConfirmReauthenticateMessage=ตรวจสอบตัวตนเพื่อเชื่อมโยงบัญชีของคุณกับ {0}
nestedFirstBrokerFlowMessage=ผู้ใช้งาน {0} {1} ไม่ได้เชื่อมโยงกับผู้ใช้งานที่รู้จัก
confirmLinkIdpReviewProfile=รีวิวโปรไฟล์
confirmLinkIdpContinue=เพิ่มเข้าไปในบัญชีที่มีอยู่

configureTotpMessage=คุณต้องตั้งค่า Mobile Authenticator เพื่อเปิดใช้งานบัญชีของคุณ
configureBackupCodesMessage=คุณต้องตั้งค่ารหัสสำรองเพื่อเปิดใช้งานบัญชีของคุณ
updateProfileMessage=คุณต้องปรับปรุงโปรไฟล์ผู้ใช้งานเพื่อเปิดใช้งานบัญชีของคุณ
updatePasswordMessage=คุณต้องเปลี่ยนรหัสผ่านเพื่อเปิดใช้งานบัญชีของคุณ
updateEmailMessage=คุณต้องปรับปรุงที่อยู่อีเมลเพื่อเปิดใช้งานบัญชีของคุณ
resetPasswordMessage=คุณต้องเปลี่ยนรหัสผ่าน
verifyEmailMessage=คุณต้องยืนยันที่อยู่อีเมลเพื่อเปิดใช้งานบัญชีของคุณ
linkIdpMessage=คุณจำเป็นต้องยืนยันที่อยู่อีเมลเพื่อเชื่อมโยงบัญชีของคุณกับ {0}

emailSentMessage=คุณจะได้รับอีเมลในไม่ช้านี้พร้อมคำแนะนำเพิ่มเติม
emailSendErrorMessage=การส่งอีเมลล้มเหลว โปรดลองอีกครั้งในภายหลัง

accountUpdatedMessage=บัญชีของคุณได้รับการปรับปรุงแล้ว
accountPasswordUpdatedMessage=รหัสผ่านของคุณได้รับการปรับปรุงแล้ว

delegationCompleteHeader=เข้าสู่ระบบสำเร็จ
delegationCompleteMessage=คุณสามารถปิดหน้าต่างเบราว์เซอร์นี้และกลับไปที่แอปพลิเคชันคอนโซลของคุณ.
delegationFailedHeader=เข้าสู่ระบบล้มเหลว
delegationFailedMessage=คุณสามารถปิดหน้าต่างเบราว์เซอร์นี้และกลับไปที่แอปพลิเคชันคอนโซลของคุณและลองเข้าสู่ระบบอีกครั้ง.

noAccessMessage=ไม่มีการเข้าถึง

invalidPasswordMinLengthMessage=รหัสผ่านไม่ถูกต้อง: ความยาวขั้นต่ำ {0} อักษร
invalidPasswordMaxLengthMessage=รหัสผ่านไม่ถูกต้อง: ความยาวสูงสุด {0} อักษร
invalidPasswordMinDigitsMessage=รหัสผ่านไม่ถูกต้อง: ต้องมีอย่างน้อย {0} หลัก
invalidPasswordMinLowerCaseCharsMessage=รหัสผ่านไม่ถูกต้อง: ต้องมีตัวอักษรพิมพ์เล็กอย่างน้อย {0} อักษร
invalidPasswordMinUpperCaseCharsMessage=รหัสผ่านไม่ถูกต้อง: ต้องมีตัวอักษรพิมพ์ใหญ่อย่างน้อย {0} อักษร
invalidPasswordMinSpecialCharsMessage=รหัสผ่านไม่ถูกต้อง: ต้องมีอักขระพิเศษอย่างน้อย {0} อักษร
invalidPasswordNotUsernameMessage=รหัสผ่านไม่ถูกต้อง: ต้องไม่เหมือนกับชื่อผู้ใช้งาน
invalidPasswordNotEmailMessage=รหัสผ่านไม่ถูกต้อง: ต้องไม่เหมือนกับอีเมล
invalidPasswordRegexPatternMessage=รหัสผ่านไม่ถูกต้อง: ไม่ตรงกับรูปแบบ regex
invalidPasswordHistoryMessage=รหัสผ่านไม่ถูกต้อง: ต้องไม่เหมือนกับ {0} รหัสผ่านล่าสุด
invalidPasswordGenericMessage=รหัสผ่านไม่ถูกต้อง: รหัสผ่านใหม่ไม่เป็นไปตามนโยบายการตั้งรหัสผ่าน

failedToProcessResponseMessage=การประมวลผลการตอบสนองล้มเหลว
httpsRequiredMessage=จำเป็นต้องใช้ HTTPS
realmNotEnabledMessage=ไม่ได้เปิดใช้ realm
invalidRequestMessage=คำขอไม่ถูกต้อง
successLogout=คุณออกจากระบบแล้ว
failedLogout=การออกจากระบบล้มเหลว
unknownLoginRequesterMessage=ผู้ร้องขอเข้าสู่ระบบที่ไม่รู้จัก
loginRequesterNotEnabledMessage=ผู้ร้องขอเข้าสู่ระบบที่ไม่ได้เปิดใช้งาน
bearerOnlyMessage=แอปพลิเคชันที่ใช้ Bearer-only ไม่ได้รับอนุญาตให้เริ่มการเข้าสู่ระบบผ่านเบราว์เซอร์
standardFlowDisabledMessage=ไคลเอนต์ไม่ได้รับอนุญาตให้เริ่มการเข้าสู่ระบบผ่านเบราว์เซอร์ด้วย response_type ที่ระบุ Standard flow ถูกระงับสำหรับไคลเอนต์
implicitFlowDisabledMessage=ไคลเอนต์ไม่ได้รับอนุญาตให้เริ่มการเข้าสู่ระบบผ่านเบราว์เซอร์ด้วย response_type ที่ระบุ Implicit flow ถูกระงับสำหรับไคลเอนต์
invalidRedirectUriMessage=Uri redirect ไม่ถูกต้อง
unsupportedNameIdFormatMessage=รูปแบบ NameIDFormat ที่ไม่รองรับ
invalidRequesterMessage=ผู้ร้องขอไม่ถูกต้อง
registrationNotAllowedMessage=ไม่อนุญาตให้ลงทะเบียน
resetCredentialNotAllowedMessage=ไม่อนุญาตให้รีเซ็ต Credential

permissionNotApprovedMessage=ไม่ได้รับการอนุมัติสิทธิ์
noRelayStateInResponseMessage=ไม่มีสถานะ relay ในการตอบสนองจากผู้ให้บริการตัวตน
insufficientPermissionMessage=สิทธิ์ไม่เพียงพอในการเชื่อมโยงตัวตน
couldNotProceedWithAuthenticationRequestMessage=ไม่สามารถดำเนินการต่อเพื่อส่งคำขอพิสูจน์ตัวจริงไปยังผู้ให้บริการตัวตน
couldNotObtainTokenMessage=ไม่สามารถรับโทเค็นจากผู้ให้บริการตัวตน
unexpectedErrorRetrievingTokenMessage=เกิดข้อผิดพลาดที่ไม่คาดคิดขณะรับโทเค็นจากผู้ให้บริการตัวตน
unexpectedErrorHandlingResponseMessage=เกิดข้อผิดพลาดที่ไม่คาดคิดขณะจัดการการตอบสนองจากผู้ให้บริการตัวตน
identityProviderAuthenticationFailedMessage=การตรวจสอบล้มเหลว ไม่สามารถตรวจสอบกับผู้ให้บริการตัวตน
couldNotSendAuthenticationRequestMessage=ไม่สามารถส่งคำขอพิสูจน์ตัวจริงไปยังผู้ให้บริการตัวตน
unexpectedErrorHandlingRequestMessage=เกิดข้อผิดพลาดที่ไม่คาดคิดขณะจัดการคำขอพิสูจน์ตัวจริงไปยังผู้ให้บริการตัวตน
invalidAccessCodeMessage=Access code ไม่ถูกต้อง
sessionNotActiveMessage=ไม่ได้ใช้งานเซสชัน
invalidCodeMessage=เกิดข้อผิดพลาด กรุณาเข้าสู่ระบบผ่านแอปพลิเคชันของคุณอีกครั้ง
cookieNotFoundMessage=ไม่พบคุกกี้ โปรดตรวจสอบให้แน่ใจว่าคุณได้เปิดใช้งานคุกกี้ในเบราว์เซอร์แล้ว
insufficientLevelOfAuthentication=ระดับการพิสูจน์ตัวจริงที่ร้องขอมีไม่เพียงพอ
identityProviderUnexpectedErrorMessage=เกิดข้อผิดพลาดที่ไม่คาดคิดขณะพิสูจน์ตัวจริงกับผู้ให้บริการตัวตน
identityProviderMissingStateMessage=ไม่พบพารามิเตอร์ state จากการตอบสนองของผู้ให้บริการตัวตน
identityProviderInvalidResponseMessage=การตอบสนองจากผู้ให้บริการตัวตนไม่ถูกต้อง
identityProviderInvalidSignatureMessage=ลายเซ็นในการตอบสนองจากผู้ให้บริการตัวตนไม่ถูกต้อง
identityProviderNotFoundMessage=ไม่พบผู้ให้บริการตัวตนด้วยตัวระบุนี้
identityProviderLinkSuccess=คุณได้ยืนยันที่อยู่อีเมลของคุณเรียบร้อยแล้ว กรุณากลับไปที่เบราว์เซอร์เดิมของคุณและดำเนินการเข้าสู่ระบบที่นั่น
staleCodeMessage=หน้านี้ไม่ถูกต้องอีกต่อไป กรุณากลับไปที่แอปพลิเคชันของคุณและเข้าสู่ระบบอีกครั้ง
realmSupportsNoCredentialsMessage=realm ไม่สนับสนุนประเภท credential ใด ๆ
credentialSetupRequired=ไม่สามารถเข้าสู่ระบบ จำเป็นต้องตั้งค่าข้อมูลประจำตัว
identityProviderNotUniqueMessage=realm สนับสนุนผู้ให้บริการตัวตนหลายแหล่ง ไม่สามารถระบุได้ว่าควรพิสูจน์ตัวจริงผ่านผู้ให้บริการตัวตนใด
emailVerifiedMessage=ยืนยันที่อยู่อีเมลของคุณแล้ว
staleEmailVerificationLink=ลิงก์ที่คุณคลิกเป็นลิงก์ที่เก่าและไม่ถูกต้อง บางทีคุณอาจจะยืนยันอีเมลของคุณแล้ว
identityProviderAlreadyLinkedMessage=Federated identity ที่ส่งคืนมาจากผู้ให้บริการ {0} มีการเชื่อมโยงกับผู้ใช้งานอื่นแล้ว
confirmAccountLinking=ยืนยันการเชื่อมโยงบัญชี {0} ของผู้ให้บริการตัวตน {1} กับบัญชีของคุณ
confirmEmailAddressVerification=ยืนยันความถูกต้องของที่อยู่อีเมล {0}
confirmExecutionOfActions=ดำเนินการต่อไปนี้

backToApplication=&laquo; กลับไปที่แอปพลิเคชัน
missingParameterMessage=ไม่มีพารามิเตอร์\: {0}
clientNotFoundMessage=ไม่พบไคลเอนต์
clientDisabledMessage=ไคลเอนต์ถูกปิดใช้งาน
invalidParameterMessage=พารามิเตอร์ไม่ถูกต้อง\: {0}
alreadyLoggedIn=คุณเข้าสู่ระบบแล้ว
differentUserAuthenticated=คุณได้พิสูจน์ตัวจริงเป็นผู้ใช้งานอื่น ''{0}'' ในเซสชันนี้แล้ว กรุณาออกจากระบบก่อน
brokerLinkingSessionExpired=ได้ขอการเชื่อมโยงบัญชีของโบรกเกอร์ แต่เซสชันปัจจุบันไม่ถูกต้องแล้ว
proceedWithAction=&raquo; คลิกที่นี่เพื่อดำเนินการ
acrNotFulfilled=คุณสมบัติไม่เป็นไปตามข้อกำหนดในการพิสูจน์ตัวจริง

requiredAction.CONFIGURE_TOTP=ตั้งค่า OTP
requiredAction.terms_and_conditions=ข้อกำหนดและเงื่อนไข
requiredAction.UPDATE_PASSWORD=ปรับปรุงรหัสผ่าน
requiredAction.UPDATE_PROFILE=ปรับปรุงโปรไฟล์
requiredAction.VERIFY_EMAIL=ยืนยันอีเมล
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=สร้างรหัสกู้คืน
requiredAction.webauthn-register-passwordless=Webauthn Register Passwordless

invalidTokenRequiredActions=การกระทำที่จำเป็นที่อยู่ในลิงก์ไม่ถูกต้อง

doX509Login=คุณจะเข้าสู่ระบบในฐานะ\:
clientCertificate=ใบรับรองไคลเอนต์ X509\:
noCertificate=[ไม่มีใบรับรอง]


pageNotFound=ไม่พบหน้า
internalServerError=เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์

console-username=ชื่อผู้ใช้งาน:
console-password=รหัสผ่าน:
console-otp=รหัสสำหรับใช้ครั้งเดียว:
console-new-password=รหัสผ่านใหม่:
console-confirm-password=ยืนยันรหัสผ่าน:
console-update-password=จำเป็นต้องปรับปรุงรหัสผ่านของคุณ
console-verify-email=คุณจำเป็นต้องยืนยันที่อยู่อีเมลของคุณ เราได้ส่งอีเมลพร้อมรหัสยืนยันไปที่ {0} กรุณาระบุรหัสที่ได้รับลงในช่องว่างด้านล่าง
console-email-code=รหัสอีเมล:
console-accept-terms=ยอมรับข้อกำหนด? [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=ข้อมูลผู้ใช้งาน
openshift.scope.user_check-access=ข้อมูลการเข้าถึงของผู้ใช้งาน
openshift.scope.user_full=การเข้าถึงแบบเต็มรูปแบบ
openshift.scope.list-projects=รายการโปรเจค

# SAML authentication
saml.post-form.title=การเปลี่ยนเส้นทางการพิสูจน์ตัวจริง
saml.post-form.message=กำลังเปลี่ยนเส้นทาง โปรดรอ
saml.post-form.js-disabled=JavaScript ถูกปิดใช้งาน เราขอแนะนำให้คุณเปิดใช้งาน คลิกที่ปุ่มด้านล่างเพื่อดำเนินการต่อ
saml.artifactResolutionServiceInvalidResponse=ไม่สามารถ resolve artifact ได้

#authenticators
otp-display-name=แอปพลิเคชัน Authenticator
otp-help-text=ระบุรหัสยืนยันจากแอปพลิเคชัน Authenticator
otp-reset-description=การตั้งค่า OTP ใดที่ควรถูกลบ?
password-display-name=รหัสผ่าน
password-help-text=ลงชื่อเข้าใช้โดยการระบุรหัสผ่าน
auth-username-form-display-name=ชื่อผู้ใช้งาน
auth-username-form-help-text=เริ่มลงชื่อเข้าใช้โดยการระบุชื่อผู้ใช้งาน
auth-username-password-form-display-name=ชื่อผู้ใช้งานและรหัสผ่าน
auth-username-password-form-help-text=ลงชื่อเข้าใช้โดยการระบุชื่อผู้ใช้งานและรหัสผ่าน

# Recovery Codes
auth-recovery-authn-code-form-display-name=รหัสพิสูจน์ตัวจริงเพื่อการกู้คืน
auth-recovery-authn-code-form-help-text=ระบุรหัสพิสูจน์ตัวจริงเพื่อการกู้คืนจากรายการที่สร้างไว้ก่อนหน้า
auth-recovery-code-info-message=กรุณาระบุรหัสกู้คืนที่ระบุ
auth-recovery-code-prompt=รหัสกู้คืน #{0}
auth-recovery-code-header=เข้าสู่ระบบด้วยรหัสพิสูจน์ตัวจริงเพื่อการกู้คืน
recovery-codes-error-invalid=รหัสพิสูจน์ตัวจริงเพื่อการกู้คืนไม่ถูกต้อง
recovery-code-config-header=พิสูจน์ตัวจริงเพื่อการกู้คืน
recovery-code-config-warning-title=รหัสกู้คืนเหล่านี้จะไม่ปรากฏอีกหลังจากที่คุณออกจากหน้านี้ไปแล้ว
recovery-code-config-warning-message=ตรวจสอบให้แน่ใจว่าคุณได้พิมพ์ ดาวน์โหลด หรือคัดลอกรหัส เก็บไว้ในที่ที่ปลอดภัย หากคุณยกเลิกการตั้งค่า รหัสกู้คืนเหล่านี้จะถูกลบออกจากบัญชีของคุณ
recovery-codes-print=พิมพ์
recovery-codes-download=ดาวน์โหลด
recovery-codes-copy=คัดลอก
recovery-codes-copied=คัดลอกแล้ว
recovery-codes-confirmation-message=ฉันได้บันทึกรหัสเหล่านี้ไว้ในที่ที่ปลอดภัยแล้ว
recovery-codes-action-complete=การตั้งค่าเสร็จสมบูรณ์
recovery-codes-action-cancel=ยกเลิกการตั้งค่า
recovery-codes-download-file-header=เก็บรหัสกู้คืนเหล่านี้ไว้ในที่ที่ปลอดภัย
recovery-codes-download-file-description=รหัสกู้คืนคือรหัสผ่านที่ใช้ได้เพียงครั้งเดียวที่จะช่วยให้คุณสามารถเข้าสู่ระบบได้หากคุณไม่สามารถเข้าถึง authenticator ของคุณ
recovery-codes-download-file-date= รหัสเหล่านี้ถูกสร้างขึ้นเมื่อ
recovery-codes-label-default=รหัสกู้คืน

# WebAuthn
webauthn-display-name=คีย์รักษาความปลอดภัย
webauthn-help-text=ใช้คีย์รักษาความปลอดภัยของคุณเพื่อเข้าสู่ระบบ
webauthn-passwordless-display-name=คีย์รักษาความปลอดภัย
webauthn-passwordless-help-text=ใช้คีย์รักษาความปลอดภัยสำหรับการเข้าสู่ระบบโดยไม่ต้องใช้รหัสผ่าน
webauthn-login-title=การเข้าสู่ระบบด้วยคีย์รักษาความปลอดภัย
webauthn-registration-title=การลงทะเบียนคีย์รักษาความปลอดภัย
webauthn-available-authenticators=คีย์รักษาความปลอดภัยที่มีอยู่
webauthn-unsupported-browser-text=เว็บเบราว์เซอร์นี้ไม่สนับสนุน WebAuthn ลองใช้เบราว์เซอร์อื่นหรือติดต่อผู้ดูแลระบบของคุณ
webauthn-doAuthenticate=เข้าสู่ระบบด้วยคีย์รักษาความปลอดภัย
webauthn-createdAt-label=สร้างแล้ว

# WebAuthn Error
webauthn-error-title=ข้อผิดพลาดของคีย์รักษาความปลอดภัย
webauthn-error-registration=การลงทะเบียนคีย์รักษาความปลอดภัยของคุณไม่สำเร็จ<br/> {0}
webauthn-error-api-get=การพิสูจน์ตัวจริงโดยใช้คีย์รักษาความปลอดภัยไม่สำเร็จ<br/> {0}
webauthn-error-different-user=ผู้ใช้งานที่พิสูจน์ตัวจริงรายแรกไม่ใช่รายที่ได้รับการพิสูจน์ตัวจริงจากคีย์รักษาความปลอดภัย
webauthn-error-auth-verification=ผลการพิสูจน์ตัวจริงของคีย์รักษาความปลอดภัยไม่ถูกต้อง<br/> {0}
webauthn-error-register-verification=ผลการลงทะเบียนของคีย์รักษาความปลอดภัยไม่ถูกต้อง<br/> {0}
webauthn-error-user-not-found=ผู้ใช้งานที่ไม่รู้จักได้รับการพิสูจน์ตัวจริงจากคีย์รักษาความปลอดภัย

# Identity provider
identity-provider-redirector=เชื่อมต่อกับผู้ให้บริการตัวตนอื่น
identity-provider-login-label=หรือเข้าสู่ระบบโดยใช้
idp-email-verification-display-name=การทวนสอบอีเมล
idp-email-verification-help-text=เชื่อมโยงบัญชีของคุณโดยการทวนสอบอีเมลของคุณ
idp-username-password-form-display-name=ชื่อผู้ใช้งานและรหัสผ่าน
idp-username-password-form-help-text=เชื่อมโยงบัญชีของคุณโดยเข้าสู่ระบบ

finalDeletionConfirmation=หากคุณลบบัญชีของคุณ จะไม่สามารถกู้คืนได้ หากต้องการเก็บบัญชีของคุณ ให้คลิกยกเลิก
irreversibleAction=การกระทำนี้ไม่สามารถย้อนกลับได้
deleteAccountConfirm=ยืนยันการลบบัญชี

deletingImplies=การลบบัญชีของคุณหมายถึง:
errasingData=การลบข้อมูลทั้งหมดของคุณ
loggingOutImmediately=การออกจากระบบในทันที
accountUnusable=ไม่สามารถใช้แอปพลิเคชันโดยใช้บัญชีนี้ได้
userDeletedSuccessfully=ลบผู้ใช้งานสำเร็จแล้ว

access-denied=ปฏิเสธการเข้าถึง
access-denied-when-idp-auth=การเข้าถึงถูกปฏิเสธเมื่อพิสูจน์ตัวจริงด้วย {0}

frontchannel-logout.title=กำลังออกจากระบบ
frontchannel-logout.message=คุณกำลังออกจากระบบจากแอปพลิเคชันต่อไปนี้
logoutConfirmTitle=กำลังออกจากระบบ
logoutConfirmHeader=คุณต้องการออกจากระบบหรือไม่?
doLogout=ออกจากระบบ

readOnlyUsernameMessage=คุณไม่สามารถปรับปรุงชื่อผู้ใช้งานของคุณเนื่องจากเป็นข้อมูลแบบอ่านอย่างเดียว
