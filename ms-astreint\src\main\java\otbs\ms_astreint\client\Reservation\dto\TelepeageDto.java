package otbs.ms_astreint.client.reservation.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * DTO pour représenter un télépeage provenant du microservice ms-reservation.
 * Cette classe contient les informations nécessaires pour représenter un télépeage
 * dans le contexte du microservice ms-astreinte.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TelepeageDto {
    private Long telepeageId;
    private String numeroBadge;
    private double solde;
    private LocalDateTime dateDerniereRecharge;
    private String nomDetenteur;
    private String immatriculationVehicule;
    private LocalDateTime dateDebutDisponibilite;
}
