:root {
  --accent-color: #ff5722;
  --primary-color: #1976d2;
  --background-color: #ffffff;
  --text-color: #333333;
  --error-color: #d32f2f;
  --border-color: #e0e0e0;
  --table-header-bg: #f4f5f7;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--accent-color);
  color: #fff;
  border-radius: 8px 8px 0 0;
  width: 100%;
  box-sizing: border-box;
}

.dialog-header h2 {
  margin: 0;
  font-size: 22px;
}

.reservation-form {
  padding: 24px;
  background-color: var(--background-color);
  border-radius: 0 0 12px 12px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  overflow-y: auto;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.priority-help-text {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--text-color);

  &.error-text {
    color: var(--error-color);
    font-weight: 500;
  }
}

.table-container {
  max-height: 300px;
  overflow-y: auto;
  border-radius: 6px;
  background: var(--background-color);
}

.badge-table {
  width: 100%;
  border-collapse: collapse;
}

.badge-table th,
.badge-table td {
  padding: 12px;
  text-align: center;
  font-size: 14px;
  color: var(--text-color);
}

.badge-table th {
  background: var(--table-header-bg);
  font-weight: 600;
  color: #444444;
  border-bottom: 1px solid var(--border-color);
  top: 0;
  z-index: 1;
}

.badge-table td {
  border-bottom: 1px solid var(--border-color);
}

.badge-table tr:last-child td {
  border-bottom: none;
}

.badge-table .checkbox-column {
  width: 60px;
  text-align: center;
}

.badge-row {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

// Styles pour l'option "Aucun badge"
.no-badge-option {
  margin: 12px 0;
  padding: 12px;
  background-color: #f9fafb;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }

  mat-checkbox {
    font-weight: 500;
    color: var(--text-color);
  }
}

// Style pour les badges sélectionnés
.selected-badge {
  background-color: #e3f2fd !important;
  border-left: 4px solid var(--primary-color);
}

// Responsive
@media (max-width: 768px) {
  .dialog-header {
    padding: 12px 16px;

    h2 {
      font-size: 18px;
    }
  }

  .reservation-form {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      width: 100%;
    }
  }

  .badge-table th,
  .badge-table td {
    padding: 8px;
    font-size: 12px;
  }
}
