spring.application.name=gateway-server

# Disable Spring Cloud Config
spring.cloud.config.enabled=false
spring.cloud.config.import-check.enabled=false

# Server port
server.port=9100

# Eureka Client Configuration
eureka.client.service-url.defaultZone=http://localhost:9102/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true

# Spring Cloud Gateway Configuration
spring.cloud.gateway.discovery.locator.enabled=true
spring.cloud.gateway.discovery.locator.lower-case-service-id=true

# CORS Configuration
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-origins=*
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-methods=*
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-headers=*

# Logging configuration
logging.level.root=INFO
logging.level.org.springframework.cloud.gateway=DEBUG
logging.level.com.netflix.eureka=DEBUG
logging.level.com.netflix.discovery=DEBUG
