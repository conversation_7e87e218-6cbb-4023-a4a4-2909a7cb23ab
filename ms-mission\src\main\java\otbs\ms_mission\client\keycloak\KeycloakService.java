package otbs.ms_mission.client.keycloak;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import otbs.ms_mission.config.CacheConfig;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * Service pour interagir avec Keycloak.
 * Utilise le Keycloak Admin Client pour récupérer les données utilisateur.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KeycloakService {

    private final Keycloak keycloak;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUri;

    /**
     * Obtient le nom du realm à partir de l'URI de l'émetteur.
     *
     * @return Le nom du realm
     */
    private String getRealm() {
        return issuerUri.substring(issuerUri.lastIndexOf("/") + 1);
    }

    /**
     * Récupère tous les utilisateurs depuis Keycloak.
     *
     * @return Liste des utilisateurs
     */
    @Cacheable(CacheConfig.USERS_CACHE)
    public List<UserDTO> getAllUsers() {
        log.debug("Récupération de tous les utilisateurs depuis Keycloak");

        try {
            String realm = getRealm();
            log.debug("Using realm: {}", realm);

            UsersResource usersResource = keycloak.realm(realm).users();
            List<UserRepresentation> users = usersResource.list();

            log.debug("Retrieved {} users from Keycloak", users.size());

            return users.stream()
                    .map(user -> mapToUserDTOWithRoles(user, realm))
                    .toList();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des utilisateurs depuis Keycloak: {}", e.getMessage(), e);
            log.warn("Retour en mode dégradé avec des utilisateurs de test");
            return createMockUsers();
        }
    }

    /**
     * Récupère un utilisateur par son ID.
     *
     * @param id L'ID de l'utilisateur
     * @return L'utilisateur correspondant
     */
    @Cacheable(value = CacheConfig.USERS_CACHE, key = "#id")
    public Optional<UserDTO> getUserById(String id) {
        log.debug("Récupération de l'utilisateur avec l'ID: {} depuis Keycloak", id);

        try {
            String realm = getRealm();
            UserResource userResource = keycloak.realm(realm).users().get(id);
            UserRepresentation userRepresentation = userResource.toRepresentation();

            if (userRepresentation != null) {
                log.debug("User found: {}", userRepresentation.getUsername());
                return Optional.of(mapToUserDTOWithRoles(userRepresentation, realm));
            } else {
                log.debug("User not found with ID: {}", id);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Erreur lors de la récupération de l'utilisateur avec l'ID: {} depuis Keycloak: {}", id, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * Récupère les utilisateurs pour le sélecteur (ID, nom complet, email).
     *
     * @return Liste des utilisateurs pour le sélecteur
     */
    @Cacheable(CacheConfig.USER_SELECTORS_CACHE)
    public List<UserSelectorOptionDTO> getUsersForSelector() {
        log.debug("Récupération des utilisateurs pour le sélecteur depuis Keycloak");

        return getAllUsers().stream()
                .filter(UserDTO::isEnabled)  // Uniquement les utilisateurs actifs
                .map(user -> UserSelectorOptionDTO.builder()
                        .id(user.getId())
                        .fullName(user.getFullName())
                        .email(user.getEmail())
                        .build())
                .toList();
    }

    /**
     * Recherche des utilisateurs par nom.
     * Recherche dans firstName, lastName et username.
     *
     * @param name Le nom à rechercher
     * @return Liste des utilisateurs correspondants
     */
    @Cacheable(value = CacheConfig.USERS_CACHE, key = "'search_name_' + #name")
    public List<UserDTO> searchUsersByName(String name) {
        log.debug("Recherche d'utilisateurs par nom: {} depuis Keycloak", name);

        try {
            UsersResource usersResource = keycloak.realm(getRealm()).users();

            // Recherche par username
            List<UserRepresentation> usersByUsername = usersResource.search(name);

            // Recherche par firstName
            List<UserRepresentation> usersByFirstName = usersResource.searchByFirstName(name, true);

            // Recherche par lastName
            List<UserRepresentation> usersByLastName = usersResource.searchByLastName(name, true);

            // Combinaison et déduplication des résultats
            String realm = getRealm();
            return Stream.of(usersByUsername, usersByFirstName, usersByLastName)
                    .flatMap(List::stream)
                    .distinct()
                    .map(user -> mapToUserDTOWithRoles(user, realm))
                    .toList();

        } catch (Exception e) {
            log.error("Erreur lors de la recherche d'utilisateurs par nom: {} depuis Keycloak", name, e);

            // Fallback: recherche locale dans le cache
            return getAllUsers().stream()
                    .filter(user -> matchesName(user, name))
                    .toList();
        }
    }

    /**
     * Recherche des utilisateurs par email.
     *
     * @param email L'email à rechercher
     * @return Liste des utilisateurs correspondants
     */
    @Cacheable(value = CacheConfig.USERS_CACHE, key = "'search_email_' + #email")
    public List<UserDTO> searchUsersByEmail(String email) {
        log.debug("Recherche d'utilisateurs par email: {} depuis Keycloak", email);

        try {
            UsersResource usersResource = keycloak.realm(getRealm()).users();

            // Recherche par email
            List<UserRepresentation> usersByEmail = usersResource.searchByEmail(email, true);

            String realm = getRealm();
            return usersByEmail.stream()
                    .map(user -> mapToUserDTOWithRoles(user, realm))
                    .toList();

        } catch (Exception e) {
            log.error("Erreur lors de la recherche d'utilisateurs par email: {} depuis Keycloak", email, e);

            // Fallback: recherche locale dans le cache
            return getAllUsers().stream()
                    .filter(user -> user.getEmail() != null &&
                                  user.getEmail().toLowerCase().contains(email.toLowerCase()))
                    .toList();
        }
    }

    /**
     * Vérifie si un utilisateur correspond au nom recherché.
     *
     * @param user L'utilisateur à vérifier
     * @param name Le nom recherché
     * @return true si l'utilisateur correspond, false sinon
     */
    private boolean matchesName(UserDTO user, String name) {
        String searchTerm = name.toLowerCase();

        return (user.getUsername() != null && user.getUsername().toLowerCase().contains(searchTerm)) ||
               (user.getFirstName() != null && user.getFirstName().toLowerCase().contains(searchTerm)) ||
               (user.getLastName() != null && user.getLastName().toLowerCase().contains(searchTerm)) ||
               (user.getFullName() != null && user.getFullName().toLowerCase().contains(searchTerm));
    }

    /**
     * Vérifie si un utilisateur existe.
     *
     * @param id L'ID de l'utilisateur
     * @return true si l'utilisateur existe, false sinon
     */
    public boolean userExists(String id) {
        return getUserById(id).isPresent();
    }

    /**
     * Crée des utilisateurs de test en mode dégradé.
     *
     * @return Liste d'utilisateurs de test
     */
    private List<UserDTO> createMockUsers() {
        return Arrays.asList(
                UserDTO.builder()
                        .id("mock-user-1")
                        .username("admin")
                        .firstName("Admin")
                        .lastName("User")
                        .email("<EMAIL>")
                        .enabled(true)
                        .createdTimestamp(LocalDateTime.now().minusDays(30))
                        .realmRoles(Arrays.asList("ADMIN", "USER"))
                        .clientRoles(Arrays.asList("ms-mission-admin"))
                        .build(),
                UserDTO.builder()
                        .id("mock-user-2")
                        .username("consultant")
                        .firstName("John")
                        .lastName("Doe")
                        .email("<EMAIL>")
                        .enabled(true)
                        .createdTimestamp(LocalDateTime.now().minusDays(15))
                        .realmRoles(Arrays.asList("CONSULTANT", "USER"))
                        .clientRoles(Arrays.asList("ms-mission-user"))
                        .build(),
                UserDTO.builder()
                        .id("mock-user-3")
                        .username("manager")
                        .firstName("Jane")
                        .lastName("Smith")
                        .email("<EMAIL>")
                        .enabled(true)
                        .createdTimestamp(LocalDateTime.now().minusDays(7))
                        .realmRoles(Arrays.asList("MANAGER", "USER"))
                        .clientRoles(Arrays.asList("ms-mission-manager"))
                        .build()
        );
    }

    /**
     * Convertit une représentation utilisateur Keycloak en DTO avec rôles.
     *
     * @param userRepresentation La représentation utilisateur Keycloak
     * @param realm Le realm Keycloak
     * @return Le DTO correspondant avec les rôles
     */
    private UserDTO mapToUserDTOWithRoles(UserRepresentation userRepresentation, String realm) {
        try {
            // Récupérer les rôles realm
            List<String> realmRoles = keycloak.realm(realm)
                    .users()
                    .get(userRepresentation.getId())
                    .roles()
                    .realmLevel()
                    .listEffective()
                    .stream()
                    .map(RoleRepresentation::getName)
                    .toList();

            // Récupérer les rôles client pour notre application
            List<String> clientRoles = new ArrayList<>();
            try {
                String clientId = issuerUri.contains("ms-mission") ? "ms-mission-client" : "account";
                clientRoles = keycloak.realm(realm)
                        .users()
                        .get(userRepresentation.getId())
                        .roles()
                        .clientLevel(clientId)
                        .listEffective()
                        .stream()
                        .map(RoleRepresentation::getName)
                        .toList();
            } catch (Exception e) {
                log.debug("Impossible de récupérer les rôles client pour l'utilisateur {}: {}",
                         userRepresentation.getUsername(), e.getMessage());
            }

            return mapToUserDTO(userRepresentation, realmRoles, clientRoles);

        } catch (Exception e) {
            log.warn("Erreur lors de la récupération des rôles pour l'utilisateur {}: {}",
                     userRepresentation.getUsername(), e.getMessage());
            return mapToUserDTO(userRepresentation, Arrays.asList(), Arrays.asList());
        }
    }

    /**
     * Convertit une représentation utilisateur Keycloak en DTO.
     *
     * @param userRepresentation La représentation utilisateur Keycloak
     * @return Le DTO correspondant
     */
    private UserDTO mapToUserDTO(UserRepresentation userRepresentation) {
        return mapToUserDTO(userRepresentation, Arrays.asList(), Arrays.asList());
    }

    /**
     * Convertit une représentation utilisateur Keycloak en DTO avec rôles spécifiés.
     *
     * @param userRepresentation La représentation utilisateur Keycloak
     * @param realmRoles Les rôles realm
     * @param clientRoles Les rôles client
     * @return Le DTO correspondant
     */
    private UserDTO mapToUserDTO(UserRepresentation userRepresentation, List<String> realmRoles, List<String> clientRoles) {
        LocalDateTime createdTimestamp = null;
        if (userRepresentation.getCreatedTimestamp() != null) {
            createdTimestamp = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(userRepresentation.getCreatedTimestamp()),
                    ZoneId.systemDefault()
            );
        }

        return UserDTO.builder()
                .id(userRepresentation.getId())
                .username(userRepresentation.getUsername())
                .firstName(userRepresentation.getFirstName())
                .lastName(userRepresentation.getLastName())
                .email(userRepresentation.getEmail())
                .enabled(userRepresentation.isEnabled())
                .createdTimestamp(createdTimestamp)
                .attributes(userRepresentation.getAttributes())
                .realmRoles(realmRoles)
                .clientRoles(clientRoles)
                .build();
    }
}
