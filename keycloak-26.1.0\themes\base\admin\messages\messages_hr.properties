invalidPasswordMinLengthMessage=Nevažeća lozinka: minimalna dužina {0}.
invalidPasswordMaxLengthMessage=Nevažeća lozinka: maksimalna duž<PERSON> {0}.
invalidPasswordMinLowerCaseCharsMessage=Nevažeća lozinka: mora sadržavati najmanje {0} malih slova.
invalidPasswordMinDigitsMessage=Nevažeća lozinka: mora sadržavati najmanje {0} numeričkih znamenki.
invalidPasswordMinUpperCaseCharsMessage=Nevažeća lozinka: mora sadržavati najmanje {0} velikih slova.
invalidPasswordMinSpecialCharsMessage=Nevažeća lozinka: mora sadržavati najmanje {0} posebnih znakova.
invalidPasswordNotUsernameMessage=Nevažeća lozinka: ne smije biti jednaka korisničkom imenu.
invalidPasswordNotContainsUsernameMessage=Nevažeća lozinka: ne može sadržavati korisničko ime.
invalidPasswordNotEmailMessage=Nevažeća lozinka: ne smije biti jednaka e-pošti.
invalidPasswordRegexPatternMessage=Nevažeća lozinka: ne odgovara regex obrascima.
invalidPasswordHistoryMessage=Nevažeća lozinka: ne smije biti jednaka nijednoj od posljednjih {0} lozinki.
invalidPasswordBlacklistedMessage=Nevažeća lozinka: lozinka je na crnoj listi.
invalidPasswordGenericMessage=Nevažeća lozinka: nova lozinka ne zadovoljava pravila lozinke.

ldapErrorEditModeMandatory=Način uređivanja je obavezan
ldapErrorInvalidCustomFilter=Prilagođeni LDAP filtar ne počinje s "(" ili ne završava s ")".
ldapErrorConnectionTimeoutNotNumber=Vrijeme isteka veze mora biti broj
ldapErrorReadTimeoutNotNumber=Vrijeme isteka čitanja mora biti broj
ldapErrorMissingClientId=ID klijenta mora biti naveden u konfiguraciji kada se ne koriste uloge okoline.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Nije moguće očuvati nasljeđe grupa i koristiti UID tip članstva zajedno.
ldapErrorCantWriteOnlyForReadOnlyLdap=Nije moguće postaviti read only kada način rada LDAP pružatelja usluga nije WRITABLE
ldapErrorCantWriteOnlyAndReadOnly=Nije moguće postaviti read only i write only zajedno
ldapErrorCantEnableStartTlsAndConnectionPooling=Nije moguće omogućiti i StartTLS i povezivanje u bazu podataka.
ldapErrorCantEnableUnsyncedAndImportOff=Nije moguće onemogućiti uvoz korisnika kada je način rada LDAP pružatelja usluga UNSYNCED
ldapErrorMissingGroupsPathGroup=Grupa putanje grupa ne postoji - molimo kreirajte grupu na navedenoj putanji prvo
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Provjera politike lozinke primjenjuje se samo uz WRITABLE način uređivanja

clientRedirectURIsFragmentError=URI za preusmjerivanje ne smije sadržavati URI fragment
clientRootURLFragmentError=Osnovna URL adresa ne smije sadržavati URL fragment
clientRootURLIllegalSchemeError=Osnovna URL adresa koristi nelegalnu shemu
clientBaseURLIllegalSchemeError=Osnovna URL adresa koristi nelegalnu shemu
backchannelLogoutUrlIllegalSchemeError=Backchannel URL za odjavu koristi nelegalnu shemu
clientRedirectURIsIllegalSchemeError=URI za preusmjerivanje koristi nelegalnu shemu
clientBaseURLInvalid=Osnovna URL adresa nije važeća URL adresa
clientRootURLInvalid=Korijenska URL adresa nije važeća URL adresa
clientRedirectURIsInvalid=URI za preusmjerivanje nije važeća URI
backchannelLogoutUrlIsInvalid=Backchannel URL za odjavu nije važeća URL adresa


pairwiseMalformedClientRedirectURI=Klijent sadrži nevažeći URI za preusmjeravanje.
pairwiseClientRedirectURIsMissingHost=URI za preusmjeravanje klijenta mora sadržavati važeću komponentu hosta.
pairwiseClientRedirectURIsMultipleHosts=Bez konfigurirane URI sektorske identifikacije, URI za preusmjeravanje klijenta ne smiju sadržavati više komponenti hosta.
pairwiseMalformedSectorIdentifierURI=Nevažeći URI sektorske identifikacije.
pairwiseFailedToGetRedirectURIs=Neuspješno dobivanje URI-a za preusmjeravanje iz URI sektorske identifikacije.
pairwiseRedirectURIsMismatch=URI za preusmjeravanje klijenta ne odgovara URI-ima za preusmjeravanje dobivenim iz URI sektorske identifikacije.

duplicatedJwksSettings=Postavke "Koristi JWKS" i "Koristi JWKS URL" ne mogu biti uključene istovremeno.

error-invalid-value=Nevažeća vrijednost.
error-invalid-blank=Molimo navedite vrijednost.
error-empty=Molimo navedite vrijednost.
error-invalid-length=Attribut {0} mora imati duljinu između {1} i {2}.
error-invalid-length-too-short=Attribut {0} mora imati minimalnu duljinu od {1}.
error-invalid-length-too-long=Attribut {0} mora imati maksimalnu duljinu od {2}.
error-invalid-email=Nevažeća adresa e-pošte.
error-invalid-number=Nevažeći broj.
error-number-out-of-range=Attribut {0} mora biti broj između {1} i {2}.
error-number-out-of-range-too-small=Attribut {0} mora imati minimalnu vrijednost od {1}.
error-number-out-of-range-too-big=Attribut {0} mora imati maksimalnu vrijednost od {2}.
error-pattern-no-match=Nevažeća vrijednost.
error-invalid-uri=Nevažeći URL.
error-invalid-uri-scheme=Nevažeći URL shema.
error-invalid-uri-fragment=Nevažeći URL fragment.
error-user-attribute-required=Molimo navedite atribut {0}.
error-invalid-date=Atribut {0} ima nevažeći datum.
error-user-attribute-read-only=Atribut {0} je samo za čitanje.
error-username-invalid-character={0} sadrži nevažeći znak.
error-person-name-invalid-character={0} sadrži nevažeći znak.
error-invalid-multivalued-size=Attribut {0} mora imati najmanje {1} i najviše {2} vrijednosti.
