package otbs.ms_incident.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import otbs.ms_incident.config.RabbitMQPublisherConfig;
import otbs.ms_incident.dto.notification.IncidentEvent;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service pour publier les événements de notification d'incidents vers RabbitMQ.
 * Implémente les cas d'usage définis dans le README :
 * - Incident créé → Notification au RESPONSABLE_SERVICE_GENERAUX
 * - Incident résolu → Notification au créateur de l'incident
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationEventPublisher {

    private final RabbitTemplate rabbitTemplate;
    private final VehiculeClient vehiculeClient;

    private static final List<String> RESPONSABLE_IDS = Arrays.asList(
     ""
    );



    /**
     * Publie un événement de création d'incident
     */
    public void publishIncidentCreated(Incident incident) {
        try {
            log.info("Publication événement incident créé: {}", incident.getId());

            List<String> destinataires = new ArrayList<>(RESPONSABLE_IDS);

            String matricule = getVehiculeMatricule(incident.getVehiculeId());

            IncidentEvent event = new IncidentEvent(
                "CREATED",
                incident.getId(),
                destinataires,
                "Nouvel incident créé",
                String.format("Un nouvel incident de type %s a été créé pour le véhicule %s. " +
                             "Priorité: %s. Description: %s",
                             incident.getType(),
                             matricule,
                             incident.getPriorite(),
                             incident.getDescription())
            );

            enrichirEvenementIncident(event, incident);

            rabbitTemplate.convertAndSend(
                RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                RabbitMQPublisherConfig.INCIDENT_CREATED_ROUTING_KEY,
                event
            );

            log.info("Événement incident créé publié avec succès: {} pour {} destinataires: {}",
                    event.getEventId(), destinataires.size(), destinataires);

        } catch (Exception e) {
            log.error("Erreur lors de la publication de l'événement incident créé: {}", incident.getId(), e);
        }
    }

    /**
     * Publie un événement de résolution d'incident
     */
    public void publishIncidentResolved(Incident incident) {
        try {
            log.info("Publication événement incident résolu: {}", incident.getId());

            List<String> destinataires = new ArrayList<>();
            if (incident.getCreatedBy() != null) {
                destinataires.add(incident.getCreatedBy());
            }

            String matricule = getVehiculeMatricule(incident.getVehiculeId());

            IncidentEvent event = new IncidentEvent(
                "RESOLVED",
                incident.getId(),
                destinataires,
                "Incident résolu",
                String.format("L'incident #%d de type %s a été résolu. " +
                             "Véhicule: %s. Statut: %s",
                             incident.getId(),
                             incident.getType(),
                             matricule,
                             incident.getStatus())
            );

            enrichirEvenementIncident(event, incident);

            rabbitTemplate.convertAndSend(
                RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                RabbitMQPublisherConfig.INCIDENT_RESOLVED_ROUTING_KEY,
                event
            );

            log.info("Événement incident résolu publié avec succès: {} pour {} destinataires: {}",
                    event.getEventId(), destinataires.size(), destinataires);

        } catch (Exception e) {
            log.error("Erreur lors de la publication de l'événement incident résolu: {}", incident.getId(), e);
        }
    }

    /**
     * Enrichit l'événement avec les détails spécifiques de l'incident
     */
    private void enrichirEvenementIncident(IncidentEvent event, Incident incident) {
        event.setIncidentId(incident.getId());
        event.setTypeIncident(incident.getType().toString());
        event.setStatusIncident(incident.getStatus().toString());
        event.setVehiculeId(incident.getVehiculeId());
        event.setCreateurIncident(incident.getCreatedBy());

        String prioriteNotification = mapperPriorite(incident.getPriorite());
        event.setPriorite(prioriteNotification);

        // Convertir la date en format string pour éviter les problèmes de sérialisation
        if (incident.getDate() != null) {
            event.getMetadata().put("dateIncident", incident.getDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")));
            event.getMetadata().put("dateIncidentObj", incident.getDate());
        }
        event.getMetadata().put("lieuIncident", incident.getLieu());
        event.getMetadata().put("description", incident.getDescription());
    }

    /**
     * Mappe la priorité de l'incident vers la priorité de notification
     */
    private String mapperPriorite(NiveauPrioriteIncident prioriteIncident) {
        if (prioriteIncident == null) return "NORMALE";

        return switch (prioriteIncident) {
            case CRITIQUE -> "CRITIQUE";
            case MOYEN -> "MOYEN";
            case FAIBLE -> "FAIBLE";
        };
    }

    private String getVehiculeMatricule(Long vehiculeId) {
        try {
            VehiculeDto vehicule = vehiculeClient.getVehiculeById(vehiculeId);
            return vehicule != null ? vehicule.getImmatriculation() : "Véhicule #" + vehiculeId;
        } catch (Exception e) {
            log.warn("Impossible de récupérer la matricule pour le véhicule {}: {}", vehiculeId, e.getMessage());
            return "Véhicule #" + vehiculeId;
        }
    }
}
