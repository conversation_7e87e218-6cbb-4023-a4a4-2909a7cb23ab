<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('username','password') displayInfo=realm.password && realm.registrationAllowed && !registrationDisabled??; section>
    <#if section = "header">
        <div class="page-header">
            <div class="header-content">
                <div class="logo-wrapper">
                    <img src="${url.resourcesPath}/img/car.png" alt="Logo" class="site-logo">
                </div>
                <div class="header-title">
                    <h1>${msg("loginAccountTitle")}</h1>
                    <p class="header-subtitle">Accédez à votre espace personnel</p>
                </div>
            </div>
        </div>
    <#elseif section = "form">
        <div class="main-container">
            <div class="split-container login-page">
                <div class="form-column">
                    <div class="unified-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="icon icon-login"></i>
                            </div>
                            <div class="card-title">
                                <h2>${msg("loginAccountTitle")}</h2>
                                <p class="card-subtitle">Entrez vos identifiants pour vous connecter</p>
                            </div>
                        </div>
                        
                        <div id="kc-form">
                            <#if realm.password>
                                <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}" method="post">
                                    <#if !usernameHidden??>
                                        <div class="${properties.kcFormGroupClass!} form-group-enhanced">
                                            <label for="username" class="${properties.kcLabelClass!}"><#if !realm.loginWithEmailAllowed>${msg("username")}<#elseif !realm.registrationEmailAsUsername>${msg("usernameOrEmail")}<#else>${msg("email")}</#if></label>
                                            
                                            <div class="input-wrapper">
                                                <div class="input-icon">
                                                    <i class="icon icon-user"></i>
                                                </div>
                                                <input tabindex="2" id="username" class="${properties.kcInputClass!}" name="username" value="${(login.username!'')}"  type="text" autofocus autocomplete="username"
                                                       aria-invalid="<#if messagesPerField.existsError('username','password')>true</#if>"
                                                       dir="ltr" placeholder="Entrez votre identifiant"
                                                />
                                            </div>

                                            <#if messagesPerField.existsError('username','password')>
                                                <span id="input-error" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                                        ${kcSanitize(messagesPerField.getFirstError('username','password'))?no_esc}
                                                </span>
                                            </#if>
                                        </div>
                                    </#if>

                                    <div class="${properties.kcFormGroupClass!} form-group-enhanced">
                                        <label for="password" class="${properties.kcLabelClass!}">${msg("password")}</label>

                                        <div class="input-wrapper">
                                            <div class="input-icon">
                                                <i class="icon icon-lock"></i>
                                            </div>
                                            <div class="${properties.kcInputGroup!} password-input-group" dir="ltr">
                                                <input tabindex="3" id="password" class="${properties.kcInputClass!}" name="password" type="password" autocomplete="current-password"
                                                       aria-invalid="<#if messagesPerField.existsError('username','password')>true</#if>"
                                                       placeholder="Entrez votre mot de passe"
                                                />
                                                <button class="${properties.kcFormPasswordVisibilityButtonClass!}" type="button" aria-label="${msg("showPassword")}"
                                                        aria-controls="password" data-password-toggle tabindex="4"
                                                        data-icon-show="${properties.kcFormPasswordVisibilityIconShow!}" data-icon-hide="${properties.kcFormPasswordVisibilityIconHide!}"
                                                        data-label-show="${msg('showPassword')}" data-label-hide="${msg('hidePassword')}">
                                                    <i class="${properties.kcFormPasswordVisibilityIconShow!}" aria-hidden="true"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <#if usernameHidden?? && messagesPerField.existsError('username','password')>
                                            <span id="input-error" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                                    ${kcSanitize(messagesPerField.getFirstError('username','password'))?no_esc}
                                            </span>
                                        </#if>
                                    </div>

                                    <div class="${properties.kcFormGroupClass!} ${properties.kcFormSettingClass!} form-options-enhanced">
                                        <div class="options-row">
                                            <div id="kc-form-options" class="remember-me-option">
                                                <#if realm.rememberMe && !usernameHidden??>
                                                    <div class="checkbox">
                                                        <label>
                                                            <#if login.rememberMe??>
                                                                <input tabindex="5" id="rememberMe" name="rememberMe" type="checkbox" checked> ${msg("rememberMe")}
                                                            <#else>
                                                                <input tabindex="5" id="rememberMe" name="rememberMe" type="checkbox"> ${msg("rememberMe")}
                                                            </#if>
                                                        </label>
                                                    </div>
                                                </#if>
                                            </div>
                                            <div class="${properties.kcFormOptionsWrapperClass!} forgot-password-option">
                                                <#if realm.resetPasswordAllowed>
                                                    <span><a tabindex="6" href="${url.loginResetCredentialsUrl}">${msg("doForgotPassword")}</a></span>
                                                </#if>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="kc-form-buttons" class="${properties.kcFormGroupClass!} form-buttons-enhanced">
                                        <input type="hidden" id="id-hidden-input" name="credentialId" <#if auth.selectedCredential?has_content>value="${auth.selectedCredential}"</#if>/>
                                        <input tabindex="7" class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonBlockClass!} ${properties.kcButtonLargeClass!} login-button" name="login" id="kc-login" type="submit" value="${msg("doLogIn")}"/>
                                    </div>
                                    
                                    <#if realm.password && realm.registrationAllowed && !registrationDisabled??>
                                        <div id="kc-registration-container" class="registration-container">
                                            <div id="kc-registration" class="registration-prompt">
                                                <span>${msg("noAccount")} <a tabindex="8" href="${url.registrationUrl}" class="register-link">${msg("doRegister")}</a></span>
                                            </div>
                                        </div>
                                    </#if>
                                </form>
                            </#if>
                        </div>
                        
                        <#if realm.password && social?? && social.providers?has_content>
                            <div class="social-section">
                                <div class="social-divider">
                                    <span class="divider-line"></span>
                                    <span class="divider-text">${msg("identity-provider-login-label")}</span>
                                    <span class="divider-line"></span>
                                </div>

                                <ul class="${properties.kcFormSocialAccountListClass!} social-providers-list <#if social.providers?size gt 3>${properties.kcFormSocialAccountListGridClass!}</#if>">
                                    <#list social.providers as p>
                                        <li class="social-provider-item">
                                            <a id="social-${p.alias}" class="${properties.kcFormSocialAccountListButtonClass!} social-button <#if social.providers?size gt 3>${properties.kcFormSocialAccountGridItem!}</#if>"
                                                    type="button" href="${p.loginUrl}">
                                                <#if p.iconClasses?has_content>
                                                    <i class="${properties.kcCommonLogoIdP!} ${p.iconClasses!} provider-icon" aria-hidden="true"></i>
                                                    <span class="${properties.kcFormSocialAccountNameClass!} provider-name">${p.displayName!}</span>
                                                <#else>
                                                    <span class="${properties.kcFormSocialAccountNameClass!} provider-name-only">${p.displayName!}</span>
                                                </#if>
                                            </a>
                                        </li>
                                    </#list>
                                </ul>
                            </div>
                        </#if>
                    </div>
                </div>
                <div class="image-column">
                    <div class="image-wrapper">
                        <img src="${url.resourcesPath}/img/Login.jpg" alt="Login Image" class="login-image">
                        <div class="image-overlay">
                            <div class="overlay-content">
                                <h2 class="overlay-title">Bienvenue sur notre plateforme</h2>
                                <p class="overlay-description">Connectez-vous pour accéder à votre espace personnel et profiter de tous nos services.</p>
                                <div class="overlay-features">
                                    <div class="feature-item">
                                        <i class="icon icon-secure"></i>
                                        <span>Sécurité renforcée</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="icon icon-fast"></i>
                                        <span>Accès rapide</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="icon icon-support"></i>
                                        <span>Support 24/7</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script type="module" src="${url.resourcesPath}/js/passwordVisibility.js"></script>
    </#if>
</@layout.registrationLayout>
