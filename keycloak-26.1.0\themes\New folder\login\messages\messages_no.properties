doLogIn=Logg inn
doRegister=Registrer deg
doRegisterSecurityKey=Registrer deg
doCancel=Avbryt
doSubmit=Send inn
doYes=Ja
doNo=Nei
doContinue=Fortsett
doAccept=Aksepter
doDecline=Avslå
doForgotPassword=Glemt passord?
doClickHere=Klikk her
doImpersonate=Utgi deg for å være en annen bruker
kerberosNotConfigured=Kerberos er ikke konfigurert
kerberosNotConfiguredTitle=Kerberos er ikke konfigurert
bypassKerberosDetail=Enten er du ikke logget inn via Kerberos eller så støtter ikke nettleseren innlogging med Kerberos. Vennligst klikk Fortsett for å logge inn på andre måter
kerberosNotSetUp=Kerberos er ikke konfigurert. Du kan ikke logge inn.
registerWithTitle=Registrer deg med {0}
registerWithTitleHtml={0}
loginTitle=Logg inn på {0}
loginTitleHtml={0}
impersonateTitle={0} Gi deg ut for å være en annen bruker
impersonateTitleHtml=<strong>{0}</strong> Gi deg ut for å være en annen bruker
realmChoice=Sikkerhetsdomene
unknownUser=Ukjent bruker
loginTotpTitle=Konfigurer autentifikator for mobil
loginProfileTitle=Oppdater konto
loginTimeout=Du brukte for lang tid på å logge inn. Vennligst prøv igjen.
oauthGrantTitle=Gi tilgang
oauthGrantTitleHtml={0}
errorTitle=Vi beklager...
errorTitleHtml=Vi <strong>beklager</strong> ...
emailVerifyTitle=E-postbekreftelse
emailForgotTitle=Glemt passord?
updatePasswordTitle=Oppdater passord
codeSuccessTitle=Suksesskode
codeErrorTitle=Feilkode\: {0}

termsTitle=Vilkår og betingelser
termsTitleHtml=Vilkår og betingelser
termsText=<p>Vilkår og betingelser kommer</p>

recaptchaFailed=Ugyldig Bildebekreftelse
recaptchaNotConfigured=Bildebekreftelse er påkrevet, men er ikke konfigurert
consentDenied=Samtykke avslått.

noAccount=Ny bruker?
username=Brukernavn
usernameOrEmail=Brukernavn eller e-postadresse
firstName=Fornavn
givenName=Fornavn
fullName=Fullstendig navn
lastName=Etternavn
familyName=Etternavn
email=E-postadresse
password=Passord
passwordConfirm=Bekreft passord
passwordNew=Nytt passord
passwordNewConfirm=Bekreft nytt Passord
rememberMe=Husk meg
authenticatorCode=Engangskode
address=Adresse
street=Gate-/veinavn + husnummer
locality=By
region=Fylke
postal_code=Postnummer
country=Land
emailVerified=E-postadresse bekreftet
gssDelegationCredential=GSS legitimasjons-delegering

loginTotpStep1=Installer <a href="https://freeotp.github.io/" target="_blank">FreeOTP</a> eller Google Authenticator på din mobiltelefon. Begge applikasjoner er tilgjengelige på <a href="https://play.google.com">Google Play</a> og Apple App Store.
loginTotpStep2=Åpne applikasjonen og skann strekkoden eller skriv inn koden
loginTotpStep3=Skriv inn engangskoden fra applikasjonen og klikk send inn for å fullføre
loginOtpOneTime=Engangskode

oauthGrantRequest=Vil du gi disse tilgangsrettighetene?
inResource=i

emailVerifyInstruction1=En e-post med instruksjoner for å bekrefte din e-postadresse har blitt sendt til deg.
emailVerifyInstruction2=Ikke mottatt en bekreftelseskode i e-posten vi sendte til deg?
emailVerifyInstruction3=for å sende e-post på nytt.

emailLinkIdpTitle=Lenke {0}
emailLinkIdp1=En e-post med instruksjoner for å koble {0} konto med din {2} konto har blitt sendt til deg.
emailLinkIdp2=Ikke mottatt en bekreftelseskode i e-posten vi sendte til deg?
emailLinkIdp3=for å sende e-post på nytt.

backToLogin=&laquo; Tilbake til innlogging
emailInstruction=Skriv inn e-postadressen din og vi vil sende deg instruksjoner for hvordan du oppretter et nytt passord.

copyCodeInstruction=Vennligst kopier denne koden og lim den inn i applikasjonen din:

personalInfo=Personlig informasjon:
role_admin=Administrator
role_realm-admin=Administrator for sikkerhetsdomene
role_create-realm=Opprette sikkerhetsdomene
role_create-client=Opprette klient
role_view-realm=Se sikkerhetsdomene
role_view-users=Se brukere
role_view-applications=Se applikasjoner
role_view-clients=Se klienter
role_view-events=Se hendelser
role_view-identity-providers=Se identitetsleverandører
role_manage-realm=Administrere sikkerhetsdomene
role_manage-users=Administrere brukere
role_manage-applications=Administrere applikasjoner
role_manage-identity-providers=Administrere identitetsleverandører
role_manage-clients=Administrere klienter
role_manage-events=Administrere hendelser
role_view-profile=Se profil
role_manage-account=Administrere konto
role_read-token=Lese token
role_offline-access=Frakoblet tilgang
role_uma_authorization=Skaffe tillatelser
client_account=Konto
client_security-admin-console=Sikkerthetsadministrasjonskonsoll
client_realm-management=Sikkerhetsdomene-administrasjon
client_broker=Broker

invalidUserMessage=Ugyldig brukernavn eller passord.
invalidEmailMessage=Ugyldig e-postadresse.
accountDisabledMessage=Konto er deaktivert, kontakt administrator.
accountTemporarilyDisabledMessage=Ugyldig brukernavn eller passord.
accountPermanentlyDisabledMessage=Ugyldig brukernavn eller passord.
accountTemporarilyDisabledMessageTotp=Ugyldig engangskode.
accountPermanentlyDisabledMessageTotp=Ugyldig engangskode.
expiredCodeMessage=Login ble tidsavbrutt. Vennligst logg inn på nytt.

missingFirstNameMessage=Vennligst oppgi fornavn.
missingLastNameMessage=Vennligst oppgi etternavn.
missingEmailMessage=Vennligst oppgi e-postadresse.
missingUsernameMessage=Vennligst oppgi brukernavn.
missingPasswordMessage=Vennligst oppgi passord.
missingTotpMessage=Vennligst oppgi autentiseringskode.
notMatchPasswordMessage=Passordene er ikke like.

invalidPasswordExistingMessage=Ugyldig eksisterende passord.
invalidPasswordConfirmMessage=Passord er ikke like.
invalidTotpMessage=Ugyldig engangskode.

usernameExistsMessage=Brukernavnet finnes allerede.
emailExistsMessage=E-post finnes allerede.

federatedIdentityExistsMessage=Bruker med {0} {1} finnes allerede. Vennligst logg inn på kontoadministratsjon for å koble sammen kontoene.

confirmLinkIdpTitle=Kontoen finnes allerede
federatedIdentityConfirmLinkMessage=Bruker med {0} {1} finnes allerede. Hvordan vil du fortsette?
#federatedIdentityConfirmReauthenticateMessage=Bekreft at du er {0} for å koble din konto med {1}
confirmLinkIdpReviewProfile=Se over og bekreft profil
confirmLinkIdpContinue=Legg til eksisterende konto

configureTotpMessage=Du må sette opp en engangskode-generator for å aktivere konto.
updateProfileMessage=Du må oppdatere brukerprofilen din for å aktivere konto.
updatePasswordMessage=Du må skifte passord for å aktivere kontoen din.
verifyEmailMessage=Du må bekrefte e-postadressen din for å aktivere konto.
linkIdpMessage=You need to verify your email address to link your account with {0}.

emailSentMessage=Du vil straks motta en e-post med ytterlige instruksjoner.
emailSendErrorMessage=Mislyktes å sende e-post, vennligst prøv igjen senere.

accountUpdatedMessage=Din konto har blitt oppdatert.
accountPasswordUpdatedMessage=Ditt passord har blitt oppdatert.

noAccessMessage=Ingen tilgang

invalidPasswordMinLengthMessage=Ugyldig passord: minimum lengde {0}.
invalidPasswordMinDigitsMessage=Ugyldig passord: må inneholde minimum {0} sifre.
invalidPasswordMinLowerCaseCharsMessage=Ugyldig passord: må inneholde minimum {0} små bokstaver.
invalidPasswordMinUpperCaseCharsMessage=Ugyldig passord: må inneholde minimum {0} store bokstaver.
invalidPasswordMinSpecialCharsMessage=Ugyldig passord: må inneholde minimum {0} spesialtegn.
invalidPasswordNotUsernameMessage=Ugyldig passord: kan ikke være likt brukernavn.
invalidPasswordRegexPatternMessage=Ugyldig passord: tilfredsstiller ikke kravene for passord-mønster.
invalidPasswordHistoryMessage=Ugyldig passord: kan ikke være likt noen av de {0} foregående passordene.

failedToProcessResponseMessage=Kunne ikke behandle svar
httpsRequiredMessage=HTTPS påkrevd
realmNotEnabledMessage=Sikkerhetsdomene er ikke aktivert
invalidRequestMessage=Ugyldig forespørsel
failedLogout=Utlogging feilet
unknownLoginRequesterMessage=Ukjent anmoder for innlogging
loginRequesterNotEnabledMessage=Anmoder for innlogging er ikke aktivert
bearerOnlyMessage=Bearer-only applikasjoner har ikke lov til å initiere innlogging via nettleser
standardFlowDisabledMessage=Klienten har ikke lov til å initiere innlogging via nettleser med gitt response_type. Standard flow er deaktivert for denne klienten.
implicitFlowDisabledMessage=Klienten har ikke lov til å initiere innlogging via nettleser med gitt response_type. Implicit flow er deaktivert for denne klienten.
invalidRedirectUriMessage=Ugyldig redirect uri
unsupportedNameIdFormatMessage=NameIDFormat er ikke støttet
invalidRequesterMessage=Ugyldig sender av forespørsel
registrationNotAllowedMessage=Registrering er ikke lov
resetCredentialNotAllowedMessage=Tilbakestilling av innloggingsdata er ikke lov

permissionNotApprovedMessage=Tillatelse ikke godkjent.
noRelayStateInResponseMessage=Ingen relay state i svar fra identitetsleverandør.
insufficientPermissionMessage=Utilstrekkelige rettigheter for å koble identiteter.
couldNotProceedWithAuthenticationRequestMessage=Kunne ikke gå videre med autentiseringsforespørsel til identitetsleverandør.
couldNotObtainTokenMessage=Klarte ikke å innhente token fra identitetsleverandør.
unexpectedErrorRetrievingTokenMessage=Uventet feil ved henting av token fra identitetsleverandør.
unexpectedErrorHandlingResponseMessage=Uventet feil ved håndtering av svar fra identitetsleverandør.
identityProviderAuthenticationFailedMessage=Autentisering feilet. Kunne ikke autentisere med identitetsleverandør.
couldNotSendAuthenticationRequestMessage=Kunne ikke sende autentiseringsforespørsel til identitetsleverandør.
unexpectedErrorHandlingRequestMessage=Uventet feil ved håndtering av autentiseringsforespørsel til identitetsleverandør.
invalidAccessCodeMessage=Ugyldig tilgangskode.
sessionNotActiveMessage=Sesjonen er ikke aktiv.
invalidCodeMessage=En feil oppstod, vennligst logg inn på nytt i din applikasjon.
identityProviderUnexpectedErrorMessage=Uventet feil ved autentisering med identitetsleverandør
identityProviderNotFoundMessage=Kunne ikke finne en identitetsleverandør med identifikatoren.
identityProviderLinkSuccess=Din konto ble suksessfullt koblet med {0} konto {1}.
staleCodeMessage=Denne siden er ikke lenger gyldig. Vennligst gå tilbake til applikasjonen din og logg inn på nytt.
realmSupportsNoCredentialsMessage=Sikkerhetsdomene støtter ingen legitimasjonstyper.
identityProviderNotUniqueMessage=Sikkerhetsdomene støtter flere identitetsleverandører. Kunne ikke avgjøre hvilken identitetsleverandør som burde brukes for autentisering.
emailVerifiedMessage=Din e-postadresse har blitt verifisert.
staleEmailVerificationLink=Lenken du klikket er utgått og er ikke lenger gyldig. Har du kanskje allerede bekreftet e-postadressen din?

backToApplication=&laquo; Tilbake til applikasjonen
missingParameterMessage=Manglende parameter\: {0}
clientNotFoundMessage=Klient ikke funnet.
clientDisabledMessage=Klient deaktivert.
invalidParameterMessage=Ugyldig parameter\: {0}
alreadyLoggedIn=Du er allerede innlogget.
