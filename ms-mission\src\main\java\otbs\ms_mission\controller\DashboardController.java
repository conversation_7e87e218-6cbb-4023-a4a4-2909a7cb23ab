package otbs.ms_mission.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import otbs.ms_mission.dto.MissionStatsDTO;
import otbs.ms_mission.dto.DetailedMissionDTO;
import otbs.ms_mission.dto.MissionTypeStatsDTO;
import otbs.ms_mission.dto.ProjectMissionStatsDTO;
import otbs.ms_mission.dto.UserParticipationStatsDTO;
import otbs.ms_mission.service.MissionStatsService;
import otbs.ms_mission.service.RecentMissionsService;
import otbs.ms_mission.service.MissionTypeStatsService;
import otbs.ms_mission.service.ProjectMissionStatsService;
import otbs.ms_mission.service.UserParticipationStatsService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Controller pour les endpoints du dashboard.
 * Fournit les statistiques et métriques des missions.
 */
@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Dashboard", description = "API pour les statistiques et métriques du dashboard")
public class DashboardController {

    private final MissionStatsService missionStatsService;
    private final RecentMissionsService recentMissionsService;
    private final MissionTypeStatsService missionTypeStatsService;
    private final ProjectMissionStatsService projectMissionStatsService;
    private final UserParticipationStatsService userParticipationStatsService;

    @GetMapping("/mission-stats")
    @Operation(
        summary = "Récupère les statistiques des missions pour une période",
        description = "Calcule le nombre total de missions, missions à venir, missions actuelles et missions terminées " +
                     "pour la période spécifiée. La période est définie par les dates de début et de fin."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistiques calculées avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres de date invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<MissionStatsDTO> getMissionStats(
            @Parameter(description = "Date de début de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-01-01T00:00:00")
            @RequestParam("dateDebut")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateDebut,

            @Parameter(description = "Date de fin de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-12-31T23:59:59")
            @RequestParam("dateFin")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateFin) {

        log.info("Demande de statistiques des missions pour la période du {} au {}", dateDebut, dateFin);

        try {
            MissionStatsDTO stats = missionStatsService.calculateMissionStats(dateDebut, dateFin);

            log.debug("Statistiques calculées: {}", stats);

            return ResponseEntity.ok(stats);

        } catch (IllegalArgumentException e) {
            log.warn("Paramètres invalides pour le calcul des statistiques: {}", e.getMessage());
            return ResponseEntity.badRequest().build();

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques des missions", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/recent-missions")
    @Operation(
        summary = "Récupère les missions récentes pour une période",
        description = "Retourne les missions récentes avec toutes leurs informations détaillées " +
                     "pour la période spécifiée, triées par date de création décroissante."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Missions récentes récupérées avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<List<DetailedMissionDTO>> getRecentMissions(
            @Parameter(description = "Date de début de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-01-01T00:00:00")
            @RequestParam("dateDebut")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateDebut,

            @Parameter(description = "Date de fin de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-12-31T23:59:59")
            @RequestParam("dateFin")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateFin,

            @Parameter(description = "Nombre maximum de missions à retourner",
                      required = true, example = "10")
            @RequestParam("limit")
            int limit) {

        log.info("Demande de {} missions récentes pour la période du {} au {}", limit, dateDebut, dateFin);

        try {
            List<DetailedMissionDTO> missions = recentMissionsService.getRecentMissions(dateDebut, dateFin, limit);

            log.debug("Récupéré {} missions récentes", missions.size());

            return ResponseEntity.ok(missions);

        } catch (IllegalArgumentException e) {
            log.warn("Paramètres invalides pour les missions récentes: {}", e.getMessage());
            return ResponseEntity.badRequest().build();

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des missions récentes", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/mission-type-stats")
    @Operation(
        summary = "Récupère les statistiques des missions par type pour une période",
        description = "Retourne le nombre de missions pour chaque type : SITESURVEY, VISITEAVANTVENTE, " +
                     "VISITEPREVENTIVE, VISITECURATIVE, PROJET pour la période spécifiée."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistiques par type récupérées avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres de date invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<MissionTypeStatsDTO> getMissionTypeStats(
            @Parameter(description = "Date de début de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-01-01T00:00:00")
            @RequestParam("dateDebut")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateDebut,

            @Parameter(description = "Date de fin de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-12-31T23:59:59")
            @RequestParam("dateFin")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateFin) {

        log.info("Demande de statistiques des missions par type pour la période du {} au {}", dateDebut, dateFin);

        try {
            MissionTypeStatsDTO stats = missionTypeStatsService.getMissionTypeStats(dateDebut, dateFin);

            log.debug("Statistiques par type calculées: {}", stats);

            return ResponseEntity.ok(stats);

        } catch (IllegalArgumentException e) {
            log.warn("Paramètres invalides pour les statistiques par type: {}", e.getMessage());
            return ResponseEntity.badRequest().build();

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques par type", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/project-mission-stats")
    @Operation(
        summary = "Récupère les statistiques des missions par projet pour une période",
        description = "Retourne le nombre de missions pour chaque projet avec le nom du projet " +
                     "et les informations associées pour la période spécifiée."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistiques par projet récupérées avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres de date invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<ProjectMissionStatsDTO> getProjectMissionStats(
            @Parameter(description = "Date de début de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-01-01T00:00:00")
            @RequestParam("dateDebut")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateDebut,

            @Parameter(description = "Date de fin de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = true, example = "2024-12-31T23:59:59")
            @RequestParam("dateFin")
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateFin) {

        log.info("Demande de statistiques des missions par projet pour la période du {} au {}", dateDebut, dateFin);

        try {
            ProjectMissionStatsDTO stats = projectMissionStatsService.getProjectMissionStats(dateDebut, dateFin);

            log.debug("Statistiques par projet calculées: {} projets, {} missions",
                     stats.getTotalProjects(), stats.getTotalMissions());

            return ResponseEntity.ok(stats);

        } catch (IllegalArgumentException e) {
            log.warn("Paramètres invalides pour les statistiques par projet: {}", e.getMessage());
            return ResponseEntity.badRequest().build();

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques par projet", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/user-participation-stats")
    @Operation(
        summary = "Récupère les statistiques de participation des utilisateurs aux missions",
        description = "Retourne les statistiques de participation des accompagnants et créateurs " +
                     "pour la période spécifiée. Inclut le nombre de missions en tant qu'accompagnant, " +
                     "créateur, et le total des participations pour chaque utilisateur."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistiques de participation récupérées avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres de date invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<UserParticipationStatsDTO> getUserParticipationStats(
            @Parameter(description = "Date de début de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = false, example = "2024-01-01T00:00:00")
            @RequestParam(value = "dateDebut", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateDebut,

            @Parameter(description = "Date de fin de la période (format: yyyy-MM-dd'T'HH:mm:ss)",
                      required = false, example = "2024-12-31T23:59:59")
            @RequestParam(value = "dateFin", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime dateFin) {

        log.info("Demande de statistiques de participation des utilisateurs pour la période du {} au {}",
                 dateDebut, dateFin);

        try {
            UserParticipationStatsDTO stats = userParticipationStatsService.getUserParticipationStats(dateDebut, dateFin);

            log.debug("Statistiques de participation calculées: {} utilisateurs, {} participations totales",
                     stats.getTotalUsers(), stats.getTotalParticipations());

            return ResponseEntity.ok(stats);

        } catch (IllegalArgumentException e) {
            log.warn("Paramètres invalides pour les statistiques de participation: {}", e.getMessage());
            return ResponseEntity.badRequest().build();

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques de participation", e);
            return ResponseEntity.internalServerError().build();
        }
    }

}
