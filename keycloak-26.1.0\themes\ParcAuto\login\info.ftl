<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=false; section>
    <#if section = "header">
        <div class="page-header">
            <img src="${url.resourcesPath}/img/car.png" alt="Logo" class="site-logo">
            <span>
                <#if messageHeader??>
                    ${kcSanitize(msg("${messageHeader}"))?no_esc}
                <#else>
                    ${message.summary}
                </#if>
            </span>
        </div>
    <#elseif section = "form">
        <div class="info-page">
            <img src="${url.resourcesPath}/img/car.png" alt="Logo" class="site-logo" style="width: 100px; height: auto; margin-bottom: 2rem;">
            <div class="icon">
                <i class="icon icon-info"></i>
            </div>
            <div id="kc-info-message">
                <h2>
                    <#if messageHeader??>
                        ${kcSanitize(msg("${messageHeader}"))?no_esc}
                    <#else>
                        ${message.summary}
                    </#if>
                </h2>
                <p class="instruction">${message.summary}<#if requiredActions??><#list requiredActions>: <b><#items as reqActionItem>${kcSanitize(msg("requiredAction.${reqActionItem}"))?no_esc}<#sep>, </#items></b></#list><#else></#if></p>
                <div class="action-links">
                    <#if skipLink??>
                    <#else>
                        <#if pageRedirectUri?has_content>
                            <a href="${pageRedirectUri}" class="btn btn-primary">${kcSanitize(msg("backToApplication"))?no_esc}</a>
                        <#elseif actionUri?has_content>
                            <a href="${actionUri}" class="btn btn-primary">${kcSanitize(msg("proceedWithAction"))?no_esc}</a>
                        <#elseif (client.baseUrl)?has_content>
                            <a href="${client.baseUrl}" class="btn btn-primary">${kcSanitize(msg("backToApplication"))?no_esc}</a>
                        </#if>
                    </#if>
                </div>
            </div>
        </div>
    </#if>
</@layout.registrationLayout>