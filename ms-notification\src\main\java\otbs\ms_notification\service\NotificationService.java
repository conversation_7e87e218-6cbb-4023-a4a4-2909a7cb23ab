package otbs.ms_notification.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import otbs.ms_notification.dto.NotificationResponseDTO;
import otbs.ms_notification.mapper.NotificationMapper;
import otbs.ms_notification.model.Notification;
import otbs.ms_notification.model.NotificationStatus;
import otbs.ms_notification.model.StatutNotification;
import otbs.ms_notification.repository.NotificationRepository;
import otbs.ms_notification.repository.NotificationStatusRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;


@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class NotificationService {

    private final NotificationRepository notificationRepository;
    private final NotificationStatusRepository notificationStatusRepository;
    private final NotificationMapper notificationMapper;
    private final WebSocketNotificationService webSocketService;
    private final EmailService emailService;
    private final UserService userService;

    public Notification sauvegarderNotification(Notification notification) {
        log.info("Sauvegarde d'une nouvelle notification: {}", notification.getTitre());
        return notificationRepository.save(notification);
    }

    public Notification sauvegarderNotificationAvecDestinataires(Notification notification, List<String> destinataires) {
        log.info("Sauvegarde d'une nouvelle notification avec {} destinataires: {}", destinataires.size(), notification.getTitre());

        Notification savedNotification = notificationRepository.save(notification);

        // Créer les statuts de notification pour chaque destinataire
        for (String destinataire : destinataires) {
            NotificationStatus status = NotificationStatus.builder()
                    .notification(savedNotification)
                    .destinataire(destinataire)
                    .statut(StatutNotification.NON_LUE)
                    .dateCreation(LocalDateTime.now())
                    .build();
            notificationStatusRepository.save(status);
            webSocketService.broadcastNotificationToUser(status);
        }

        // Envoyer les emails avec la méthode basique (sans métadonnées enrichies)
        log.info("Envoi des emails de notification basiques pour {} destinataires", destinataires.size());
        for (String destinataire : destinataires) {
            try {
                // Créer un NotificationStatus pour l'email
                NotificationStatus statusForEmail = notificationStatusRepository
                    .findByNotificationIdAndDestinataire(savedNotification.getId(), destinataire)
                    .orElse(null);
                
                if (statusForEmail != null) {
                    emailService.sendNotificationEmail(savedNotification, statusForEmail, destinataire);
                    log.debug("Email basique envoyé avec succès à: {}", destinataire);
                }
            } catch (Exception e) {
                log.error("Erreur lors de l'envoi de l'email basique à {}: {}", destinataire, e.getMessage(), e);
                // Continue avec les autres destinataires même si un email échoue
            }
        }

        return savedNotification;
    }
    
    public Notification sauvegarderNotificationAvecDestinatairesPourEvent(Notification notification, List<String> destinataires, otbs.ms_notification.dto.events.NotificationEvent event) {
        log.info("Sauvegarde d'une nouvelle notification avec {} destinataires pour événement: {}", destinataires.size(), notification.getTitre());

        Notification savedNotification = notificationRepository.save(notification);

        // Créer les statuts de notification pour chaque destinataire
        for (String destinataire : destinataires) {
            NotificationStatus status = NotificationStatus.builder()
                    .notification(savedNotification)
                    .destinataire(destinataire)
                    .statut(StatutNotification.NON_LUE)
                    .dateCreation(LocalDateTime.now())
                    .build();
            notificationStatusRepository.save(status);
            webSocketService.broadcastNotificationToUser(status);
        }

        // Envoyer les emails APRÈS avoir créé tous les statuts
        // Un email par destinataire avec l'événement original qui contient les métadonnées
        log.info("Envoi des emails de notification pour {} destinataires", destinataires.size());
        for (String destinataire : destinataires) {
            try {
                emailService.sendNotificationEmail(event, destinataire);
                log.debug("Email envoyé avec succès à: {}", destinataire);
            } catch (Exception e) {
                log.error("Erreur lors de l'envoi de l'email à {}: {}", destinataire, e.getMessage(), e);
                // Continue avec les autres destinataires même si un email échoue
            }
        }

        return savedNotification;
    }


    public Notification sauvegarderNotificationComplete(Notification notification, List<String> destinataires, 
                                                        otbs.ms_notification.dto.events.NotificationEvent event, 
                                                        boolean sendEmails) {
        log.info("Sauvegarde complète d'une notification avec {} destinataires: {}, emails: {}", 
                destinataires.size(), notification.getTitre(), sendEmails);

        Notification savedNotification = notificationRepository.save(notification);

        // Créer les statuts de notification pour chaque destinataire
        List<NotificationStatus> statuses = new ArrayList<>();
        for (String destinataire : destinataires) {
            NotificationStatus status = NotificationStatus.builder()
                    .notification(savedNotification)
                    .destinataire(destinataire)
                    .statut(StatutNotification.NON_LUE)
                    .dateCreation(LocalDateTime.now())
                    .build();
            NotificationStatus savedStatus = notificationStatusRepository.save(status);
            statuses.add(savedStatus);
            webSocketService.broadcastNotificationToUser(savedStatus);
        }

        // Envoyer les emails si demandé
        if (sendEmails && !destinataires.isEmpty()) {
            log.info("Envoi des emails de notification pour {} destinataires", destinataires.size());
            
            for (String destinataire : destinataires) {
                try {
                    if (event != null) {
                        // Utiliser l'événement avec métadonnées enrichies
                        emailService.sendNotificationEmail(event, destinataire);
                        log.debug("Email enrichi envoyé avec succès à: {}", destinataire);
                    } else {
                        // Utiliser la méthode basique
                        NotificationStatus statusForEmail = statuses.stream()
                            .filter(s -> s.getDestinataire().equals(destinataire))
                            .findFirst()
                            .orElse(null);
                        
                        if (statusForEmail != null) {
                            emailService.sendNotificationEmail(savedNotification, statusForEmail, destinataire);
                            log.debug("Email basique envoyé avec succès à: {}", destinataire);
                        }
                    }
                } catch (Exception e) {
                    log.error("Erreur lors de l'envoi de l'email à {}: {}", destinataire, e.getMessage(), e);
                    // Continue avec les autres destinataires même si un email échoue
                }
            }
        }

        return savedNotification;
    }

    @Transactional(readOnly = true)
    public List<NotificationResponseDTO> getNotificationsForCurrentUser() {
        try {
            String currentUser = getCurrentUserId();
            log.info("Récupération des notifications non archivées pour l'utilisateur: {}", currentUser);


            List<NotificationStatus> statusesByUser = notificationStatusRepository
                    .findByDestinataireAndStatutNotOrderByDateCreationDesc(currentUser, StatutNotification.ARCHIVEE);

            log.info("Trouvé {} notifications non archivées pour l'utilisateur: {}", statusesByUser.size(), currentUser);
            return notificationMapper.toResponseDTOList(statusesByUser);
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des notifications pour l'utilisateur actuel", e);
            return new ArrayList<>();
        }
    }

    @Transactional(readOnly = true)
    public List<NotificationResponseDTO> getUnreadNotificationsForCurrentUser() {
        String currentUser = getCurrentUserId();
        log.info("Récupération des notifications non lues pour l'utilisateur: {}", currentUser);

        List<NotificationStatus> unreadStatuses = notificationStatusRepository
                .findByDestinataireAndStatutOrderByDateCreationDesc(currentUser, StatutNotification.NON_LUE);

        return notificationMapper.toResponseDTOList(unreadStatuses);
    }

    @Transactional(readOnly = true)
    public Long getUnreadCountForCurrentUser() {
        String currentUser = getCurrentUserId();
        log.info("Comptage des notifications non lues pour l'utilisateur: {}", currentUser);

        Long count = notificationStatusRepository.countByDestinataireAndStatut(currentUser, StatutNotification.NON_LUE);
        log.info("Comptage: {} notifications non lues pour l'utilisateur {}", count, currentUser);
        return count;
    }

    public void markAsRead(Long notificationId) {
        log.info("Marquage de la notification {} comme lue", notificationId);
        String currentUser = getCurrentUserId();

        NotificationStatus status = notificationStatusRepository
                .findByNotificationIdAndDestinataire(notificationId, currentUser)
                .orElseThrow(() -> new RuntimeException("Statut de notification non trouvé pour l'utilisateur: " + currentUser));

        status.marquerCommeLue();
        notificationStatusRepository.save(status);

        webSocketService.broadcastNotificationStatusUpdate(status, "MARKED_AS_READ");

        long unreadCount = getUnreadCountForCurrentUser();
        webSocketService.broadcastUnreadCountToUser(currentUser, unreadCount);

        log.info("Notification {} marquée comme lue pour l'utilisateur {}", notificationId, currentUser);
    }

    public void markAllAsReadForCurrentUser() {
        String currentUser = getCurrentUserId();
        log.info("Marquage de toutes les notifications comme lues pour l'utilisateur: {}", currentUser);

        int updatedCount = notificationStatusRepository.markAllAsReadForUser(
            currentUser, StatutNotification.LUE, StatutNotification.NON_LUE);

        webSocketService.broadcastUnreadCountToUser(currentUser, 0L);
        log.info("{} notifications marquées comme lues pour l'utilisateur {}", updatedCount, currentUser);
    }

    public void archiveNotification(Long notificationId) {
        log.info("Archivage de la notification: {}", notificationId);
        String currentUser = getCurrentUserId();

        NotificationStatus status = notificationStatusRepository
                .findByNotificationIdAndDestinataire(notificationId, currentUser)
                .orElseThrow(() -> new RuntimeException("Statut de notification non trouvé pour l'utilisateur: " + currentUser));

        status.setStatut(StatutNotification.ARCHIVEE);
        notificationStatusRepository.save(status);

        webSocketService.broadcastNotificationStatusUpdate(status, "ARCHIVED");
        log.info("Notification {} archivée pour l'utilisateur {}", notificationId, currentUser);
    }

    public void archiveAllReadForCurrentUser() {
        String currentUser = getCurrentUserId();
        log.info("Archivage de toutes les notifications lues pour l'utilisateur: {}", currentUser);

        int archivedCount = notificationStatusRepository.archiveAllReadForUser(
            currentUser, StatutNotification.ARCHIVEE, StatutNotification.LUE);

        log.info("{} notifications archivées pour l'utilisateur {}", archivedCount, currentUser);
    }

    @Transactional(readOnly = true)
    public List<NotificationResponseDTO> getArchivedNotificationsForCurrentUser() {
        String currentUser = getCurrentUserId();
        log.info("Récupération des notifications archivées pour l'utilisateur: {}", currentUser);
        List<NotificationStatus> statuses = notificationStatusRepository
                .findByDestinataireAndStatutOrderByDateCreationDesc(currentUser, StatutNotification.ARCHIVEE);
        return notificationMapper.toResponseDTOList(statuses);
    }

    @Transactional(readOnly = true)
    public List<NotificationResponseDTO> getNonArchivedNotificationsForCurrentUser() {
        String currentUser = getCurrentUserId();
        log.info("Récupération des notifications non archivées pour l'utilisateur: {}", currentUser);
        List<NotificationStatus> statuses = notificationStatusRepository
                .findByDestinataireAndStatutNotOrderByDateCreationDesc(currentUser, StatutNotification.ARCHIVEE);
        return notificationMapper.toResponseDTOList(statuses);
    }



    @Transactional(readOnly = true)
    public NotificationResponseDTO getNotificationById(Long id) {
        log.info("Récupération de la notification avec l'ID: {}", id);
        String currentUser = getCurrentUserId();

        NotificationStatus status = notificationStatusRepository
                .findByNotificationIdAndDestinataire(id, currentUser)
                .orElseThrow(() -> new RuntimeException("Statut de notification non trouvé pour l'utilisateur: " + currentUser));

        return notificationMapper.toResponseDTO(status);
    }

    @Transactional(readOnly = true)
    public Page<NotificationResponseDTO> getNotificationsForCurrentUser(Pageable pageable) {
        String currentUser = getCurrentUserId();
        log.info("Récupération des notifications non archivées avec pagination pour l'utilisateur: {}", currentUser);
        Page<NotificationStatus> statuses = notificationStatusRepository
                .findByDestinataireAndStatutNotOrderByDateCreationDesc(currentUser, StatutNotification.ARCHIVEE, pageable);
        return statuses.map(notificationMapper::toResponseDTO);
    }

    @Transactional(readOnly = true)
    public List<NotificationResponseDTO> getNotificationsForUser(String userId) {
        log.info("Récupération des notifications pour l'utilisateur: {}", userId);
        List<NotificationStatus> statuses = notificationStatusRepository
                .findByDestinataireOrderByDateCreationDesc(userId);
        return notificationMapper.toResponseDTOList(statuses);
    }


    private String getEmailFromUserId(String userId) {
        return userService.getEmailFromUserId(userId);
    }

    private String getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof Jwt jwt) {

            String userId = jwt.getClaimAsString("sub");
            if (userId != null) {
                return userId;
            }

            return jwt.getClaimAsString("preferred_username");
        }
        return "SYSTEM";
    }

    public Boolean existNotificationWithEntityAndEntityIdAndDateCreation(String entity, Long id, LocalDateTime date) {
        LocalDateTime approximateDate = date.plusMonths(2L);
        return notificationRepository.getByEntityByEntityIdByDateCreation(entity, id, approximateDate) != null;
    }

}