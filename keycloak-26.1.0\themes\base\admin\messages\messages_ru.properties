invalidPasswordMinLengthMessage=Некорректный пароль: длина пароля должна быть не менее {0} символов(а).
invalidPasswordMinDigitsMessage=Некорректный пароль: должен содержать не менее {0} цифр(ы).
invalidPasswordMinLowerCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символов(а) в нижнем регистре.
invalidPasswordMinUpperCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символов(а) в верхнем регистре.
invalidPasswordMinSpecialCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} спецсимволов(а).
invalidPasswordNotUsernameMessage=Некорректный пароль: пароль не должен совпадать с именем пользователя.
invalidPasswordRegexPatternMessage=Некорректный пароль: пароль не прошел проверку по регулярному выражению.
invalidPasswordHistoryMessage=Некорректный пароль: пароль не должен совпадать с последним(и) {0} паролем(ями).
invalidPasswordGenericMessage=Некорректный пароль: новый пароль не соответствует правилам пароля.

ldapErrorInvalidCustomFilter=Сконфигурированный пользователем фильтр LDAP не должен начинаться с "(" или заканчиваться на ")".
ldapErrorMissingClientId=Client ID должен быть настроен в конфигурации, если не используется сопоставление ролей в realm.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Не удалось унаследовать группу и использовать членство UID типа вместе.
ldapErrorCantWriteOnlyForReadOnlyLdap=Невозможно установить режим "только на запись", когда LDAP провайдер не в режиме WRITABLE
ldapErrorCantWriteOnlyAndReadOnly=Невозможно одновременно установить режимы "только на чтение" и "только на запись"

clientRedirectURIsFragmentError=URI перенаправления не должен содержать фрагмент URI
clientRootURLFragmentError=Корневой URL не должен содержать фрагмент URL 

pairwiseMalformedClientRedirectURI=Клиент содержит некорректный URI перенаправления.
pairwiseClientRedirectURIsMissingHost=URI перенаправления клиента должен содержать корректный компонент хоста.
pairwiseClientRedirectURIsMultipleHosts=Без конфигурации по части идентификатора URI, URI перенаправления клиента не может содержать несколько компонентов хоста.
pairwiseMalformedSectorIdentifierURI=Искаженная часть идентификатора URI.
pairwiseFailedToGetRedirectURIs=Не удалось получить идентификаторы URI перенаправления из части идентификатора URI.
pairwiseRedirectURIsMismatch=Клиент URI переадресации не соответствует URI переадресации, полученной из части идентификатора URI.
