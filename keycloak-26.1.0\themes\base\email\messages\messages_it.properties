emailVerificationSubject=Verifica l''email
emailVerificationBody=Qualcuno ha creato un account {2} con questo indirizzo email. Se sei stato tu, fai clic sul link seguente per verificare il tuo indirizzo email\n\n{0}\n\nQuesto link scadrà in {3}.\n\nSe non sei stato tu a creare questo account, ignora questo messaggio.
emailVerificationBodyHtml=<p>Qualcuno ha creato un account {2} con questo indirizzo email. Se sei stato tu, fai clic sul link seguente per verificare il tuo indirizzo email</p><p><a href="{0}">Link per verificare l''indirizzo email</a></p><p>Questo link scadrà in {3}.</p><p>Se non sei stato tu a creare questo account, ignora questo messaggio.</p>
emailTestSubject=[KEYCLOAK] - messaggio di test SMTP
emailTestBody=Questo è un messaggio di test
emailTestBodyHtml=<p>Questo è un messaggio di test</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Qualcuno vuole associare il tuo account "{1}" con l''account "{0}" dell''utente {2}. Se sei stato tu, fai clic sul link seguente per associare gli account\n\n{3}\n\nQuesto link scadrà in {5}.\n\nSe non vuoi associare l''account, ignora questo messaggio. Se associ gli account, potrai accedere a {1} attraverso {0}.
identityProviderLinkBodyHtml=<p>Qualcuno vuole associare il tuo account <b>{1}</b> con l''account <b>{0}</b> dell''utente {2}. Se sei stato tu, fai clic sul link seguente per associare gli account</p><p><a href="{3}">{3}</a></p><p>Questo link scadrà in {5}.</p><p>Se non vuoi associare l''account, ignora questo messaggio. Se associ gli account, potrai accedere a {1} attraverso {0}.</p>
passwordResetSubject=Reimposta la password
passwordResetBody=Qualcuno ha appena richiesto di cambiare le credenziali di accesso al tuo account {2}. Se sei stato tu, fai clic sul link seguente per reimpostarle.\n\n{0}\n\nQuesto link e codice scadranno in {3}.\n\nSe non vuoi reimpostare le tue credenziali di accesso, ignora questo messaggio e non verrà effettuato nessun cambio.
passwordResetBodyHtml=<p>Qualcuno ha appena richiesto di cambiare le credenziali di accesso al tuo account {2}. Se sei stato tu, fai clic sul link seguente per reimpostarle.</p><p><a href="{0}">Link per reimpostare le credenziali</a></p><p>Questo link scadrà in {3}.</p><p>Se non vuoi reimpostare le tue credenziali di accesso, ignora questo messaggio e non verrà effettuato nessun cambio.</p>
executeActionsSubject=Aggiorna il tuo account
executeActionsBody=Il tuo amministratore ha appena richiesto un aggiornamento del tuo account {2} ed è necessario che tu esegua la/le seguente/i azione/i: {3}. Fai clic sul link seguente per iniziare questo processo.\n\n{0}\n\nQuesto link scadrà in {4}.\n\nSe non sei a conoscenza della richiesta del tuo amministratore, ignora questo messaggio e non verrà effettuato nessun cambio.
executeActionsBodyHtml=<p>Il tuo amministratore ha appena richiesto un aggiornamento del tuo account {2} ed è necessario che tu esegua la/le seguente/i azione/i: {3}. Fai clic sul link seguente per iniziare questo processo.</p><p><a href="{0}">Link per aggiornare l''account</a></p><p>Questo link scadrà in {4}.</p><p>Se non sei a conoscenza della richiesta del tuo amministratore, ignora questo messaggio e non verrà effettuato nessun cambio.</p>
eventLoginErrorSubject=Errore di accesso
eventLoginErrorBody=È stato rilevato un tentativo fallito di accesso al tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventLoginErrorBodyHtml=<p>È stato rilevato un tentativo fallito di accesso al tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>
eventRemoveTotpSubject=Rimozione OTP (password temporanea valida una volta sola)
eventRemoveTotpBody=La OTP (password temporanea valida una volta sola) è stata rimossa dal tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventRemoveTotpBodyHtml=<p>La OTP (password temporanea valida una volta sola) è stata rimossa dal tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>
eventUpdatePasswordSubject=Aggiornamento password
eventUpdatePasswordBody=La tua password è stata cambiata il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventUpdatePasswordBodyHtml=<p>La tua password è stata cambiata il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>
eventUpdateTotpSubject=Aggiornamento OTP (password temporanea valida una volta sola)
eventUpdateTotpBody=La OTP (password temporanea valida una volta sola) è stata aggiornata per il tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventUpdateTotpBodyHtml=<p>La OTP (password temporanea valida una volta sola) è stata aggiornata per il tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>

requiredAction.CONFIGURE_TOTP=Configurazione OTP
requiredAction.TERMS_AND_CONDITIONS=Termini e condizioni
requiredAction.UPDATE_PASSWORD=Aggiornamento password
requiredAction.UPDATE_PROFILE=Aggiornamento profilo
requiredAction.VERIFY_EMAIL=Verifica dell''indirizzo email

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#secondi|1#secondo|1<secondi}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minuti|1#minuto|1<minuti}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#ore|1#ora|1<ore}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#giorni|1#giorno|1<giorni}

emailVerificationBodyCode=Per favore verifica il tuo indirizzo email inserendo il codice seguente.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Per favore verifica il tuo indirizzo email inserendo il codice seguente.</p><p><b>{0}</b></p>

