/* Variables globales */
:root {
    /* Couleurs principales */
    --primary-color: #4318FF;
    --primary-dark: #3311CC;
    --secondary-color: #2B3674;
    
    /* Couleurs de texte */
    --text-primary: #2B3674;
    --text-secondary: #707EAE;
    
    /* Couleurs de fond */
    --bg-primary: #F4F7FE;
    --bg-white: #ffffff;
    --bg-secondary: #1B2559;
    
    /* Couleurs d'état */
    --success-color: #2E844A;
    --success-bg: #E6F5EC;
    --error-color: #FF4B4B;
    --error-bg: #FFF1F1;
    --warning-color: #FFB75D;
    --warning-bg: #FFF6E6;
    
    /* Bordures et ombres */
    --border-color: #E0E5F2;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.05);
    --shadow-lg: 0 8px 30px rgba(0,0,0,0.1);
    --shadow-focus: 0 0 0 2px rgba(67, 24, 255, 0.2);
    
    /* Espacements */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    
    /* Typographie */
    --font-size-xs: 0.8rem;
    --font-size-sm: 0.9rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.1rem;
    --font-size-xl: 1.8rem;
    --font-size-2xl: 2rem;
    --font-size-3xl: 2.5rem;
    
    /* Border radius */
    --radius-sm: 6px;
    --radius-md: 10px;
    --radius-lg: 20px;
    --radius-full: 50%;
    
    /* Transitions */
    --transition-base: all 0.3s ease;
    
    /* Layout */
    --header-height: 60px;
    --container-max-width: 1400px;
    --form-max-width: 400px;
    
    /* Breakpoints */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
    
    /* Breakpoints pour laptops */
    --breakpoint-laptop-sm: 1280px;
    --breakpoint-laptop-md: 1366px;
    --breakpoint-laptop-lg: 1440px;
    --breakpoint-laptop-xl: 1600px;

    /* Breakpoints verticaux */
    --breakpoint-height-xs: 480px;
    --breakpoint-height-sm: 600px;
    --breakpoint-height-md: 768px;
    --breakpoint-height-lg: 900px;
    --breakpoint-height-laptop: 800px;

    --image-border-radius: 12px;
    --image-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --image-transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

/* Main Layout */
.kc-login-class {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding-top: 80px;
    padding-bottom: 40px;
    background-color: var(--bg-primary);
    position: relative;
    overflow-x: hidden;
}

/* Ajustements pour les écrans de laptop */
@media (max-height: 800px) {
    .kc-login-class {
        padding-top: 80px; /* Réduit davantage pour les écrans de laptop avec hauteur limitée */
    }
}

/* Header/Navbar */
#kc-header {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 10;
    height: 60px;
}

#kc-header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    height: 100%;
}

.brand-logo {
    height: 40px;
    width: auto;
    border-radius: var(--radius-md);
    transition: var(--image-transition);
}

/* Site logo above form */
.site-logo {
    height: 60px;
    width: auto;
    margin-bottom: 2rem;
    display: block;
    margin: 0 auto 2rem;
    border-radius: var(--radius-md);
    transition: var(--image-transition);
}

/* Main content wrapper */
#kc-content {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: space-between;
    width: 85%;
    max-width: 1200px;
    margin: 0 auto;
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    height: auto;
    min-height: 500px;
}

/* Ajustements pour les écrans de laptop */
@media (max-width: 1366px) {
    #kc-content {
        max-width: 90%;
        gap: var(--spacing-lg);
        margin: 1.5rem auto;
    }
}

@media (max-height: 768px) {
    #kc-content {
        margin: 1rem auto;
    }
}

@media (max-width: 992px) {
    #kc-content {
        flex-direction: column;
        width: 90%;
        max-width: 500px;
        overflow: visible;
        min-height: auto;
    }
}

#kc-form-wrapper {
    flex: 0 0 450px;
    width: 450px;
    max-width: 450px;
    padding: var(--spacing-xl);
    background-color: var(--bg-white);
    border-radius: 0;
    box-shadow: none;
    transition: var(--transition-base);
    border: none;
    display: flex;
    flex-direction: column;
    height: 100%;
}

#login-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

/* Ajustements pour les écrans de laptop */
@media (max-width: 1366px) {
    #kc-form-wrapper {
        padding: var(--spacing-lg);
    }
}

@media (max-height: 768px) {
    #kc-form-wrapper {
        padding: var(--spacing-md) var(--spacing-lg);
    }
}

@media (max-width: 576px) {
    #kc-form-wrapper {
        padding: var(--spacing-md);
        max-width: 100%;
    }
}

/* Form header styling */
.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

@media (max-height: 768px) {
    .form-header {
        margin-bottom: 1.5rem;
    }
}

.form-logo {
    width: 60px;
    height: auto;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.form-title {
    font-size: 2rem;
    color: #2B3674;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

@media (max-width: 1366px) {
    .form-title {
        font-size: 1.8rem;
    }
}

@media (max-height: 768px) {
    .form-title {
        font-size: 1.6rem;
        margin-bottom: 0.4rem;
    }
}

@media (max-height: 600px) {
    .form-title {
        font-size: 1.4rem;
        margin-bottom: 0.3rem;
    }
}

.form-subtitle {
    color: #707EAE;
    font-size: 1rem;
    margin-bottom: 2rem;
}

@media (max-width: 1366px) {
    .form-subtitle {
        font-size: 0.95rem;
        margin-bottom: 1.75rem;
    }
}

@media (max-height: 768px) {
    .form-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
}

@media (max-height: 600px) {
    .form-subtitle {
        font-size: 0.85rem;
        margin-bottom: 1.25rem;
    }
}

/* Form container */
#kc-form {
    flex: 1;
    max-width: 450px;
    width: 100%;
    padding: var(--spacing-xl);
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
    border: 1px solid var(--border-color);
}

#kc-form-wrapper {
    flex: 1;
    max-width: 450px;
    width: 100%;
    padding: var(--spacing-xl);
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
    border: 1px solid var(--border-color);
}

/* Welcome text */
#kc-page-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: #2B3674;
    margin-bottom: 2rem;
}

/* Form inputs */
.kc-form-group {
    margin-bottom: 1.5rem;
}

.kc-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2B3674;
    font-weight: 500;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    height: 56px;
    padding: 1.25rem 2.5rem 0.5rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-white);
    transition: var(--transition-base);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    z-index: 1;
}

input[type="text"]:focus,
input[type="password"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-focus);
}

/* Submit button */
input[type="submit"] {
    width: 100%;
    padding: 1rem;
    background: #4318FF;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1rem;
    margin-top: 1rem;
}

input[type="submit"]:hover {
    background: #3311CC;
    transform: translateY(-1px);
}

/* Right side - Illustration */
.login-illustration {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-light);
    overflow: hidden;
}

.login-illustration img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
}

/* Proportional relationship between form and illustration */
@media (min-width: 992px) {
    #kc-content {
        height: calc(100vh - 250px);
        max-height: 800px;
        align-items: stretch;
    }
    
    #kc-form-wrapper, 
    .login-illustration {
        height: 100%;
    }
}

/* Responsive styles for login illustration */
@media (orientation: portrait) {
    #kc-content {
        flex-direction: column-reverse;
        height: auto;
        width: 90%;
    }
    
    #kc-form-wrapper {
        max-width: 100%;
        width: 100%;
    }
    
    .login-illustration {
        min-height: 180px;
        max-height: 25vh;
        width: 100%;
        margin-bottom: 2rem;
        height: auto;
    }
    
    .login-illustration img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

@media (orientation: portrait) and (min-height: 1000px) {
    .login-illustration {
        min-height: 250px;
        max-height: 30vh;
    }
}

@media (orientation: portrait) and (max-height: 800px) {
    .login-illustration {
        min-height: 160px;
        max-height: 22vh;
    }
    
    #kc-form-wrapper {
        padding: var(--spacing-lg) var(--spacing-lg);
    }
}

@media (orientation: portrait) and (max-height: 700px) {
    .login-illustration {
        min-height: 140px;
        max-height: 20vh;
        margin-bottom: 1rem;
    }
    
    #kc-form-wrapper {
        padding: var(--spacing-md) var(--spacing-lg);
    }
}

@media (orientation: portrait) and (max-height: 600px) {
    .login-illustration {
        min-height: 120px;
        max-height: 18vh;
        margin-bottom: 0.5rem;
    }
    
    #kc-form-wrapper {
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

@media (max-width: 576px) and (orientation: portrait) {
    #kc-content {
        width: 95%;
        margin: 1rem auto;
    }
    
    #kc-form-wrapper {
        padding: var(--spacing-sm);
    }
    
    .login-illustration {
        min-height: 100px;
        max-height: 15vh;
        margin-bottom: 0.5rem;
    }
}

/* Remember me checkbox */
.checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.checkbox label {
    color: #2B3674;
    font-size: 0.9rem;
}

/* Forgot password link */
.kc-form-options-wrapper a {
    color: #4318FF;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Language selector */
#kc-locale {
    position: absolute;
    top: 1rem;
    right: 2rem;
}

/* Alert System */
.alert-system {
    position: fixed;
    top: calc(var(--header-height) + 1rem);
    right: 1rem;
    z-index: 999;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    pointer-events: none;
    max-width: 400px;
    gap: 0.5rem;
}

.alert {
    pointer-events: auto;
    position: relative;
    padding: 1rem 1.25rem;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transform: translateX(100%);
    animation: alertSlideInRight 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    width: 100%;
    overflow: hidden;
    margin: 0;
}

.alert-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
    min-width: 0;
}

.alert-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    color: white;
}

.alert-text {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    margin: 0;
    word-wrap: break-word;
}

/* Success Alert */
.alert.alert-success {
    background: linear-gradient(145deg, rgba(46, 132, 74, 0.95), rgba(46, 132, 74, 0.85));
    border: 1px solid rgba(46, 132, 74, 0.2);
}

/* Error Alert */
.alert.alert-error {
    background: linear-gradient(145deg, rgba(255, 75, 75, 0.95), rgba(255, 75, 75, 0.85));
    border: 1px solid rgba(255, 75, 75, 0.2);
}

/* Warning Alert */
.alert.alert-warning {
    background: linear-gradient(145deg, rgba(255, 183, 93, 0.95), rgba(255, 183, 93, 0.85));
    border: 1px solid rgba(255, 183, 93, 0.2);
}

/* Info Alert */
.alert.alert-info {
    background: linear-gradient(145deg, rgba(67, 24, 255, 0.95), rgba(67, 24, 255, 0.85));
    border: 1px solid rgba(67, 24, 255, 0.2);
}

/* Close Button */
.alert-close {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.alert-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.alert-close::before,
.alert-close::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 12px;
    background: white;
    border-radius: 1px;
}

.alert-close::before {
    transform: rotate(45deg);
}

.alert-close::after {
    transform: rotate(-45deg);
}

/* Progress Bar */
.alert-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.alert-progress-bar {
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    width: 100%;
    animation: alertProgress 5s linear forwards;
}

/* Animations */
@keyframes alertSlideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    60% {
        transform: translateX(-10%);
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes alertSlideOutRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes alertProgress {
    0% {
        width: 100%;
    }
    100% {
        width: 0;
    }
}

.alert.hiding {
    animation: alertSlideOutRight 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .alert {
        max-width: calc(100vw - 2rem);
        margin: 0;
    }

    .alert-title {
        font-size: 0.9rem;
    }

    .alert-text {
        font-size: 0.8rem;
    }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
    .alert {
        background: rgba(27, 37, 89, 0.95);
    }
}

/* Info Section Styling */
/* Commenté pour masquer la section info
#kc-info-wrapper:empty {
    display: none;
}

#kc-info-wrapper {
    position: relative;
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

/* Info Links */
#kc-info-wrapper a {
    color: #4318FF;
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
}

#kc-info-wrapper a:hover {
    color: #3311CC;
}

#kc-info-wrapper a::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: currentColor;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

#kc-info-wrapper a:hover::after {
    transform: scaleX(1);
}


/* User Info Banner */
.user-info-banner {
    width: 100%;
    background: #F4F7FE;
    padding: 1.5rem;
    margin: 2rem auto 0;
    border-radius: 15px;
    max-width: 1400px;
}

.user-info-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0 2rem;
}

/* User avatar and icon */
.user-avatar {
    width: 36px;
    height: 36px;
    background: #F4F7FE;
    border-radius: var(--radius-full);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--image-transition);
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--image-shadow);
}

.user-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}


.user-icon::before {
    content: none;
}

.user-details {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.welcome-text {
    color: #707EAE;
    font-size: 0.8rem;
}

.username {
    color: #2B3674;
    font-size: 0.9rem;
    font-weight: 600;
}

.switch-account {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #4318FF;
    text-decoration: none;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    background: #F4F7FE;
    transition: all 0.3s ease;
}

.switch-account:hover {
    background: #EEF1FA;
}

.switch-icon::before {
    content: '';
    display: block;
    width: 20px;
    height: 20px;
    /* background: url(${url.resourcesPath}/img/switch-icon.svg) no-repeat center; */
}

/* Responsive design */
@media (max-width: 1200px) {
    #kc-content {
        max-width: 95%;
    }

    .login-illustration {
        min-height: 350px;
    }
}

@media (max-width: 992px) {
    #kc-content {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
    }

    #kc-form-wrapper {
        max-width: 100%;
    }

    .login-illustration {
        display: none;
    }

    /* Navbar responsive */
    #kc-header-wrapper {
        padding: 0 1rem;
    }

    .user-info-content {
        margin: 0 1rem;
    }

    .user-details {
        display: none;
    }

    .switch-account span {
        display: none;
    }

    #kc-totp-secret-qr-code {
        max-width: 180px;
    }
}

@media (max-width: 768px) {
    #kc-content {
        margin: var(--spacing-md) auto;
        padding: 0 var(--spacing-md);
    }

    #kc-form-wrapper {
        padding: var(--spacing-lg);
    }

    .form-floating {
        margin-bottom: var(--spacing-sm);
    }
}

@media (max-width: 576px) {
    #kc-content {
        margin: var(--spacing-sm) auto;
        padding: 0 var(--spacing-sm);
    }

    #kc-form-wrapper {
        padding: var(--spacing-md);
    }

    .form-control {
        height: 52px;
        font-size: var(--font-size-sm);
    }

    .form-label {
        font-size: var(--font-size-sm);
    }

    .form-options {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
    #kc-form-wrapper,
    .login-illustration {
        background-color: var(--bg-secondary);
    }

    .form-control {
        background-color: var(--bg-secondary);
        color: var(--text-primary);
    }

    .form-control:focus ~ .form-label,
    .form-control:not(:placeholder-shown) ~ .form-label {
        background-color: var(--bg-secondary);
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#kc-form-wrapper {
    animation: fadeIn 0.6s ease-out;
}

.login-illustration {
    animation: fadeIn 0.8s ease-out;
}

/* Reset Password specific styles */
#kc-reset-password-form {
    width: 100%;
}

#kc-form-options {
    margin-top: 1.5rem;
    text-align: center;
}

#kc-form-options a {
    color: #4318FF;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 6px;
}

#kc-form-options a:hover {
    background: #F4F7FE;
}

/* Message d'erreur */
.kc-input-error-message {
    color: #FF4B4B;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: block;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    #kc-reset-password-form {
        padding: 0;
    }

    #kc-form-options {
        margin-top: 1rem;
    }
}

/* Update Password specific styles */
#kc-passwd-update-form {
    width: 100%;
}

.password-requirements {
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(67, 24, 255, 0.05);
    border-radius: 10px;
    font-size: 0.9rem;
    color: #2B3674;
}

.password-requirements ul {
    list-style: none;
    margin: 0.5rem 0 0;
    padding: 0;
}

.password-requirements li {
    margin: 0.3rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.password-requirements li::before {
    content: '•';
    color: #4318FF;
}

/* Password strength indicator */
.password-strength {
    margin-top: 0.5rem;
    height: 4px;
    background: #E0E5F2;
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    width: 0;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-weak {
    background-color: #FF4B4B;
    width: 33%;
}

.strength-medium {
    background-color: #FFB75D;
    width: 66%;
}

.strength-strong {
    background-color: #2E844A;
    width: 100%;
}

/* TOTP specific styles */
#kc-totp-settings {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

#kc-totp-settings li {
    margin-bottom: 1.5rem;
}

#kc-totp-settings p {
    font-size: 0.9rem;
    color: #707EAE;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

/* QR Code styling */
#kc-totp-secret-qr-code {
    display: block;
    max-width: 200px;
    height: auto;
    margin: 1.5rem auto;
    border-radius: var(--radius-md);
    padding: 1rem;
    background: white;
    box-shadow: var(--shadow-sm);
    transition: var(--image-transition);
}

#kc-totp-secret-qr-code:hover {
    transform: scale(1.02);
    box-shadow: var(--image-shadow);
}

/* Applications supportées */
#kc-totp-supported-apps {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0;
    font-size: 0.85rem;
    color: #2B3674;
}

#kc-totp-supported-apps li {
    margin: 0.3rem 0;
    padding-left: 1rem;
    position: relative;
}

#kc-totp-supported-apps li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #4318FF;
}

/* Secret key styling */
#kc-totp-secret-key {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: #F4F7FE;
    border-radius: 8px;
    font-family: monospace;
    font-size: 0.9rem;
    color: #2B3674;
    margin: 0.5rem 0;
}

/* Styles pour la page de confirmation */
.info-success-page {
    max-width: 500px;
    margin: 0 auto;
    padding: 2.5rem;
    background: #F4F7FE;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    text-align: center;
}

/* Icône de succès */
.info-icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: #E6F5EC;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.success-icon {
    width: 40px;
    height: 40px;
    /* background: url(${url.resourcesPath}/img/success-check.svg) no-repeat center; */
    background-size: contain;
}

/* Titre du message */
.info-title {
    color: #2B3674;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 2rem;
    line-height: 1.4;
}

/* Wrapper du bouton de retour */
.back-to-app-wrapper {
    margin-top: 2rem;
}

/* Bouton de retour à l'application */
.back-to-app-button {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: #4318FF;
    color: white;
    text-decoration: none;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-to-app-button:hover {
    background: #3311CC;
    transform: translateY(-1px);
}

/* Icône de retour */
.back-icon {
    width: 20px;
    height: 20px;
    /* background: url(${url.resourcesPath}/img/arrow-left.svg) no-repeat center; */
    background-size: contain;
}

/* Responsive design */
@media (max-width: 768px) {
    .info-success-page {
        padding: 2rem;
        margin: 1rem;
    }

    .info-icon-wrapper {
        width: 70px;
        height: 70px;
        margin-bottom: 1.5rem;
    }

    .success-icon {
        width: 35px;
        height: 35px;
    }

    .info-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 576px) {
    .info-success-page {
        padding: 1.5rem;
        margin: 0.5rem;
        border-radius: 15px;
    }

    .info-icon-wrapper {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
    }

    .success-icon {
        width: 30px;
        height: 30px;
    }

    .info-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .back-to-app-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Footer Styles */
.kc-footer {
    background: var(--bg-white);
    padding: 3rem 0 0;
    margin-top: auto;
    width: 100%;
    border-top: 1px solid var(--border-color);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-main {
    display: flex;
    justify-content: space-between;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand {
    flex: 0 0 300px;
}

.footer-logo {
    height: 40px;
    width: auto;
    margin-bottom: 1rem;
    border-radius: var(--radius-md);
    transition: var(--image-transition);
}

.footer-logo:hover {
    transform: translateY(-2px);
    box-shadow: var(--image-shadow);
}

.footer-description {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.6;
}

.footer-nav {
    display: flex;
    gap: 4rem;
}

.footer-nav-column {
    min-width: 160px;
}

.footer-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-copyright {
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 0;
    margin-top: 2rem;
    background: var(--bg-white);
}

.copyright-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
}

/* Dark mode footer adjustments */
@media (prefers-color-scheme: dark) {
    .kc-footer {
        background: var(--bg-white);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-copyright {
        background: var(--bg-white);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* Responsive Footer */
@media (max-width: 992px) {
    .footer-main {
        flex-direction: column;
        gap: 3rem;
    }

    .footer-brand {
        flex: 0 0 auto;
        text-align: center;
    }

    .footer-nav {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .kc-footer {
        padding: 2rem 0 0;
    }

    .footer-nav {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .footer-nav-column {
        min-width: auto;
    }

    .footer-title {
        margin-bottom: 1rem;
    }

    .footer-links li {
        margin-bottom: 0.6rem;
    }
}

@media (max-width: 576px) {
    .footer-container {
        padding: 0 1rem;
    }

    .copyright-content {
        padding: 0 1rem;
    }

    .footer-description {
        font-size: 0.9rem;
    }
}

/* Animation pour les liens */
.footer-links a {
    position: relative;
}

.footer-links a::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: -2px;
    left: 0;
    background-color: #4318FF;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.footer-links a:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

/* Styles pour toutes les images */
img {
    border-radius: var(--image-border-radius);
    transition: var(--image-transition);
}

/* Effet au survol pour les images interactives */
a img:hover, 
button img:hover,
.brand-logo:hover {
    transform: translateY(-2px);
    box-shadow: var(--image-shadow);
}

/* Styles spécifiques pour les logos */
.brand-logo {
    border-radius: var(--radius-md);
    transition: var(--image-transition);
}

/* Exception pour le logo OTBS */
img[src*="OTBS.png"] {
    border-radius: 0;
}

/* Styles pour l'avatar utilisateur */
.user-avatar {
    border-radius: var(--radius-full);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--image-transition);
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--image-shadow);
}

/* Style pour le QR code */
#kc-totp-secret-qr-code {
    border-radius: var(--radius-md);
    transition: var(--image-transition);
    box-shadow: var(--shadow-sm);
}

#kc-totp-secret-qr-code:hover {
    transform: scale(1.02);
    box-shadow: var(--image-shadow);
}

/* Style pour le logo du site */
.site-logo {
    border-radius: var(--radius-md);
    transition: var(--image-transition);
}

.site-logo:hover {
    transform: translateY(-2px);
    box-shadow: var(--image-shadow);
}

/* Style pour le logo du footer */
.footer-logo {
    border-radius: var(--radius-md);
    transition: var(--image-transition);
}

.footer-logo:hover {
    transform: translateY(-2px);
    box-shadow: var(--image-shadow);
}

.form-floating {
    position: relative;
    margin-bottom: 1.5rem;
    width: 100%;
}

.form-control {
    width: 100%;
    height: 56px;
    padding: 1.375rem 1rem 0.375rem 2.5rem;
    font-size: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-white);
    transition: var(--transition-base);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-focus);
}

/* Styles des icônes */
.form-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.6;
    transition: var(--transition-base);
    pointer-events: none;
    z-index: 2;
}

.form-control:focus ~ .form-icon {
    opacity: 1;
}

.username-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234318FF'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.password-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234318FF'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z'/%3E%3C/svg%3E");
}

.email-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234318FF'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z'/%3E%3C/svg%3E");
}

.otp-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234318FF'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
}

/* Style du label */
.form-floating label {
    position: absolute;
    left: 2.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: 0 0;
    padding: 0;
    margin: 0;
    background: none;
    z-index: 2;
}

.form-control:focus ~ label,
.form-control:not(:placeholder-shown) ~ label {
    top: 0.5rem;
    transform: translateY(0) scale(0.75);
    color: var(--primary-color);
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
    .form-control {
        background: var(--bg-secondary);
        color: var(--text-primary);
    }
    
    .form-control:focus ~ label,
    .form-control:not(:placeholder-shown) ~ label {
        color: var(--primary-color);
    }

    .form-icon {
        opacity: 0.8;
    }
}

/* Components */

/* Cards et conteneurs */
.content-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Headers de section */
.section-header {
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.section-title {
    font-size: var(--font-size-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.section-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* Badges et étiquettes */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.badge-primary {
    background: rgba(67, 24, 255, 0.1);
    color: var(--primary-color);
}

.badge-success {
    background: var(--success-bg);
    color: var(--success-color);
}

.badge-error {
    background: var(--error-bg);
    color: var(--error-color);
}

/* Listes */
.list-group {
    border-radius: var(--radius-md);
    overflow: hidden;
}

.list-group-item {
    padding: var(--spacing-md);
    background: var(--bg-white);
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item:hover {
    background: var(--bg-primary);
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem 1rem;
    background: var(--bg-secondary);
    color: white;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: 1000;
}

/* Spinners et loaders */
.spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Dividers */
.divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: var(--spacing-md) 0;
    color: var(--text-secondary);
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--border-color);
}

.divider::before {
    margin-right: var(--spacing-sm);
}

.divider::after {
    margin-left: var(--spacing-sm);
}

/* Progress bars */
.progress {
    height: 8px;
    background: var(--bg-primary);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--primary-color);
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
}

/* Avatars */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 2px solid var(--bg-white);
    box-shadow: var(--shadow-sm);
}

.avatar-group {
    display: flex;
    align-items: center;
}

.avatar-group .avatar {
    margin-left: -8px;
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.tab {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tab:hover {
    color: var(--text-primary);
}

.tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .content-card,
    .list-group-item {
        background: var(--bg-secondary);
    }
    
    .list-group-item:hover {
        background: rgba(255, 255, 255, 0.05);
    }
    
    .divider::before,
    .divider::after {
        border-color: var(--border-color);
    }
    
    .progress {
        background: rgba(255, 255, 255, 0.1);
    }
    
    .avatar {
        border-color: var(--bg-secondary);
    }
}

/* Buttons */
.form-buttons {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    width: 100%;
}

.login-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-white);
    background-color: var(--primary-color);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-base);
}

.login-button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.login-button:active {
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-height: 768px) {
    .login-button {
        padding: 0.7rem 1.25rem;
        font-size: 0.95rem;
    }
}

@media (max-height: 600px) {
    .login-button {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}

/* Optimisations pour les écrans avec ratio d'aspect différent */
@media (min-aspect-ratio: 16/9) {
    .kc-login-class {
        padding-top: 70px;
    }
    
    #kc-content {
        margin: 1rem auto;
    }
    
    .login-illustration img {
        max-height: 80vh;
    }
}

@media (max-aspect-ratio: 4/3) {
    .login-illustration {
        max-width: 40%;
    }
    
    #kc-form-wrapper {
        max-width: 55%;
    }
}

/* Optimisations pour les écrans de laptop avec résolutions spécifiques */
@media (min-width: 1280px) and (max-width: 1366px) and (min-height: 720px) and (max-height: 768px) {
    .kc-login-class {
        padding-top: 70px;
    }
    
    #kc-content {
        max-width: 90%;
        margin: 1.25rem auto;
    }
    
    #kc-form-wrapper {
        padding: var(--spacing-lg);
    }
    
    .form-header {
        margin-bottom: 1.5rem;
    }
    
    .form-logo {
        width: 50px;
        margin-bottom: 1rem;
    }
}

/* Optimisations pour les écrans de laptop haute résolution */
@media (min-width: 1440px) and (min-height: 900px) {
    #kc-content {
        max-width: 80%;
        margin: 2rem auto;
    }
    
    .login-illustration img {
        max-height: 70vh;
    }
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    font-size: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-white);
    transition: var(--transition-base);
}

@media (max-width: 1366px) {
    .form-control {
        padding: 0.7rem 0.9rem 0.7rem 2.3rem;
        font-size: 0.95rem;
    }
}

@media (max-height: 768px) {
    .form-control {
        padding: 0.65rem 0.85rem 0.65rem 2.2rem;
        font-size: 0.9rem;
    }
}

@media (max-height: 600px) {
    .form-control {
        padding: 0.6rem 0.8rem 0.6rem 2.1rem;
        font-size: 0.85rem;
    }
}

.form-floating label {
    position: absolute;
    top: 0;
    left: 2.5rem;
    height: 100%;
    padding: 0.75rem 0;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: var(--transition-base);
    color: var(--text-secondary);
    font-size: 1rem;
}

@media (max-width: 1366px) {
    .form-floating label {
        left: 2.3rem;
        padding: 0.7rem 0;
        font-size: 0.95rem;
    }
}

@media (max-height: 768px) {
    .form-floating label {
        left: 2.2rem;
        padding: 0.65rem 0;
        font-size: 0.9rem;
    }
}

@media (max-height: 600px) {
    .form-floating label {
        left: 2.1rem;
        padding: 0.6rem 0;
        font-size: 0.85rem;
    }
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.register-link {
    margin-top: 1.5rem;
    text-align: center;
    width: 100%;
}
  