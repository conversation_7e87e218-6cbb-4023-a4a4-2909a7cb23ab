<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('totp'); section>
    <#if section="header">
        
    <#elseif section="form">
        <form id="kc-otp-login-form" class="${properties.kcFormClass!}" onsubmit="login.disabled = true; return true;" action="${url.loginAction}"
            method="post">
            <img src="${url.resourcesPath}/img/OTBS.png" alt="Onetech Logo" id="logo"><br>
            <span id="title">${msg("Authentification à deux facteurs")}</span>
            
            <div class="otp-instructions">
                <p>Veuillez entrer le code à usage unique généré par votre application d'authentification .</p>
            </div>
            
            <#if otpLogin.userOtpCredentials?size gt 1>
                <div class="${properties.kcFormGroupClass!}">
                    <div class="credential-selector-label">
                        <label>Sélectionnez votre appareil d'authentification</label>
                    </div>
                    <div class="credential-selector-wrapper">
                        <select id="credential-selector" class="credential-selector" onchange="updateSelectedCredential(this.value)">
                            <#list otpLogin.userOtpCredentials as otpCredential>
                                <option value="${otpCredential.id}" <#if otpCredential.id == otpLogin.selectedCredentialId>selected</#if>>${otpCredential.userLabel}</option>
                            </#list>
                        </select>
                        <div class="credential-selector-icon">
                            <i class="fa fa-chevron-down"></i>
                        </div>
                    </div>
                    
                    <!-- Champs cachés pour stocker la sélection -->
                    <#list otpLogin.userOtpCredentials as otpCredential>
                        <input id="kc-otp-credential-${otpCredential?index}" class="hidden-credential-input" type="radio" name="selectedCredentialId" value="${otpCredential.id}" <#if otpCredential.id == otpLogin.selectedCredentialId>checked="checked"</#if>>
                    </#list>
                </div>
                
                <script>
                    function updateSelectedCredential(credentialId) {
                        // Décocher tous les radios
                        document.querySelectorAll('.hidden-credential-input').forEach(function(radio) {
                            radio.checked = false;
                        });
                        
                        // Cocher le radio correspondant à la sélection
                        document.querySelector('.hidden-credential-input[value="' + credentialId + '"]').checked = true;
                    }
                </script>
            </#if>
            
            <!-- Ajouter un conteneur parent -->
            <div id="kc-form-bottom-right-container">
                <!-- Groupe OTP -->
                <div class="${properties.kcFormGroupClass!}" id="kc-otp-group">
                    <div class="${properties.kcLabelWrapperClass!}" id="kc-otp-label-wrapper">
                        <label for="kc-otp-input" class="${properties.kcLabelClass!}" id="kc-otp-label">
                            Code de vérification
                        </label>
                    </div>

                    <div class="${properties.kcInputWrapperClass!}" id="kc-otp-input-wrapper">
                        <input id="kc-otp-input" name="otp" type="text"
                               class="${properties.kcInputClass!}"
                               autocomplete="one-time-code"
                               placeholder="Entrez le code à 6 chiffres"
                               autofocus
                               aria-invalid="<#if messagesPerField.existsError('totp')>true</#if>"
                               dir="ltr" />

                        <#if messagesPerField.existsError('totp')>
                            <span id="kc-otp-error" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                ${kcSanitize(messagesPerField.get('totp'))?no_esc}
                            </span>
                        </#if>
                    </div>
                </div>

                <!-- Bouton Submit -->
                <div class="${properties.kcFormGroupClass!}" id="kc-submit-group">
                    <div id="kc-form-buttons" class="${properties.kcFormButtonsClass!}">
                        <input type="submit"
                               id="kc-login-submit"
                               class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!}"
                               value="Vérifier" />
                    </div>
                </div>
                
                <div class="otp-help-text">
                    <p>Problème avec votre code ? <a href="${url.loginRestartFlowUrl}">Recommencer</a> ou contactez l'administrateur.</p>
                </div>
            </div>
        </form>
    </#if>
</@layout.registrationLayout>