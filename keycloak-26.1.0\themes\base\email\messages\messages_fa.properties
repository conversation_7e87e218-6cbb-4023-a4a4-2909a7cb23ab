emailVerificationSubject=تایید ایمیل
emailVerificationBody=شخصی با این آدرس ایمیل یک حساب {2} ایجاد کرده است. اگر این شما بودید، روی پیوند زیر کلیک کنید تا آدرس ایمیل خود را تأیید کنید\n\n{0}\n\nاین پیوند در {3} منقضی می‌شود.\n\nاگر شما این حساب را ایجاد نکرده‌اید، فقط این پیام را نادیده بگیرید .
emailVerificationBodyHtml=<p>شخصی با این آدرس ایمیل یک حساب {2} ایجاد کرده است. اگر این شما بودید، برای تأیید آدرس ایمیل خود روی پیوند زیر کلیک کنید</p><p><a href="{0}">پیوند به تأیید آدرس ایمیل</a></p><p>این پیوند در {3} منقضی می‌شود.</p><p>اگر شما این حساب را ایجاد نکرده‌اید، فقط این پیام را نادیده بگیرید.</p>
emailUpdateConfirmationSubject=تایید ایمیل جدید
emailUpdateConfirmationBody=برای به‌روزرسانی حساب {2} خود با آدرس ایمیل {1}، روی پیوند زیر کلیک کنید\n\n{0}\n\nاین پیوند ظرف {3} منقضی می‌شود.\n\nاگر نمی‌خواهید ادامه دهید این تغییر، فقط این پیام را نادیده بگیرید.
emailUpdateConfirmationBodyHtml=<p>برای به‌روزرسانی حساب {2} خود با آدرس ایمیل {1}، روی پیوند زیر کلیک کنید</p><p><a href="{0}">{0}</a></p>< p>این پیوند در مدت {3} منقضی می‌شود.</p><p>اگر نمی‌خواهید به این اصلاح ادامه دهید، فقط این پیام را نادیده بگیرید.</p>
emailTestSubject=[KEYCLOAK] - پیام آزمایشی SMTP
emailTestBody=این یک پیام آزمایشی است
emailTestBodyHtml=<p>این یک پیام آزمایشی است</p>
identityProviderLinkSubject=پیوند {0}
identityProviderLinkBody=شخصی می خواهد حساب "{1}" شما را با حساب "{0}" کاربر {2} پیوند دهد. اگر این شما بودید، برای پیوند دادن حساب‌ها روی پیوند زیر کلیک کنید\n\n{3}\n\nاین پیوند در {5} منقضی می‌شود.\n\nاگر نمی‌خواهید حساب را پیوند دهید، فقط این پیام را نادیده بگیرید. اگر حساب‌ها را پیوند دهید، می‌توانید از طریق {0} به {1} وارد شوید.
identityProviderLinkBodyHtml=<p>شخصی می خواهد حساب <b>{1}</b> شما را به حساب <b>{0}</b> کاربر {2} پیوند دهد. اگر این شما بودید، برای پیوند دادن حساب‌ها روی پیوند زیر کلیک کنید</p><p><a href="{3}">پیوند برای تأیید پیوند حساب</a></p><p>این پیوند در مدت زمان منقضی می‌شود. {5}.</p><p>اگر نمی‌خواهید حساب را پیوند دهید، فقط این پیام را نادیده بگیرید. اگر حساب‌ها را پیوند دهید، می‌توانید از طریق {0} به {1} وارد شوید.</p>
passwordResetSubject=بازنشانی رمز عبور
passwordResetBody=شخصی به تازگی درخواست کرده است که اعتبارنامه حساب {2} شما را تغییر دهد. اگر این شما بودید، روی پیوند زیر کلیک کنید تا آنها را بازنشانی کنید.\n\n{0}\n\nاین پیوند و کد در مدت {3} منقضی می‌شوند.\n\nاگر نمی‌خواهید اطلاعات کاربری خود را بازنشانی کنید، فقط این پیام را نادیده بگیرید و چیزی تغییر نخواهد کرد.
passwordResetBodyHtml=<p>شخصی به تازگی درخواست تغییر اعتبار حساب {2} شما را داده است. اگر این شما بودید، روی پیوند زیر کلیک کنید تا آنها را بازنشانی کنید.</p><p><a href="{0}">پیوند به بازنشانی اعتبارنامه</a></p><p>این پیوند منقضی می‌شود. در {3}.</p><p>اگر نمی‌خواهید اعتبارنامه خود را بازنشانی کنید، فقط این پیام را نادیده بگیرید و چیزی تغییر نخواهد کرد.</p>
executeActionsSubject=اکانت خود را به روز کنید
executeActionsBody=سرپرست شما به تازگی درخواست کرده است که حساب {2} خود را با انجام اقدامات زیر به روز کنید: {3}. برای شروع این فرآیند روی پیوند زیر کلیک کنید.\n\n{0}\n\nاین پیوند در مدت {4} منقضی می‌شود.\n\nاگر نمی‌دانید که سرپرست شما این درخواست را کرده است، فقط این پیام را نادیده بگیرید و هیچ چیز انجام نخواهد شد. تغییر یافته.
executeActionsBodyHtml=<p>سرپرست شما به تازگی درخواست کرده است که حساب {2} خود را با انجام اقدامات زیر به روز کنید: {3}. برای شروع این فرآیند روی پیوند زیر کلیک کنید.</p><p><a href="{0}">پیوند به به‌روزرسانی حساب</a></p><p>این پیوند در {4} منقضی می‌شود. .</p><p>اگر نمی دانید که سرپرست شما این درخواست را کرده است، فقط این پیام را نادیده بگیرید و چیزی تغییر نخواهد کرد.</p>
eventLoginErrorSubject=خطای ورود
eventLoginErrorBody=یک تلاش ناموفق برای ورود به حساب شما در {0} از {1} شناسایی شد. اگر این شما نبودید، لطفا با یک مدیر تماس بگیرید.
eventLoginErrorBodyHtml=<p>یک تلاش ناموفق برای ورود به حساب شما در {0} از {1} شناسایی شد. اگر این شما نبودید، لطفاً با یک سرپرست تماس بگیرید.</p>
eventRemoveTotpSubject=OTP را حذف کنید
eventRemoveTotpBody=OTP در تاریخ {0} از {1} از حساب شما حذف شد. اگر این شما نبودید، لطفا با یک مدیر تماس بگیرید.
eventRemoveTotpBodyHtml=<p>OTP از حساب شما در {0} از {1} حذف شد. اگر این شما نبودید، لطفاً با یک سرپرست تماس بگیرید.</p>
eventUpdatePasswordSubject=رمز عبور را به روز کنید
eventUpdatePasswordBody=رمز عبور شما در {0} از {1} تغییر کرد. اگر این شما نبودید، لطفا با یک مدیر تماس بگیرید.
eventUpdatePasswordBodyHtml=<p>گذرواژه شما در {0} از {1} تغییر کرد. اگر این شما نبودید، لطفاً با یک سرپرست تماس بگیرید.</p>
eventUpdateTotpSubject=OTP را به روز کنید
eventUpdateTotpBody=OTP برای حساب شما در {0} از {1} به روز شد. اگر این شما نبودید، لطفا با یک مدیر تماس بگیرید.
eventUpdateTotpBodyHtml=<p>OTP برای حساب شما در {0} از {1} به روز شد. اگر این شما نبودید، لطفاً با یک سرپرست تماس بگیرید.</p>

requiredAction.CONFIGURE_TOTP=OTP را پیکربندی کنید
requiredAction.TERMS_AND_CONDITIONS=شرایط و ضوابط
requiredAction.UPDATE_PASSWORD=رمز عبور را بروزرسانی کنید
requiredAction.UPDATE_PROFILE=پروفایل را بروزرسانی کنید
requiredAction.VERIFY_EMAIL=تأیید ایمیل
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=کدهای بازیابی را ایجاد کنید

linkExpirationFormatter.timePeriodUnit.seconds=ثانیه
linkExpirationFormatter.timePeriodUnit.seconds.1=ثانیه
linkExpirationFormatter.timePeriodUnit.minutes=دقیقه
linkExpirationFormatter.timePeriodUnit.minutes.1=دقیقه

linkExpirationFormatter.timePeriodUnit.hours=ساعت
linkExpirationFormatter.timePeriodUnit.hours.1=ساعت
linkExpirationFormatter.timePeriodUnit.days=روز
linkExpirationFormatter.timePeriodUnit.days.1=روز

emailVerificationBodyCode=لطفاً آدرس ایمیل خود را با وارد کردن کد زیر تأیید کنید.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>لطفاً آدرس ایمیل خود را با وارد کردن کد زیر تأیید کنید.</p><p><b>{0}</b></p>


linkExpirationFormatter.timePeriodUnit.seconds=ثانیه
linkExpirationFormatter.timePeriodUnit.minutes=دقیقه
linkExpirationFormatter.timePeriodUnit.hours=ساعت
linkExpirationFormatter.timePeriodUnit.days=روز
