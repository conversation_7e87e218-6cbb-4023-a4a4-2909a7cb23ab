<!-- Dialog Header -->
<div class="dialog-header">
  <h2>Affecter carte carburant & frais</h2>
  <button class="close-button" (click)="onCancel()">
    <svg class="close-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="white"/>
    </svg>
  </button>
</div>
<br>

<!-- Form Container -->
<form [formGroup]="carburantForm" (ngSubmit)="onSubmit()" class="reservation-form">
  <div class="form-grid">
    <!-- Radio Selection -->
    <div class="type-selection">
      <label class="radio-option">
        <input type="radio" formControlName="typeConsommation" class="radio-input" value="carte">
        <span class="radio-checkmark"></span>
        <span class="radio-label">Carte Carburant</span>
      </label>
      <label class="radio-option">
        <input type="radio" formControlName="typeConsommation" class="radio-input" value="frais">
        <span class="radio-checkmark"></span>
        <span class="radio-label">Frais</span>
      </label>
    </div>

    <!-- Carte Carburant Section -->
    <div class="section-container carte-section" *ngIf="carburantForm.get('typeConsommation')?.value === 'carte'">
      <h3 class="section-title">Sélectionnez une carte carburant</h3>
      <div class="table-container">
        <table class="carte-table">
          <thead>
            <tr>
              <th class="checkbox-column"></th>
              <th>Numéro de carte</th>
              <th>Solde</th>
              <th>Type</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let carte of cartesDisponibles"
                class="carte-row"
                [class.selected]="carburantForm.get('carteId')?.value === carte.id">
              <td class="checkbox-column">
                <label class="custom-checkbox">
                  <input type="checkbox"
                         [checked]="carburantForm.get('carteId')?.value === carte.id"
                         (change)="onCheckboxChange(carte.id)">
                  <span class="checkmark"></span>
                </label>
              </td>
              <td class="card-number">{{ carte.numero }}</td>
              <td class="solde">{{ carte.solde | number:'1.2-2' }} DT</td>
              <td class="type">{{ carte.typeCarburant }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="error-message" *ngIf="carburantForm.get('carteId')?.hasError('required') && carburantForm.get('carteId')?.touched">
        Veuillez sélectionner une carte.
      </div>
    </div>

    <!-- Frais Section -->
    <div class="section-container frais-section" *ngIf="carburantForm.get('typeConsommation')?.value === 'frais'">
      <h3 class="section-title">Saisir les frais</h3>
      <div class="input-group">
        <input type="number" min="0" step="0.01" id="montantEstime" formControlName="montantEstime">
        <label for="montantEstime">Montant des frais (DT)</label>
      </div>
      <div class="error-message" *ngIf="carburantForm.get('montantEstime')?.hasError('required') && carburantForm.get('montantEstime')?.touched">
        Le montant est requis.
      </div>
      <div class="error-message" *ngIf="carburantForm.get('montantEstime')?.hasError('min') && carburantForm.get('montantEstime')?.touched">
        Le montant doit être positif.
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="form-actions">
    <button type="button" class="btn btn-secondary" (click)="onCancel()" [disabled]="loading">
      <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
      </svg>
      Fermer
    </button>
    <button type="submit" class="btn btn-primary" [disabled]="!carburantForm.valid || loading">
      <ng-container *ngIf="!loading; else loadingTemplate">
        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
        </svg>
        Attribuer
      </ng-container>
      <ng-template #loadingTemplate>
        <div class="loader"></div>
        Attribution...
      </ng-template>
    </button>
  </div>
</form>
