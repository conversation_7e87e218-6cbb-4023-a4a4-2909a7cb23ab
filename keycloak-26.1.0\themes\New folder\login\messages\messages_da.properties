doLogIn=Log ind
doRegister=Registrer
doRegisterSecurityKey=Registrer
doCancel=Annuller
doSubmit=Indsend
doYes=Ja
doNo=Nej
doContinue=Fortsæt
doIgnore=Ignorer
doAccept=Accepter
doDecline=Afslå
doForgotPassword=Glemt adgangskode?
doClickHere=Klik her
doImpersonate=Efterlign
kerberosNotConfigured=Kerberos er ikke konfigureret
kerberosNotConfiguredTitle=Kerberos er ikke konfigureret
bypassKerberosDetail=Enten er du ikke logget ind via Kerberos eller også er din browser ikke sat op til Kerberos login. Tryk fortsæt for at logge ind på anden vis
kerberosNotSetUp=Kerberos er ikke sat op. Du kan ikke logge ind.
registerTitle=Registrer
loginAccountTitle=Log ind på din konto
loginTitle=Log ind i {0}
loginTitleHtml={0}
impersonateTitle={0} Efterlign bruger
impersonateTitleHtml=<strong>{0}</strong> Efterlign bruger
realmChoice=Rige
unknownUser=Ukendt bruger
loginTotpTitle=Mobil Godkendelses Opsætning
loginProfileTitle=Opdater brugerinformationer
loginTimeout=Dit login tog for lang tid. Login processen vil nu begynde forfra.
oauthGrantTitle=Giv adgang til {0}
oauthGrantTitleHtml={0}
errorTitle=Vi beklager...
errorTitleHtml=Vi <strong>beklager</strong> ...
emailVerifyTitle=Email verificering
emailForgotTitle=Glemt din adgangskode?
updatePasswordTitle=Opdater adgangskode
codeSuccessTitle=Success kode
codeErrorTitle=Fejl kode\: {0}
displayUnsupported=Den ønskede skærmtype understøttes ikke
browserRequired=Brwoseren skal logges ind
browserContinue=Browser påkrævet for at kunne gennemføre login
browserContinuePrompt=Åben browser for at fortsætte login? [j/n]:
browserContinueAnswer=j

termsTitle=Vilkår og betingelser
termsText=<p>Vilkår og betingelser mangler at blive beskrevet</p>
termsPlainText=Vilkår og betingelser mangler at blive beskrevet.

recaptchaFailed=Ugyldig Recaptcha
recaptchaNotConfigured=Recaptcha er påkrævet, men ikke konfigureret
consentDenied=Samtykke afslået.

noAccount=Ny bruger?
username=Brugernavn
usernameOrEmail=Brugernavn eller email
firstName=Fornavn
givenName=Fornavn
fullName=Fulde navn
lastName=Efternavn
familyName=Efternavn
email=Email
password=Adgangskode
passwordConfirm=Bekræft adgangskode
passwordNew=Ny Adgangskode
passwordNewConfirm=Bekræft ny adgangskode
rememberMe=Husk mig
authenticatorCode=Engangskode
address=Adresse
street=Vej
locality=By
region=Region
postal_code=Postnummer
country=Land
emailVerified=Email verificeret
gssDelegationCredential=GSS Delegation Credential

profileScopeConsentText=Brugerprofil
emailScopeConsentText=Email adresse
addressScopeConsentText=Adresse
phoneScopeConsentText=Telefonnummer
offlineAccessScopeConsentText=Offline Adgang
samlRoleListScopeConsentText=Mine roller

loginTotpIntro=Du skal opsætte en Engangskodegenerator for at kunne tilgå denne konto.
loginTotpStep1=Installer en af følgende applikationer på din mobil
loginTotpStep2=Åben applikationen og skan stregkoden
loginTotpStep3=Indtast engangskoden fra applikationen og tryk Indsend for at gennemføre opsætningen
loginTotpManualStep2=Åben applikationen og indtast nøglen
loginTotpManualStep3=Brug følgende konfigurations værdier hvis applikationen tillader det
loginTotpUnableToScan=Kan du ikke skanne?
loginTotpScanBarcode=Skan stregkode?
loginOtpOneTime=Engangskode
loginTotpType=Type
loginTotpAlgorithm=Algoritme
loginTotpDigits=Cifre
loginTotpInterval=Interval
loginTotpCounter=Tæller

loginTotp.totp=Tidsbaseret
loginTotp.hotp=Tællerbaseret

oauthGrantRequest=Bevilger du disse adgangs privilegier?
inResource=ind
emailVerifyInstruction1=En email med instruktioner til, hvordan du verificerer din mail adresse er blevet sendt til dig.
emailVerifyInstruction2=Har du ikke modtaget en verificerings kode i din inbox?
emailVerifyInstruction3=for at gensende emailen.

emailLinkIdpTitle=Link {0}
emailLinkIdp1=En email med instruktioner til hvordan du linker {0} konto {1} med din {2} konto er blevet sendt til dig.
emailLinkIdp2=Har du ikke modtaget en verificerings kode i din inbox?
emailLinkIdp3=for at gensende emailen.
emailLinkIdp4=Hvis du allerede har verificeret din email i en anden browser
emailLinkIdp5=for at fortsætte.

backToLogin=&laquo; Tilbage til log ind

emailInstruction=Indtast dit brugernavn eller email adresse, så sender vi instruktioner til dig om hvordan du angiver en ny adgangskode.

copyCodeInstruction=Kopier denne kode og indsæt den i din applikation:

pageExpiredTitle=Siden er udløbet
pageExpiredMsg1=For at genstarte login processen
pageExpiredMsg2=For at fortsætte login processen

personalInfo=Personlig information:
role_admin=Admin
role_realm-admin=Rige Admin
role_create-realm=Opret rige
role_create-client=Opret klient
role_view-realm=Se rige
role_view-users=Se brugere
role_view-applications=Se applikationer
role_view-clients=Se klienter
role_view-events=Se hændelser
role_view-identity-providers=Se identitetsudbydere
role_manage-realm=Administrer rige
role_manage-users=Administrer brugere
role_manage-applications=Administrer applikationer
role_manage-identity-providers=Administrer identitetsudbydere
role_manage-clients=Administrer klienter
role_manage-events=Administrer hændelser
role_view-profile=Se profil
role_manage-account=Administrer konto
role_manage-account-links=Administrer konto links
role_read-token=Se token
role_offline-access=Offline adgang
client_account=Konto
client_account-console=Kontokonsol
client_security-admin-console=Sikkerhefds Admin Konsol
client_admin-cli=Admin CLI
client_realm-management=Rige administration
client_broker=Broker

invalidUserMessage=Ugyldig brugernavn eller adgangskode.
invalidEmailMessage=Ugyldig email adresse.
accountDisabledMessage=Kontoen er deaktiveret, kontakt en administrator.
accountTemporarilyDisabledMessage=Ugyldig brugernavn eller adgangskode.
accountPermanentlyDisabledMessage=Ugyldig brugernavn eller adgangskode.
accountTemporarilyDisabledMessageTotp=Ugyldig autentificerings kode.
accountPermanentlyDisabledMessageTotp=Ugyldig autentificerings kode.
expiredCodeMessage=Log ind tog for lang tid. Prøv igen.
expiredActionMessage=Handlingen er udløbet. Fortsæt med log ind nu.
expiredActionTokenNoSessionMessage=Handling udløbet.
expiredActionTokenSessionExistsMessage=Handlingen er udløbet. Start venligst forfra.

missingFirstNameMessage=Angiv fornavn.
missingLastNameMessage=Angiv efternavn.
missingEmailMessage=Angiv email adressse.
missingUsernameMessage=Angiv brugernavn.
missingPasswordMessage=Angiv password.
missingTotpMessage=Angiv autentificerings kode.
notMatchPasswordMessage=Passwords er ikke ens.

invalidPasswordExistingMessage=Ugyldig eksisterende adgangskode.
invalidPasswordBlacklistedMessage=Ugyldig adgangskode: Adgangskoden er sortlisted.
invalidPasswordConfirmMessage=Adgangskoderne er ikke ens
invalidTotpMessage=Ugyldig autentificerings kode.

usernameExistsMessage=Brugernavnet eksisterer allerede.
emailExistsMessage=Email adressen eksisterer allerede.

federatedIdentityExistsMessage=Bruger med {0} {1} eksisterer allerede. Log ind i konto administration for at linke kontoen.

confirmLinkIdpTitle=Kontoen eksisterer allerede
federatedIdentityConfirmLinkMessage=Bruger med {0} {1} eksisterer allerede. Hvordan vil du fortsætte?
federatedIdentityConfirmReauthenticateMessage=Log ind som {0} for at linke din konto med {1}
confirmLinkIdpReviewProfile=Se profil
confirmLinkIdpContinue=Tilføj til eksisterende konto

configureTotpMessage=Du skal opsætte en Mobile Authenticator for at kunne aktivere din konto.
updateProfileMessage=Du skal opdatere din brugerprofil for at kunne aktivere din konto.
updatePasswordMessage=Du skal ændre din adgangskode for at kunne aktivere din konto.
resetPasswordMessage=Du skal ændre din adgangskode.
verifyEmailMessage=Du skal verificere din email adresse for at kunne aktivere din konto.
linkIdpMessage=Du skal verificere din email adresse for at kunne kontoen med {0}.

emailSentMessage=Du vil snarest modtage en email med yderligere instruktioner.
emailSendErrorMessage=Kunne ikke sende email, prøv igen senere.

accountUpdatedMessage=Din konto er blevet opdateret.
accountPasswordUpdatedMessage=Din adgangskode er blevet opdateret.

delegationCompleteHeader=Login lykkedes
delegationCompleteMessage=Du kan nu lukke dette browser vindue og gå tilbage til din konsol applikation.
delegationFailedHeader=Log ind fejlede
delegationFailedMessage=Du kan nu lukke dette browser vindue og gå tilbage til din konsol applikation for at forsøge at logge ind igen.

noAccessMessage=Ingen adgang

invalidPasswordMinLengthMessage=Ugyldig adgangskode: minimum længde {0}.
invalidPasswordMinDigitsMessage=Ugyldig adgangskode: skal minimum indeholde {0} tal.
invalidPasswordMinLowerCaseCharsMessage=Ugyldig adgangskode: skal minimum indeholde {0} små bogstaver.
invalidPasswordMinUpperCaseCharsMessage=Ugyldig adgangskode: skal minimum indeholde {0} store bogstaver.
invalidPasswordMinSpecialCharsMessage=Ugyldig adgangskode: skal minimum indeholde {0} specialtegn.
invalidPasswordNotUsernameMessage=Ugyldig adgangskode: må ikke være identisk med brugernavnet.
invalidPasswordRegexPatternMessage=Ugyldig adgangskode: Ikke i stand til at matche regex mønstre.
invalidPasswordHistoryMessage=Ugyldig adgangskode: må ikke være identisk med nogle af de seneste {0} adgangskoder.
invalidPasswordGenericMessage=Ugyldig adgangskode: ny adgangskode matcher ikke vores adgangskode politikker.

failedToProcessResponseMessage=Ude af stand til at processere svaret
httpsRequiredMessage=HTTPS påkrævet
realmNotEnabledMessage=Riget er ikke aktiveret
invalidRequestMessage=Ugyldig Forespørgsel
failedLogout=Logud fejlede
unknownLoginRequesterMessage=Ukendt log ind forespørger
loginRequesterNotEnabledMessage=Log ind forespørgeren er ikke aktiveret
bearerOnlyMessage=Bearer-only applikationer må ikke foretage browser login
standardFlowDisabledMessage=Klienten må ikke foretage browser login med den givne response_type. Standard flowet er deaktiveret for klienten.
implicitFlowDisabledMessage=Klienten må ikke foretage browser login med den givne response_type. Implicit flowet er deaktiveret for klienten.

invalidRedirectUriMessage=Ugyldig redirect uri
unsupportedNameIdFormatMessage=Ikke understøttet NameIDFormat
invalidRequesterMessage=Ugyldig forespørger
registrationNotAllowedMessage=Registrering er ikke tilladt
resetCredentialNotAllowedMessage=Reset Credential er ikke tilladt

permissionNotApprovedMessage=Tilladelse ikke godkendt.
noRelayStateInResponseMessage=Ingen relæ tilstand i svaret fra identitetsudbyderen.
insufficientPermissionMessage=Utilstrækkelig tilladelse for at kunne linke identiter.
couldNotProceedWithAuthenticationRequestMessage=Kunne ikke fortsætte med godkendelsesanmodning til identitetsudbyderen.
couldNotObtainTokenMessage=Kunne ikke opnå token fra identitetsudbyder.
unexpectedErrorRetrievingTokenMessage=Uventet fejl i forsøget på at hente token fra identitetsudbyder.
unexpectedErrorHandlingResponseMessage=Uventet fejl i forsøget på at behandle svaret fra identitetsudbyder.
identityProviderAuthenticationFailedMessage=Log ind fejlede. Kunne ikke logge ind ved identitetsudbyder.
couldNotSendAuthenticationRequestMessage=Kunne ikke sende log ind forespørgsel til identitetsudbyder.
unexpectedErrorHandlingRequestMessage=Uventet fejl under håndteringen af forespørgsel til identitetsudbyder.
invalidAccessCodeMessage=Ugyldig adgangskode.
sessionNotActiveMessage=Sessionen er ikke aktiv.
invalidCodeMessage=Der opstod en fejl, log ind igen via din applikation.
identityProviderUnexpectedErrorMessage=Uventet fejl under log ind ved identitetsudbyder
identityProviderNotFoundMessage=Kunne ikke finde en identitetsudbyder med det angivede id.
identityProviderLinkSuccess=Din email er nu verificeret. Gå tilbage til din oprindelige browser og fortsæt log ind derfra.
staleCodeMessage=Siden er ikke længere gyldig, gå tilbage til din applikation og login igen
realmSupportsNoCredentialsMessage=Riget understøtter ikke nogen legimatitionstype.
credentialSetupRequired=Kan ikke logge ind. Legimatitionstype skal konfigureres.
identityProviderNotUniqueMessage=Riget understøtter flere forskellige identitetsudbydere. Kunne ikke beslutte hvilken identitetsudbyder der skulle bruges til at logge ind med.
emailVerifiedMessage=Din email adresse er verificeret.
staleEmailVerificationLink=Linket du har klikket på er et gammelt udløbet link. Måske har du allerede verificeret din mailadresse?
identityProviderAlreadyLinkedMessage=Forbundsidentitet returneret af {0} er allerede linket til en anden bruger.
confirmAccountLinking=Bekræft sammenkobling af konto {0} fra identitetsudbyder {1} med din konto.
confirmEmailAddressVerification=Bekræft gyldigheden af email adresse {0}.
confirmExecutionOfActions=Udfør følgende handling(er)

backToApplication=&laquo; Tilbage til applikation
missingParameterMessage=Manglende parametre\: {0}
clientNotFoundMessage=Klienten kunne ikke findes.
clientDisabledMessage=Klienten er deaktiveret.
invalidParameterMessage=Ugyldig parameter\: {0}
alreadyLoggedIn=Du er allerede logget ind.
differentUserAuthenticated=Du er allerede logget ind som en anden bruger ''{0}'' i denne session. Log venligst ud først.
brokerLinkingSessionExpired=Har forespørgt kobling mellem mæglerkonti, men den nuværende session er ikke længere gyldig.

proceedWithAction=&raquo; Tryk her for at fortsætte

requiredAction.CONFIGURE_TOTP=Konfigurer OTP
requiredAction.TERMS_AND_CONDITIONS=Vilkår og betingelser
requiredAction.UPDATE_PASSWORD=Opdater Adgangskode
requiredAction.UPDATE_PROFILE=Opdater Profil
requiredAction.VERIFY_EMAIL=Verificer email adresse

doX509Login=Du vil blive logget ind som\:
clientCertificate=X509 client certificate\:
noCertificate=[No Certificate]

pageNotFound=Siden kunne ikke findes
internalServerError=Der opstod en intern server fejl.

console-username=Brugernavn:
console-password=Adgangskode:
console-otp=Engangskode:
console-new-password=Ny Adgangskode:
console-confirm-password=Bekræft Adgangskode:
console-update-password=Du skal opdatere din adgangskode.
console-verify-email=Du skal verificere din email adresse. En email er blevet sendt til {0} som indeholder en verificerings kode. Indtast koden i input feltet herunder.

console-email-code=Email Kode:
console-accept-terms=Accepter Vilkår? [j/n]:
console-accept=j

auth-username-form-display-name=Brugernavn
auth-username-form-help-text=Start log ind ved at indtaste dit brugernavn
auth-username-password-form-display-name=Brugernavn og adgangskode
auth-username-password-form-help-text=Log ind ved at indtaste dit brugernavn og adgangskode

doBack=Tilbage
doTryAgain=Prøv igen
doTryAnotherWay=Prøv på en anden måde
rolesScopeConsentText=Brugerroller
restartLoginTooltip=Start log ind forfra
loginTotpStep3DeviceName=Angiv et udstyrsnavn for at kunne holde rede på udstyr med engangskode.
loginCredential=Credential
loginTotpDeviceName=Udstyrsnavn
loginChooseAuthenticator=Vælg metode til log ind
requiredFields=Nødvendige felter
invalidUsernameMessage=Ugyldigt brugernavn.
invalidUsernameOrEmailMessage=Ugyldigt brugernavn eller email.
invalidPasswordMessage=Ugyldig adangskode.
missingTotpDeviceNameMessage=Angiv venligst et udstyrsnavn.
nestedFirstBrokerFlowMessage={0} brugeren {1} er ikke forbundet til nogen kendt bruger.
openshift.scope.user_info=Brugerinformation
openshift.scope.user_check-access=Brugeradgangsinformation
openshift.scope.user_full=Fuld adgang
openshift.scope.list-projects=Vis liste af projekter
saml.post-form.title=Log ind Redirect
saml.post-form.message=Redirigerer, vent venligst.
saml.post-form.js-disabled=JavaScript er disabled. Vi anbefaler stærkt at enbable det. Klik på knappen nedenfor for at fortsætte.
otp-display-name=Engangskodegenerator
otp-help-text=Indtast en godkendelseskode fra engangskodegeneratoren.
password-display-name=Adgangskode
password-help-text=Log ind ved at indtaste din adgangskode.
webauthn-display-name=Sikkerhedsnøgle
webauthn-help-text=Brug din sikkerhedsnøgle for at logge ind.
webauthn-passwordless-display-name=Sikkerhedsnøgle
webauthn-passwordless-help-text=Brug din sikkerhedsnøgle for at logge ind uden adgangskode.
webauthn-login-title=Log ind med sikkerhedsnøgle
webauthn-registration-title=Registrering af Sikkerhedsnøgle
webauthn-available-authenticators=Tilgængelige log ind måder
webauthn-error-title=Sikkerhedsnøglefejl
webauthn-error-registration=Det lykkedes ikke at registrere din sikkerhedsnøgle.
webauthn-error-api-get=Det lykkedes ikke at logge ind med din sikkerhedsnøgle.
webauthn-error-different-user=Den første authenticatede bruger er ikke den der er authenticated med sikkerhedsnøglen.
webauthn-error-auth-verification=Resultatet fra log ind med sikkerhedsnøgle er ugyldigt.
webauthn-error-register-verification=Resultatet fra registrering med sikkerhedsnøglen er ugyldigt.
webauthn-error-user-not-found=Ukendt bruger authenticated med sikkerhedsnøglen.
identity-provider-redirector=Forbind med en anden Identitetsudbyder
identity-provider-login-label=Eller log ind med

readOnlyUsernameMessage=Du kan ikke opdatere dit brugernavn da det er read-only.
