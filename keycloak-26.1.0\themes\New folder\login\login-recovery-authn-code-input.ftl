<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('recoveryCodeInput'); section>
    <#if section = "header">
        ${msg("auth-recovery-code-header")}
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form">
                <div id="kc-form-wrapper">
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("auth-recovery-code-header")}</h1>
                        <p class="form-subtitle">${msg("auth-recovery-code-info")}</p>
                    </div>

                    <form id="kc-recovery-code-login-form" action="${url.loginAction}" method="post">
                        <div class="form-floating">
                            <input tabindex="1" 
                                   id="recoveryCodeInput"
                                   name="recoveryCodeInput"
                                   class="form-control ${messagesPerField.existsError('recoveryCodeInput')?then('is-invalid', '')}"
                                   type="text"
                                   autocomplete="off"
                                   autofocus
                                   placeholder=" "
                                   aria-invalid="<#if messagesPerField.existsError('recoveryCodeInput')>true</#if>"
                            />
                            <span class="form-icon otp-icon" aria-hidden="true"></span>
                            <label for="recoveryCodeInput">${msg("auth-recovery-code-prompt", recoveryAuthnCodesInputBean.codeNumber?c)}</label>

                            <#if messagesPerField.existsError('recoveryCodeInput')>
                                <span id="input-error" class="error-message" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('recoveryCodeInput'))?no_esc}
                                </span>
                            </#if>
                        </div>

                        <div class="form-group">
                            <input tabindex="2"
                                   class="btn btn-primary btn-block btn-lg"
                                   type="submit"
                                   value="${msg("doSubmit")}"
                            />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </#if>
</@layout.registrationLayout>