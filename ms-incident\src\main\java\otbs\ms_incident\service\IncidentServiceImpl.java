package otbs.ms_incident.service;

import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.commun.PageUtil;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;
import otbs.ms_incident.config.CacheConfig;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.IncidentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class IncidentServiceImpl implements IncidentService {

    private final IncidentRepository incidentRepository;
    private final ReparationRepository reparationRepository;
    private final IncidentMapper incidentMapper;
    private final NotificationEventPublisher notificationEventPublisher;

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = CacheConfig.INCIDENTS_CACHE, allEntries = true)
    })
    public IncidentResponseDTO createIncident(IncidentRequestDTO requestDTO) {
        log.info("Creating incident with data: {}", requestDTO);

        // S'assurer que la priorité est définie comme FAIBLE si elle est null
        if (requestDTO.getPriorite() == null) {
            requestDTO.setPriorite(NiveauPrioriteIncident.FAIBLE);
        }

        Incident incident = incidentMapper.toEntity(requestDTO);

        // Double vérification pour s'assurer que la priorité est bien définie
        if (incident.getPriorite() == null) {
            incident.setPriorite(NiveauPrioriteIncident.FAIBLE);
        }

        Incident savedIncident = incidentRepository.save(incident);
        log.info("Incident created with ID: {}", savedIncident.getId());

        // Publier l'événement de création d'incident
        notificationEventPublisher.publishIncidentCreated(savedIncident);

        return incidentMapper.toResponseDTO(savedIncident);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = CacheConfig.INCIDENT_BY_ID_CACHE, key = "#id")
    public IncidentResponseDTO getIncidentById(Long id) {
        log.info("Fetching incident with ID: {}", id);
        Incident incident = findIncidentById(id);
        return incidentMapper.toResponseDTO(incident);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = CacheConfig.INCIDENTS_CACHE)
    public List<IncidentResponseDTO> getAllIncidents() {
        log.info("Fetching all incidents");
        List<Incident> incidents = incidentRepository.findAll();
        return incidentMapper.toResponseDTOList(incidents);
    }

    @Override
    @Transactional
    @Caching(
        put = { @CachePut(value = CacheConfig.INCIDENT_BY_ID_CACHE, key = "#id") },
        evict = { @CacheEvict(value = CacheConfig.INCIDENTS_CACHE, allEntries = true) }
    )
    public IncidentResponseDTO updateIncident(Long id, IncidentRequestDTO requestDTO) {
        log.info("Updating incident with ID: {} with data: {}", id, requestDTO);
        Incident incident = findIncidentById(id);
        incidentMapper.updateIncidentFromDTO(requestDTO, incident);
        Incident updatedIncident = incidentRepository.save(incident);
        log.info("Incident with ID: {} updated successfully", id);
        return incidentMapper.toResponseDTO(updatedIncident);
    }

    @Override
    @Transactional
    public void deleteIncident(Long id) {
        log.info("Deleting incident with ID: {}", id);
        if (!incidentRepository.existsById(id)) {
            throw new ResourceNotFoundException("Incident n'a pas été trouvé avec id : '" + id + "'");
        }
        incidentRepository.deleteById(id);
        log.info("Incident with ID: {} deleted successfully", id);
    }



    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getIncidentsByDateRange(LocalDate debut, LocalDate fin) {
        log.info("Fetching incidents between dates: {} and {}", debut, fin);
        if (debut.isAfter(fin)) {
            throw new BadRequestException("La date de début doit être antérieure ou égale à la date de fin");
        }
        List<Incident> incidents = incidentRepository.findByDateBetween(debut, fin);
        return incidentMapper.toResponseDTOList(incidents);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getIncidentsByLieu(String lieu) {
        log.info("Fetching incidents by location containing: {}", lieu);
        List<Incident> incidents = incidentRepository.findByLieuContaining(lieu);
        return incidentMapper.toResponseDTOList(incidents);
    }

    @Override
    @Transactional
    public IncidentResponseDTO addReparationToIncident(Long incidentId, Long reparationId) {
        log.info("Adding reparation with ID: {} to incident with ID: {}", reparationId, incidentId);
        Incident incident = findIncidentById(incidentId);

        Reparation reparation = reparationRepository.findById(reparationId)
                .orElseThrow(() -> new ResourceNotFoundException("Reparation", "id", reparationId));

        if (reparation.getIncident() != null) {
            throw new BadRequestException("La réparation est déjà associée à un incident");
        }

        reparation.setIncident(incident);
        incident.getReparations().add(reparation);

        Incident updatedIncident = incidentRepository.save(incident);
        log.info("Reparation with ID: {} added to incident with ID: {}", reparationId, incidentId);
        return incidentMapper.toResponseDTO(updatedIncident);
    }

    @Override
    @Transactional
    public IncidentResponseDTO removeReparationFromIncident(Long incidentId, Long reparationId) {
        log.info("Removing reparation with ID: {} from incident with ID: {}", reparationId, incidentId);
        Incident incident = findIncidentById(incidentId);

        Reparation reparation = reparationRepository.findById(reparationId)
                .orElseThrow(() -> new ResourceNotFoundException("Reparation", "id", reparationId));

        if (reparation.getIncident() == null || !reparation.getIncident().getId().equals(incidentId)) {
            throw new BadRequestException("La réparation n'est pas associée à cet incident");
        }

        reparation.setIncident(null);
        incident.getReparations().remove(reparation);

        Incident updatedIncident = incidentRepository.save(incident);
        log.info("Reparation with ID: {} removed from incident with ID: {}", reparationId, incidentId);
        return incidentMapper.toResponseDTO(updatedIncident);
    }

    @Override
    @Transactional(readOnly = true)
    public Incident getIncidentEntityById(Long id) {
        log.info("Fetching incident entity with ID: {}", id);
        return findIncidentById(id);
    }

    // Helper methods
    private Incident findIncidentById(Long id) {
        return incidentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Incident n'a pas été trouvé avec id : '" + id + "'"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getIncidentsByVehiculeId(Long vehiculeId) {
        log.info("Récupération des incidents pour le véhicule avec id: {}", vehiculeId);
        List<Incident> incidents = incidentRepository.findByVehiculeId(vehiculeId);
        return incidentMapper.toResponseDTOList(incidents);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countIncidentsByVehiculeId(Long vehiculeId) {
        log.info("Comptage des incidents pour le véhicule avec id: {}", vehiculeId);
        return incidentRepository.countByVehiculeId(vehiculeId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getIncidentsForToday() {
        log.info("Récupération des incidents survenus aujourd'hui");
        LocalDate today = LocalDate.now();
        List<Incident> incidents = incidentRepository.findByDateBetween(today, today);
        return incidentMapper.toResponseDTOList(incidents);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getIncidentsForLastMonth() {
        log.info("Récupération des incidents survenus au cours du dernier mois");
        LocalDate today = LocalDate.now();
        LocalDate oneMonthAgo = today.minusMonths(1);
        List<Incident> incidents = incidentRepository.findByDateBetween(oneMonthAgo, today);
        return incidentMapper.toResponseDTOList(incidents);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getIncidentsForLastSemester() {
        log.info("Récupération des incidents survenus au cours du dernier semestre");
        LocalDate today = LocalDate.now();
        LocalDate sixMonthsAgo = today.minusMonths(6);
        List<Incident> incidents = incidentRepository.findByDateBetween(sixMonthsAgo, today);
        return incidentMapper.toResponseDTOList(incidents);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getIncidentsForLastYear() {
        log.info("Récupération des incidents survenus au cours de la dernière année");
        LocalDate today = LocalDate.now();
        LocalDate oneYearAgo = today.minusYears(1);
        List<Incident> incidents = incidentRepository.findByDateBetween(oneYearAgo, today);
        return incidentMapper.toResponseDTOList(incidents);
    }

    // Méthodes paginées

    @Override
    @Transactional(readOnly = true)
    public PageResponse<IncidentResponseDTO> getAllIncidents(Pageable pageable) {
        log.info("Récupération de tous les incidents avec pagination: {}", pageable);
        Page<Incident> incidentPage = incidentRepository.findAll(pageable);
        return PageUtil.toPageResponse(incidentPage, incidentMapper::toResponseDTO);
    }



    @Override
    @Transactional(readOnly = true)
    public PageResponse<IncidentResponseDTO> getIncidentsByDateRange(LocalDate debut, LocalDate fin, Pageable pageable) {
        log.info("Récupération des incidents entre les dates: {} et {} avec pagination: {}", debut, fin, pageable);
        if (debut.isAfter(fin)) {
            throw new BadRequestException("La date de début doit être antérieure ou égale à la date de fin");
        }
        Page<Incident> incidentPage = incidentRepository.findByDateBetween(debut, fin, pageable);
        return PageUtil.toPageResponse(incidentPage, incidentMapper::toResponseDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<IncidentResponseDTO> getIncidentsByLieu(String lieu, Pageable pageable) {
        log.info("Récupération des incidents par lieu contenant: {} avec pagination: {}", lieu, pageable);
        Page<Incident> incidentPage = incidentRepository.findByLieuContaining(lieu, pageable);
        return PageUtil.toPageResponse(incidentPage, incidentMapper::toResponseDTO);
    }



    @Override
    @Transactional(readOnly = true)
    public PageResponse<IncidentResponseDTO> getIncidentsByVehiculeId(Long vehiculeId, Pageable pageable) {
        log.info("Récupération des incidents pour le véhicule avec id: {} avec pagination: {}", vehiculeId, pageable);
        Page<Incident> incidentPage = incidentRepository.findByVehiculeId(vehiculeId, pageable);
        return PageUtil.toPageResponse(incidentPage, incidentMapper::toResponseDTO);
    }



    @Override
    @Transactional
    public IncidentResponseDTO updateIncidentStatus(Long id, StatusIncident status) {
        log.info("Mise à jour du statut de l'incident avec id: {} vers: {}", id, status);
        Incident incident = findIncidentById(id);
        incident.setStatus(status);
        Incident updatedIncident = incidentRepository.save(incident);

        // Publier l'événement de résolution si l'incident est résolu
        if (status == StatusIncident.RESOLU) {
            notificationEventPublisher.publishIncidentResolved(updatedIncident);
        }

        return incidentMapper.toResponseDTO(updatedIncident);
    }

    @Override
    @Transactional
    public IncidentResponseDTO updateIncidentPriority(Long id, NiveauPrioriteIncident priority) {
        log.info("Mise à jour de la priorité de l'incident avec id: {} vers: {}", id, priority);
        Incident incident = findIncidentById(id);
        incident.setPriorite(priority);
        Incident updatedIncident = incidentRepository.save(incident);
        return incidentMapper.toResponseDTO(updatedIncident);
    }
}