invalidPasswordMinLengthMessage=無効なパスワード: 最小長は{0}です。
invalidPasswordMinLowerCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の小文字を含む必要があります。
invalidPasswordMinDigitsMessage=無効なパスワード: 少なくとも{0}文字の数字を含む必要があります。
invalidPasswordMinUpperCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の大文字を含む必要があります。
invalidPasswordMinSpecialCharsMessage=無効なパスワード: 少なくとも{0}文字の特殊文字を含む必要があります。
invalidPasswordNotUsernameMessage=無効なパスワード: ユーザー名と同じパスワードは禁止されています。
invalidPasswordRegexPatternMessage=無効なパスワード: 正規表現パターンと一致しません。
invalidPasswordHistoryMessage=無効なパスワード: 最近の{0}パスワードのいずれかと同じパスワードは禁止されています。
invalidPasswordBlacklistedMessage=無効なパスワード: パスワードがブラックリストに含まれています。
invalidPasswordGenericMessage=無効なパスワード: 新しいパスワードはパスワード・ポリシーと一致しません。
ldapErrorInvalidCustomFilter=LDAPフィルターのカスタム設定が、「(」から開始または「)」で終了となっていません。
ldapErrorConnectionTimeoutNotNumber=接続タイムアウトは数字でなければなりません
ldapErrorReadTimeoutNotNumber=読み取りタイムアウトは数字でなければなりません
ldapErrorMissingClientId=レルムロール・マッピングを使用しない場合は、クライアントIDは設定内で提供される必要があります。
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=グループの継承を維持することと、UIDメンバーシップ・タイプを使用することは同時にできません。
ldapErrorCantWriteOnlyForReadOnlyLdap=LDAPプロバイダー・モードがWRITABLEではない場合は、write onlyを設定することはできません。
ldapErrorCantWriteOnlyAndReadOnly=write-onlyとread-onlyを一緒に設定することはできません。
ldapErrorCantEnableStartTlsAndConnectionPooling=StartTLSと接続プーリングの両方を有効にできません。
clientRedirectURIsFragmentError=リダイレクトURIにURIフラグメントを含めることはできません。
clientRootURLFragmentError=ルートURLにURLフラグメントを含めることはできません。
pairwiseMalformedClientRedirectURI=クライアントに無効なリダイレクトURIが含まれていました。
pairwiseClientRedirectURIsMissingHost=クライアントのリダイレクトURIには有効なホスト・コンポーネントが含まれている必要があります。
pairwiseClientRedirectURIsMultipleHosts=設定されたセレクター識別子URIがない場合は、クライアントのリダイレクトURIは複数のホスト・コンポーネントを含むことはできません。
pairwiseMalformedSectorIdentifierURI=不正なセレクター識別子URIです。
pairwiseFailedToGetRedirectURIs=セクター識別子URIからリダイレクトURIを取得できませんでした。
pairwiseRedirectURIsMismatch=クライアントのリダイレクトURIは、セクター識別子URIからフェッチされたリダイレクトURIと一致しません。
invalidPasswordMaxLengthMessage=無効なパスワード: 最大長は{0}です。
