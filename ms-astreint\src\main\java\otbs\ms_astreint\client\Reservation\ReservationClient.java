package otbs.ms_astreint.client.reservation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import otbs.ms_astreint.client.reservation.dto.*;

import java.time.LocalDateTime;
import java.util.List;

@FeignClient(
    name = "reservation",
    url = "${reservation.url}",
    fallback = ReservationClientFallback.class,
    configuration = ReservationClientConfig.class
)
public interface ReservationClient {

    @PostMapping("/api/v1/reservation/addReservation")
    ReservationDto addReservation(@RequestBody ReservationDto reservationDto);

    @GetMapping("/api/v1/reservation/getReservationsByDescription")
    FullReservationDto getReservationsByDescription(@RequestParam(defaultValue = "0") int page,
                                                     @RequestParam(defaultValue = "10") int size,
                                                     @RequestParam String description);

    @PostMapping(value = "/api/consommation-carburant/addConsommation", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ConsommationCarburantDto addConsommationCarburant(
            @ModelAttribute ConsommationCarburantDto dto,
            @RequestPart(value = "file", required = false) MultipartFile file);

    @PutMapping(value = "/api/consommation-carburant/update/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ConsommationCarburantDto updateConsommation(
            @PathVariable Long id,
            @ModelAttribute ConsommationCarburantDto dto,
            @RequestPart(value = "file", required = false) MultipartFile file);

    @PostMapping("/api/v1/consommationTelepeage/addNewConsommationTelepeageByReservation")
    ConsommationTelepeageDto addNewConsommationTelepeageByReservation(@RequestBody ConsommationTelepeageDto consommationTelepeageDto);

    @PostMapping("/api/v1/consommationTelepeage/updateConsommationTelepeageByReservation")
    ConsommationTelepeageDto updateConsommationTelepeageByReservation(@RequestBody ConsommationTelepeageDto consommationTelepeageDto);

    @PostMapping("/api/tickets-restaurant")
    TicketRestaurantDto addTicket(@RequestBody TicketRestaurantDto dto);

    @PutMapping("/api/tickets-restaurant/updateTicketByReservationId/{reservationId}")
    TicketRestaurantDto updateTicketByReservationId(@PathVariable Long reservationId, @RequestBody TicketRestaurantDto dto);

    @GetMapping("/api/v1/telepeage/getAllTelepeages")
    List<TelepeageDto> getAllTelepeages();

    @GetMapping("/api/v1/reservation/getAllAvailableCarsByPeriod")
    List<VehiculeDto> getAllAvailableCarsByPeriod(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateDebut,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateFin);

    @DeleteMapping("/api/v1/reservation/deleteReservation/{reservationId}")
    void deleteReservation(@PathVariable Long reservationId);

    @GetMapping("/api/v1/telepeage/getTelepeageByReservation/{reservationId}")
    TelepeageDto getTelepeageByReservation(@PathVariable Long reservationId);

    @GetMapping("/api/v1/consommationTelepeage/getConsommationParReservation/{reservationId}")
    Double getConsommationParReservation(@PathVariable Long reservationId);


    @GetMapping("/api/paiments-cash/by-reservation/{reservationId}")
    List<PaimentCarburantCashDto> getPaimentsByReservation(@PathVariable Long reservationId);

    @PostMapping(value = "/api/paiments-cash/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    PaimentCarburantCashDto uploadPaiement(@RequestPart("data") PaimentCarburantCashDto dto);

    @PostMapping(value = "/api/consommation-carburant/ajouter-cash", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ConsommationCarburantDto ajouterConsommationCash(
            @ModelAttribute ConsommationCarburantDto dto,
            @RequestPart(value = "file", required = false) MultipartFile file);

    @PostMapping(value = "/api/consommation-carburant/reimburse-cash/{id}")
    ConsommationCarburantDto reimburseCash(@PathVariable Long id);

    @GetMapping("/api/consommation-carburant/reservation/{reservationId}")
    List<ConsommationCarburantDto> getConsommationsByReservation(@PathVariable Long reservationId);

    @GetMapping("/api/consommation-carburant/reservation/{reservationId}/type/{type}")
    List<ConsommationCarburantDto> getConsommationsByReservationAndType(
            @PathVariable Long reservationId,
            @PathVariable TypeConsommation type);

    @GetMapping("/api/cartes-carburant/{id}")
    CarteCarburantDto getCarteCarburantById(@PathVariable Long id);

    @GetMapping("/api/cartes-carburant/findAll")
    List<CarteCarburantDto> getAllCartesCarburant();

    

    @GetMapping("/api/v1/telepeage/getTelepeageByReservation/{reservationId}")
    TelepeageDto getTelepeageByReservation(@PathVariable Long reservationId);

    @GetMapping("/api/v1/consommationTelepeage/getConsommationParReservation/{reservationId}")
    Double getConsommationParReservation(@PathVariable Long reservationId);

    @GetMapping("/api/paiments-cash/by-reservation/{reservationId}")
    List<PaimentCarburantCashDto> getPaimentsByReservation(@PathVariable Long reservationId);

    @PostMapping(value = "/api/paiments-cash/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    PaimentCarburantCashDto uploadPaiement(@RequestPart("data") PaimentCarburantCashDto dto);

    @PostMapping(value = "/api/consommation-carburant/ajouter-cash", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ConsommationCarburantDto ajouterConsommationCash(
            @ModelAttribute ConsommationCarburantDto dto,
            @RequestPart(value = "file", required = false) MultipartFile file);

    @PostMapping(value = "/api/consommation-carburant/reimburse-cash/{id}")
    ConsommationCarburantDto reimburseCash(@PathVariable Long id);

    @GetMapping("/api/consommation-carburant/reservation/{reservationId}")
    List<ConsommationCarburantDto> getConsommationsByReservation(@PathVariable Long reservationId);

    @GetMapping("/api/consommation-carburant/reservation/{reservationId}/type/{type}")
    List<ConsommationCarburantDto> getConsommationsByReservationAndType(
            @PathVariable Long reservationId,
            @PathVariable TypeConsommation type);

    @GetMapping("/api/cartes-carburant/{id}")
    CarteCarburantDto getCarteCarburantById(@PathVariable Long id);

    @GetMapping("/api/cartes-carburant/findAll")
    List<CarteCarburantDto> getAllCartesCarburant();

}





