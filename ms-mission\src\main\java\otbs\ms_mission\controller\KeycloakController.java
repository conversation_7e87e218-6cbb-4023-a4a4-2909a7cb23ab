package otbs.ms_mission.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;

import otbs.ms_mission.client.keycloak.KeycloakService;
import otbs.ms_mission.client.keycloak.UserDTO;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Contrôleur REST pour la gestion des utilisateurs Keycloak.
 */
@RestController
@RequestMapping("/api/keycloak/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Utilisateurs Keycloak", description = "API pour la gestion des utilisateurs Keycloak")
public class KeycloakController {

    private final KeycloakService keycloakService;

    /**
     * Récupère tous les utilisateurs Keycloak.
     *
     * @return Liste de tous les utilisateurs
     */
    @GetMapping
    @Operation(
        summary = "Récupère tous les utilisateurs",
        description = "Récupère la liste complète de tous les utilisateurs Keycloak"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Liste des utilisateurs récupérée avec succès"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Non autorisé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<List<UserDTO>> getAllUsers() {

        log.debug("Demande de récupération de tous les utilisateurs Keycloak");

        try {
            List<UserDTO> users = keycloakService.getAllUsers();

            log.info("Récupération réussie de {} utilisateurs Keycloak", users.size());

            return ResponseEntity.ok(users);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des utilisateurs Keycloak", e);
            throw e;
        }
    }

    /**
     * Recherche des utilisateurs par nom.
     *
     * @param name Nom à rechercher (peut être partiel)
     * @return Liste des utilisateurs correspondants
     */
    @GetMapping("/search")
    @Operation(
        summary = "Recherche des utilisateurs par nom",
        description = "Recherche des utilisateurs Keycloak dont le nom (firstName, lastName ou username) contient la chaîne spécifiée"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Résultats de recherche récupérés avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètre de recherche invalide"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Non autorisé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<List<UserDTO>> searchUsersByName(
            @Parameter(description = "Nom à rechercher (minimum 2 caractères)", required = true)
            @RequestParam String name) {

        log.debug("Demande de recherche d'utilisateurs Keycloak par nom: {}", name);

        // Validation du paramètre
        if (name == null || name.trim().length() < 2) {
            log.warn("Paramètre de recherche invalide: {}", name);
            throw new IllegalArgumentException("Le nom de recherche doit contenir au moins 2 caractères");
        }

        try {
            List<UserDTO> users = keycloakService.searchUsersByName(name.trim());

            log.info("Recherche réussie: {} utilisateurs trouvés pour le nom '{}'", users.size(), name);

            return ResponseEntity.ok(users);

        } catch (Exception e) {
            log.error("Erreur lors de la recherche d'utilisateurs Keycloak par nom: {}", name, e);
            throw e;
        }
    }

    /**
     * Récupère un utilisateur par son ID.
     *
     * @param userId ID de l'utilisateur Keycloak
     * @return Utilisateur correspondant
     */
    @GetMapping("/{userId}")
    @Operation(
        summary = "Récupère un utilisateur par son ID",
        description = "Récupère les détails d'un utilisateur Keycloak spécifique par son ID"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Utilisateur trouvé"),
        @ApiResponse(responseCode = "404", description = "Utilisateur non trouvé"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Non autorisé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<UserDTO> getUserById(
            @Parameter(description = "ID de l'utilisateur Keycloak", required = true)
            @PathVariable String userId) {

        log.debug("Demande de récupération de l'utilisateur Keycloak ID: {}", userId);

        try {
            Optional<UserDTO> userOptional = keycloakService.getUserById(userId);

            if (userOptional.isPresent()) {
                UserDTO user = userOptional.get();
                log.debug("Utilisateur trouvé: {}", user.getUsername());
                return ResponseEntity.ok(user);
            } else {
                log.warn("Utilisateur non trouvé avec l'ID: {}", userId);
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("Erreur lors de la récupération de l'utilisateur Keycloak ID: {}", userId, e);
            throw e;
        }
    }

    /**
     * Endpoint pour tester les rôles d'un utilisateur spécifique.
     *
     * @param userId ID de l'utilisateur
     * @return Rôles de l'utilisateur
     */
    @GetMapping("/{userId}/roles")
    @Operation(
        summary = "Récupère les rôles d'un utilisateur",
        description = "Récupère les rôles realm et client d'un utilisateur spécifique"
    )
    @ApiResponse(responseCode = "200", description = "Rôles récupérés avec succès")
    public ResponseEntity<UserRolesResponse> getUserRoles(
            @Parameter(description = "ID de l'utilisateur", required = true) @PathVariable String userId) {

        log.debug("Demande de récupération des rôles pour l'utilisateur ID: {}", userId);

        try {
            Optional<UserDTO> userOptional = keycloakService.getUserById(userId);

            if (userOptional.isPresent()) {
                UserDTO user = userOptional.get();
                UserRolesResponse response = UserRolesResponse.builder()
                        .userId(user.getId())
                        .username(user.getUsername())
                        .realmRoles(user.getRealmRoles() != null ? user.getRealmRoles() : Arrays.asList())
                        .clientRoles(user.getClientRoles() != null ? user.getClientRoles() : Arrays.asList())
                        .build();

                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des rôles pour l'utilisateur ID: {}", userId, e);
            throw e;
        }
    }

    /**
     * Recherche des utilisateurs par email.
     *
     * @param email Email à rechercher
     * @return Liste des utilisateurs correspondants
     */
    @GetMapping("/search/email")
    @Operation(
        summary = "Recherche des utilisateurs par email",
        description = "Recherche des utilisateurs Keycloak dont l'email contient la chaîne spécifiée"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Résultats de recherche récupérés avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètre de recherche invalide"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Non autorisé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<List<UserDTO>> searchUsersByEmail(
            @Parameter(description = "Email à rechercher", required = true)
            @RequestParam String email) {

        log.debug("Demande de recherche d'utilisateurs Keycloak par email: {}", email);

        // Validation du paramètre
        if (email == null || email.trim().isEmpty()) {
            log.warn("Paramètre email invalide: {}", email);
            throw new IllegalArgumentException("L'email de recherche ne peut pas être vide");
        }

        try {
            List<UserDTO> users = keycloakService.searchUsersByEmail(email.trim());

            log.info("Recherche réussie: {} utilisateurs trouvés pour l'email '{}'", users.size(), email);

            return ResponseEntity.ok(users);

        } catch (Exception e) {
            log.error("Erreur lors de la recherche d'utilisateurs Keycloak par email: {}", email, e);
            throw e;
        }
    }

    /**
     * Récupère les statistiques des utilisateurs.
     *
     * @return Statistiques des utilisateurs
     */
    @GetMapping("/stats")
    @Operation(
        summary = "Récupère les statistiques des utilisateurs",
        description = "Récupère des statistiques sur les utilisateurs Keycloak (nombre total, actifs, etc.)"
    )
    @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès")
    public ResponseEntity<UserStatsResponse> getUserStats() {

        log.debug("Demande de récupération des statistiques utilisateurs Keycloak");

        try {
            List<UserDTO> allUsers = keycloakService.getAllUsers();

            long totalUsers = allUsers.size();
            long enabledUsers = allUsers.stream()
                    .mapToLong(user -> user.isEnabled() ? 1 : 0)
                    .sum();
            long disabledUsers = totalUsers - enabledUsers;

            UserStatsResponse stats = UserStatsResponse.builder()
                    .totalUsers(totalUsers)
                    .enabledUsers(enabledUsers)
                    .disabledUsers(disabledUsers)
                    .build();

            log.info("Statistiques utilisateurs: {} total, {} actifs, {} désactivés",
                    totalUsers, enabledUsers, disabledUsers);

            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des statistiques utilisateurs", e);
            throw e;
        }
    }

    /**
     * DTO pour les statistiques des utilisateurs.
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserStatsResponse {
        private long totalUsers;
        private long enabledUsers;
        private long disabledUsers;

        public double getEnabledPercentage() {
            return totalUsers > 0 ? (double) enabledUsers / totalUsers * 100 : 0;
        }

        public double getDisabledPercentage() {
            return totalUsers > 0 ? (double) disabledUsers / totalUsers * 100 : 0;
        }
    }

    /**
     * DTO pour les rôles d'un utilisateur.
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserRolesResponse {
        private String userId;
        private String username;
        private List<String> realmRoles;
        private List<String> clientRoles;

        public int getTotalRolesCount() {
            int realmCount = realmRoles != null ? realmRoles.size() : 0;
            int clientCount = clientRoles != null ? clientRoles.size() : 0;
            return realmCount + clientCount;
        }
    }
}
