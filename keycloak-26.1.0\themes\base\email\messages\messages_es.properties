emailVerificationSubject=Verificación de email
emailVerificationBody=<PERSON>guien ha creado una cuenta de {2} con esta dirección de email. Si has sido tú, haz click en el enlace siguiente para verificar tu dirección de email.\n\n{0}\n\nEste enlace expirará en {3} minutos.\n\nSi tú no has creado esta cuenta, simplemente ignora este mensaje.
emailVerificationBodyHtml=<p>Alguien ha creado una cuenta de {2} con esta dirección de email. Si has sido tú, haz click en el enlace siguiente para verificar tu dirección de email.</p><p><a href="{0}">Enlace de verficación de dirección de email</a></p><p>Este enlace expirará en {3} minutos.</p><p>Si tú no has creado esta cuenta, simplemente ignora este mensaje.</p>
passwordResetSubject=Reiniciar contraseña
passwordResetBody=<PERSON><PERSON>ien ha solicitado cambiar las credenciales de tu cuenta de {2}. <PERSON> has sido tú, haz clic en el enlace siguiente para reiniciarlas.\n\n{0}\n\nEste enlace expirará en {3} minutos.\n\nSi no quieres reiniciar tus credenciales, simplemente ignora este mensaje y no se realizará ningún cambio.
passwordResetBodyHtml=<p>Alguien ha solicitado cambiar las credenciales de tu cuenta de {2}. Si has sido tú, haz clic en el enlace siguiente para reiniciarlas.</p><p><a href="{0}">Enlace para actualizar la cuenta</a></p><p>Este enlace expirará en {3} minutos.</p><p>Si no quieres reiniciar tus credenciales, simplemente ignora este mensaje y no se realizará ningún cambio.</p>
executeActionsSubject=Actualiza tu cuenta
executeActionsBody=Tu administrador ha solicitado que actualices tu cuenta {2} realizando la(s) siguiente(s) acción(es): {3}. Haz clic en el enlace de abajo para comenzar este proceso.\n\n{0}\n\nEste enlace expirará en {4} minutos.\n\nSi no estás al tanto de que el administrador haya solicitado esto, simplemente ignora este mensaje y no se realizará ningún cambio.
executeActionsBodyHtml=<p>Tu administrador ha solicitado que actualices tu cuenta {2} realizando la(s) siguiente(s) acción(es): {3}. Haz clic en el enlace de abajo para comenzar este proceso.</p><p><a href="{0}">Enlace para actualizar la cuenta</a></p><p>Este enlace caducará en {4}.</p><p>Si no sabes que tu administrador ha solicitado esto, simplemente ignora este mensaje y no se realizará ningún cambio.</p>
eventLoginErrorSubject=Fallo en el inicio de sesión
eventLoginErrorBody=Se ha detectado un intento de acceso fallido a tu cuenta el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.
eventLoginErrorBodyHtml=<p>Se ha detectado un intento de acceso fallido a tu cuenta el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.</p>
eventRemoveTotpSubject=Borrado OTP
eventRemoveTotpBody=OTP fue eliminado de tu cuenta el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.
eventRemoveTotpBodyHtml=<p>OTP fue eliminado de tu cuenta el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.</p>
eventUpdatePasswordSubject=Actualización de contraseña
eventUpdatePasswordBody=Tu contraseña se ha actualizado el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.
eventUpdatePasswordBodyHtml=<p>Tu contraseña se ha actualizado el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.</p>
eventUpdateTotpSubject=Actualización de OTP
eventUpdateTotpBody=OTP se ha actualizado en tu cuenta el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.
eventUpdateTotpBodyHtml=<p>OTP se ha actualizado en tu cuenta el {0} desde {1}. Si no has sido tú, por favor contacta con el administrador.</p>
emailUpdateConfirmationSubject=Verificar un nuevo correo electrónico
emailUpdateConfirmationBody=Para actualizar su cuenta {2} con la dirección de correo electrónico {1}, haga clic en el enlace a continuación\n\n{0}\n\nEl enlace expirará dentro de {3}.\n\nSi no desea continuar con esta modificación, simplemente ignore este mensaje.
emailUpdateConfirmationBodyHtml=<p>Para actualizar su cuenta {2} con la dirección de correo electrónico {1}, haga clic en el enlace a continuación</p><p><a href="{0}">{0}</a></p><p>Este enlace expirará dentro de {3}.</p><p>Si no desea continuar con esta modificación, simplemente ignore este mensaje.</p>
emailTestSubject=[KeyCloak] - Mensaje de prueba SMTP
emailTestBody=Este es un mensaje de prueba
emailTestBodyHtml=<p> Este es un mensaje de prueba </p>
identityProviderLinkSubject=Enlace {0}
identityProviderLinkBody=Alguien quiere vincular su cuenta "{1}" con la cuenta "{0}" del usuario {2}. Si ha sido usted, haga clic en el siguiente enlace para vincular las cuentas \n\n{3}\n\nEste enlace expirará en {5}.\n\nSi no desea vincular la cuenta, simplemente ignore este mensaje. Si vincula las cuentas, podrá iniciar sesión en {1} a través de {0}.
identityProviderLinkBodyHtml=<p>Alguien quiere vincular su <b>{1}</b> cuenta con <b>{0}</b> cuenta del usuario {2}. Si ha sido usted, haga clic en el siguiente enlace para vincular las cuentas</p><p><a href="{3}">Enlace para confirmar el vínculo de cuentas</a></p><p>Este enlace expirará en {5}.</p><p>Si no desea vincular la cuenta, simplemente ignore este mensaje. Si vincula las cuentas, podrá iniciar sesión en {1} a través de {0}.</p>
requiredAction.CONFIGURE_TOTP=Configurar OTP
requiredAction.TERMS_AND_CONDITIONS=Términos y condiciones
requiredAction.UPDATE_PASSWORD=Actualiza la contraseña
requiredAction.UPDATE_PROFILE=Actualización del perfil
requiredAction.VERIFY_EMAIL=Verificar correo electrónico
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generar códigos de recuperación

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#segundos|1#segundo|1<segundos}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minutos|1#minuto|1<minutos}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#horas|1#hora|1<horas}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#días|1#día|1<días}
emailVerificationBodyCode=Verifique su dirección de correo electrónico ingresando en el siguiente código. \n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p> Verifique su dirección de correo electrónico ingresando el siguiente código. </p> <p> <b> {0} </b> </p>
orgInviteSubject=Invitación para unirse a la organización {0}
orgInviteBody=Has sido invitado a unirte a la organización "{3}". Haz clic en el enlace de abajo para unirte.\n\n{0}\n\nEste enlace caducará en {4}.\n\nSi no deseas unirte a la organización, simplemente ignora este mensaje.
eventUpdateCredentialSubject=Actualizar credencial
eventUpdateCredentialBody=Tu credencial {0} fue cambiada el {1} desde {2}. Si no fuiste tú, por favor contacta a un administrador.
eventRemoveCredentialSubject=Eliminar credencial
eventRemoveCredentialBody=La credencial {0} fue eliminada de tu cuenta el {1} desde {2}. Si no fuiste tú, por favor contacta a un administrador.
eventUserDisabledByTemporaryLockoutSubject=Usuario deshabilitado por bloqueo temporal
eventUserDisabledByPermanentLockoutSubject=Usuario deshabilitado por bloqueo permanente
eventUserDisabledByPermanentLockoutBody=Tu usuario ha sido deshabilitado permanentemente debido a múltiples intentos fallidos el {0}. Por favor, contacta a un administrador.
eventUserDisabledByPermanentLockoutHtml=<p>Tu usuario ha sido deshabilitado permanentemente debido a múltiples intentos fallidos el {0}. Por favor, contacta a un administrador.</p>
eventRemoveCredentialBodyHtml=<p>La credencial {0} fue eliminada de tu cuenta el {1} desde {2}. Si no fuiste tú, por favor contacta a un administrador.</p>
eventUserDisabledByTemporaryLockoutHtml=<p>Tu usuario ha sido deshabilitado temporalmente debido a múltiples intentos fallidos el {0}. Por favor, contacta a un administrador si es necesario.</p>
orgInviteBodyHtml=<p>Has sido invitado a unirte a la organización {3}. Haz clic en el enlace de abajo para unirte. </p><p><a href="{0}">Enlace para unirse a la organización</a></p><p>Este enlace caducará en {4}.</p><p>Si no deseas unirte a la organización, simplemente ignora este mensaje.</p>
orgInviteBodyPersonalized=Hola, "{5}" "{6}".\n\nHas sido invitado a unirte a la organización {3}. Haz clic en el enlace de abajo para unirte.\n\n{0}\n\nEste enlace caducará en {4}.\n\nSi no deseas unirte a la organización, simplemente ignora este mensaje.
orgInviteBodyPersonalizedHtml=<p>Hola, {5} {6}.</p><p>Has sido invitado a unirte a la organización {3}. Haz clic en el enlace de abajo para unirte. </p><p><a href="{0}">Enlace para unirse a la organización</a></p><p>Este enlace caducará en {4}.</p><p>Si no deseas unirte a la organización, simplemente ignora este mensaje.</p>
eventUpdateCredentialBodyHtml=<p>Tu credencial {0} fue cambiada el {1} desde {2}. Si no fuiste tú, por favor contacta a un administrador.</p>
eventUserDisabledByTemporaryLockoutBody=Tu usuario ha sido deshabilitado temporalmente debido a múltiples intentos fallidos el {0}. Por favor, contacta a un administrador si es necesario.
