doLogIn=Entrar
doRegister=Cadastre-se
doRegisterSecurityKey=Cadastre-se
doCancel=Cancelar
doSubmit=Ok
doBack=Voltar
doYes=Sim
doNo=Não
doContinue=Continuar
doIgnore=Ignorar
doAccept=Aceitar
doDecline=Rejeitar
doForgotPassword=Esqueceu sua senha?
doClickHere=Clique aqui
doImpersonate=Personificar
doTryAgain=Tente novamente
doTryAnotherWay=Tente outra forma
doConfirmDelete=Confirmar descadastramento
errorDeletingAccount=Falha ao apagar conta
deletingAccountForbidden=Você não tem permissões para apagar a sua própria conta, entre em contato com um administrador.
kerberosNotConfigured=Kerberos Não Configurado
kerberosNotConfiguredTitle=Kerberos Não Configurado
bypassKerberosDetail=Ou você não possui uma sessão Kerberos ou o seu navegador não está configurado para usar o acesso do Kerberos. Por favor, clique em continuar para fazer o login no através de outros meios
kerberosNotSetUp=Kerberos não está configurado. Você não pode acessar a aplicação.
registerTitle=Registre-se
loginAccountTitle=Entrar na sua conta
loginTitle=Entrar em {0}
loginTitleHtml={0}
impersonateTitle={0} Personificar Usuário
impersonateTitleHtml=<strong>{0}</strong> Personificar Usuário
realmChoice=Domínio
unknownUser=Usuário desconhecido
loginTotpTitle=Configuração do autenticador móvel
loginProfileTitle=Atualizar Informações da Conta
loginTimeout=Você demorou muito para entrar. Por favor, recomece o processo de login.
oauthGrantTitle=Conceder acesso a {0}
oauthGrantTitleHtml={0}
errorTitle=Sentimos muito...
errorTitleHtml=<strong>Sentimos</strong> muito ...
emailVerifyTitle=Verificação de endereço de e-mail
emailForgotTitle=Esqueceu sua senha?
updatePasswordTitle=Atualizar senha
codeSuccessTitle=Código de sucesso
codeErrorTitle=Código de erro\: {0}
displayUnsupported=Tipo de exibição solicitado não suportado
browserRequired=Navegador necessário para realizar acesso
browserContinue=Navegador necessário para concluir o login
browserContinuePrompt=Abrir navegador e continuar o login? [s/n]:
browserContinueAnswer=s


termsTitle=Termos e Condições
termsText=<p>Termos e condições a serem definidos</p>
termsPlainText=Termos e condições a serem definidos.

recaptchaFailed=Recaptcha Inválido
recaptchaNotConfigured=O recaptcha é necessário, mas não foi configurado
consentDenied=Consentimento negado.

noAccount=Novo usuário?
username=Nome de usuário
usernameOrEmail=Nome de usuário ou e-mail
firstName=Primeiro nome
givenName=Primeiro nome
fullName=Nome completo
lastName=Sobrenome
familyName=Sobrenome
email=Endereço de e-mail
password=Senha
passwordConfirm=Confirme a senha
passwordNew=Nova senha
passwordNewConfirm=Confirmação de Nova Senha
rememberMe=Mantenha-me conectado
authenticatorCode=Código autenticador
address=Endereço
street=Logradouro
locality=Cidade ou localidade
region=Estado
postal_code=CEP
country=País
emailVerified=Endereço de e-mail verificado
website=Página da web
phoneNumber=Número de telefone
phoneNumberVerified=Número de telefone verificado
gender=Gênero
birthday=Data de nascimento
zoneinfo=Zona horária
gssDelegationCredential=Delegação de Credenciais GSS
logoutOtherSessions=Sair dos outros dispositivos

profileScopeConsentText=Perfil de usuário
emailScopeConsentText=Endereço de e-mail
addressScopeConsentText=Endereço
phoneScopeConsentText=Número de telefone
offlineAccessScopeConsentText=Acesso Offline
samlRoleListScopeConsentText=Meus Perfis de Acesso
rolesScopeConsentText=Perfis de acesso do usuário

restartLoginTooltip=Reiniciar o login

loginTotpIntro=Você precisa configurar um gerador de código de uso único para acessar esta conta
loginTotpStep1=Instale um dos seguintes aplicativos no seu celular:
loginTotpStep2=Abra o aplicativo e escaneie o código QR:
loginTotpStep3=Digite o código de uso único fornecido pelo aplicativo e clique em Ok para concluir a configuração.
loginTotpStep3DeviceName=Forneça um nome de dispositivo para ajudá-lo a gerenciar seus dispositivos de autenticação de dois fatores.
loginTotpManualStep2=Abra o aplicativo e digite a chave:
loginTotpManualStep3=Use os seguintes valores de configuração se o aplicativo permitir defini-los:
loginTotpUnableToScan=Não foi possível ler o código QR?
loginTotpScanBarcode=Escanear código QR?
loginCredential=Credencial
loginOtpOneTime=Código de uso único
loginTotpType=Tipo
loginTotpAlgorithm=Algoritmo
loginTotpDigits=Dígitos
loginTotpInterval=Intervalo
loginTotpCounter=Contador
loginTotpDeviceName=Nome do dispositivo

loginTotp.totp=Baseado em tempo
loginTotp.hotp=Baseado em contador

loginChooseAuthenticator=Selecione o método de login

oauthGrantRequest=Você concede esses privilégios de acesso?
inResource=em

emailVerifyInstruction1=Um e-mail com instruções para verificar o seu endereço de e-mail foi enviado para você.
emailVerifyInstruction2=Não recebeu um código de verificação em seu e-mail?
emailVerifyInstruction3=para reenviar o e-mail.

emailLinkIdpTitle=Vincular {0}
emailLinkIdp1=Um e-mail com instruções para vincular a conta {0} {1} com sua conta {2} foi enviado para você.
emailLinkIdp2=Não recebeu um código de verificação no e-mail?
emailLinkIdp3=para reenviar o e-mail.
emailLinkIdp4=Se você já verificou o email em outro navegador
emailLinkIdp5=para continuar.

backToLogin=&laquo; Voltar ao Login

emailInstruction=Digite seu nome de usuário ou endereço de e-mail e nós lhe enviaremos instruções sobre como criar uma nova senha.

copyCodeInstruction=Por favor, copie o código e cole-o em seu aplicativo:

pageExpiredTitle=A página expirou
pageExpiredMsg1=Para reiniciar o processo de login
pageExpiredMsg2=Para continuar o processo de login

personalInfo=Informações Pessoais:
role_admin=Admininstrador
role_realm-admin=Admininstrador do Domínio
role_create-realm=Criar domínio
role_create-client=Criar cliente
role_view-realm=Visualizar domínio
role_view-users=Visualizar usuários
role_view-applications=Visualizar aplicativos
role_view-clients=Visualizar clientes
role_view-events=Visualizar eventos
role_view-identity-providers=Visualizar provedores de identidade
role_manage-realm=Gerenciar domínio
role_manage-users=Gerenciar usuários
role_manage-applications=Gerenciar aplicativos
role_manage-identity-providers=Gerenciar provedores de identidade
role_manage-clients=Gerenciar clientes
role_manage-events=Gerenciar eventos
role_view-profile=Visualizar perfil
role_manage-account=Gerenciar conta
role_manage-account-links=Gerenciar vinculações de conta
role_read-token=Ler token
role_offline-access=Acesso offline
client_account=Conta
client_account-console=Console da Conta
client_security-admin-console=Console de Administração de Segurança
client_admin-cli=CLI de Administração
client_realm-management=Gerenciamento de Domínio
client_broker=Provedor de Identidade

requiredFields=Campos obrigatórios

invalidUserMessage=Nome de usuário ou senha inválida.
invalidUsernameMessage=Nome de usuário inválido.
invalidUsernameOrEmailMessage=Nome de usuário ou endereço de e-mail inválido.
invalidPasswordMessage=Senha inválida.
invalidEmailMessage=Endereço de e-mail inválido.
accountDisabledMessage=Conta desativada, por favor, contate um administrador.
accountTemporarilyDisabledMessage=Nome de usuário ou senha inválida.
accountPermanentlyDisabledMessage=Nome de usuário ou senha inválida.
accountTemporarilyDisabledMessageTotp=Código de uso único inválido.
accountPermanentlyDisabledMessageTotp=Código de uso único inválido.
expiredCodeMessage=Tempo de login expirado. Por favor, faça login novamente.
expiredActionMessage=Ação expirada. Por favor, continue com o login agora.
expiredActionTokenNoSessionMessage=Ação expirada.
expiredActionTokenSessionExistsMessage=Ação expirada. Por favor, comece novamente.

missingFirstNameMessage=Por favor, informe o primeiro nome.
missingLastNameMessage=Por favor, informe o sobrenome.
missingEmailMessage=Por favor, informe o endereço de e-mail.
missingUsernameMessage=Por favor, informe o nome de usuário.
missingPasswordMessage=Por favor, informe a senha.
missingTotpMessage=Por favor, informe o código de uso único.
missingTotpDeviceNameMessage=Por favor, informe o nome do dispositivo.
notMatchPasswordMessage=As senhas não coincidem.

invalidPasswordExistingMessage=Senha atual inválida.
invalidPasswordBlacklistedMessage=Senha inválida, devido a lista de exclusão.
invalidPasswordConfirmMessage=Senha de confirmação não coincide.
invalidTotpMessage=Código de uso único inválido.

usernameExistsMessage=Nome de usuário já existe.
emailExistsMessage=Endereço de e-mail já existe.

federatedIdentityExistsMessage=Usuário com {0} {1} já existe. Por favor, entre no gerenciamento de conta para vincular a conta.
federatedIdentityUnavailableMessage=Usuário {0} autenticado com o provedor de identidade {1} não existe. Por favor, entre em contato com um administrador.

confirmLinkIdpTitle=Conta já existente
federatedIdentityConfirmLinkMessage=Usuário com {0} {1} já existe. Como você quer continuar?
federatedIdentityConfirmReauthenticateMessage=Autenticar para vincular sua conta com {0}
nestedFirstBrokerFlowMessage=O usuário {0} {1} não está vinculado a nenhum usuário conhecido.
confirmLinkIdpReviewProfile=Revisar informações do perfil
confirmLinkIdpContinue=Vincular à conta existente

configureTotpMessage=Você precisa configurar seu celular com o autenticador Mobile para ativar sua conta.
updateProfileMessage=Você precisa atualizar o seu perfil de usuário para ativar sua conta.
updatePasswordMessage=Você precisa mudar sua senha para ativar sua conta.
resetPasswordMessage=Você precisa mudar sua senha.
verifyEmailMessage=Você precisa verificar o seu endereço de e-mail para ativar sua conta.
linkIdpMessage=Você precisa confirmar o seu endereço de e-mail para vincular sua conta com {0}.

emailSentMessage=Você deverá receber um e-mail em breve com mais instruções.
emailSendErrorMessage=Falha ao enviar e-mail, por favor, tente novamente mais tarde.

accountUpdatedMessage=Sua conta foi atualizada.
accountPasswordUpdatedMessage=Sua senha foi atualizada.

delegationCompleteHeader=Autenticação Bem Sucedida
delegationCompleteMessage=Você pode fechar esta janela do navegador e voltar ao seu aplicativo.
delegationFailedHeader=Falha na Autenticação
delegationFailedMessage=Você pode fechar esta janela do navegador e voltar ao aplicativo e tentar fazer login novamente.

noAccessMessage=Sem acesso

invalidPasswordMinLengthMessage=Senha inválida\: deve ter pelo menos {0} caracteres.
invalidPasswordMinDigitsMessage=Senha inválida\: deve conter pelo menos {0} número(s).
invalidPasswordMinLowerCaseCharsMessage=Senha inválida\: deve conter pelo menos {0} letra(s) minúscula(s).
invalidPasswordMinUpperCaseCharsMessage=Senha inválida\: deve conter pelo menos {0} letra(s) maiúscula(s).
invalidPasswordMinSpecialCharsMessage=Senha inválida\: deve conter pelo menos {0} caractere(s) especial(is).
invalidPasswordNotUsernameMessage=Senha inválida\: não pode ser igual ao nome de usuário
invalidPasswordNotContainsUsernameMessage=Senha inválida\: não pode conter o nome de usuário.
invalidPasswordNotEmailMessage=Senha inválida: não pode ser igual ao endereço de e-mail.
invalidPasswordRegexPatternMessage=Senha inválida\: não corresponde ao(s) padrão(ões) de expressão regular.
invalidPasswordHistoryMessage=Senha inválida\: não pode ser igual a qualquer uma da(s) última(s) {0} senha(s).
invalidPasswordGenericMessage=Senha inválida: a nova senha não cumpre as políticas de senha.

failedToProcessResponseMessage=Falha ao processar a resposta
httpsRequiredMessage=HTTPS necessário
realmNotEnabledMessage=Domínio desativado
invalidRequestMessage=Solicitação inválida
failedLogout=Falha ao sair
unknownLoginRequesterMessage=Solicitante de login desconhecido
loginRequesterNotEnabledMessage=Solicitante de login desativado
bearerOnlyMessage=Aplicativos configurados como Bearer-Only não têm permissão para iniciar o login pelo navegador
standardFlowDisabledMessage=O cliente não tem permissão para iniciar o login com o response_type informado. O fluxo padrão está desabilitado para o cliente.
implicitFlowDisabledMessage=O cliente não tem permissão para iniciar o login com o response_type informado. O fluxo implícito está desabilitado para o cliente.
invalidRedirectUriMessage=URI de redirecionamento inválido
unsupportedNameIdFormatMessage=NameIDFormat não suportado
invalidRequesterMessage=Solicitante inválido
registrationNotAllowedMessage=Cadastro não é permitido
resetCredentialNotAllowedMessage=Sem permissão de redefinição de credenciais

permissionNotApprovedMessage=Permissão não aprovada.
noRelayStateInResponseMessage=Sem estado de retransmissão na resposta do provedor de identidade.
insufficientPermissionMessage=Permissões insuficientes para vincular identidades.
couldNotProceedWithAuthenticationRequestMessage=Não foi possível proceder com a solicitação de autenticação ao provedor de identidade.
couldNotObtainTokenMessage=Não foi possível obter token do provedor de identidade.
unexpectedErrorRetrievingTokenMessage=Erro inesperado ao recuperar token do provedor de identidade.
unexpectedErrorHandlingResponseMessage=Erro inesperado ao tratar a resposta do provedor de identidade.
identityProviderAuthenticationFailedMessage=Falha na autenticação. Não foi possível autenticar com o provedor de identidade.
couldNotSendAuthenticationRequestMessage=Não foi possível enviar a solicitação de autenticação ao provedor de identidade.
unexpectedErrorHandlingRequestMessage=Erro inesperado ao tratar o pedido de autenticação ao provedor de identidade.
invalidAccessCodeMessage=Código de acesso inválido.
sessionNotActiveMessage=Sessão inativa.
invalidCodeMessage=Um erro correu, por favor, faça login novamente através da aplicação.
identityProviderUnexpectedErrorMessage=Erro inesperado durante a autenticação com o provedor de identidade
identityProviderMissingStateMessage=Parâmetro de estado ausente da resposta do provedor de identidades.
identityProviderNotFoundMessage=Não foi possível encontrar um provedor de identidade com o identificador.
identityProviderLinkSuccess=O seu endereço de e-mail foi confirmado com sucesso. Por favor, retorne à aba original e continue com o login.
staleCodeMessage=Esta página não é mais válida. Por favor, volte à aplicação e faça login novamente
realmSupportsNoCredentialsMessage=O domínio não suporta qualquer tipo de credencial.
credentialSetupRequired=Não é possível fazer o login, a configuração de credencial é necessária.
identityProviderNotUniqueMessage=O domínio suporta múltiplos provedores de identidade. Não foi possível determinar qual o provedor de identidade deve ser usado para autenticação.
emailVerifiedMessage=O seu endereço de e-mail foi confirmado.
staleEmailVerificationLink=O link em que você clicou é um link antigo e não é mais válido. Talvez você já tenha confirmado o seu endereço de e-mail.
identityProviderAlreadyLinkedMessage=A conta retornada do {0} já está vinculada a outro usuário.
confirmAccountLinking=Confirme o vinculação da conta {0} do provedor de identidade {1} à sua conta.
confirmEmailAddressVerification=Confirme a validade do endereço de e-mail {0}.
confirmExecutionOfActions=Execute a(s) seguinte(s) ação(ões)

backToApplication=&laquo; Voltar para o aplicativo
missingParameterMessage=Parâmetros ausentes\: {0}
clientNotFoundMessage=Cliente não encontrado.
clientDisabledMessage=Cliente desativado.
invalidParameterMessage=Parâmetro inválido\: {0}
alreadyLoggedIn=Você já está logado.
differentUserAuthenticated=Você já está autenticado como outro usuário ''{0}'' nesta sessão. Por favor, finalize a sessão primeiro.
brokerLinkingSessionExpired=A vinculção de conta do provedor de identidade foi solicitado, mas a sessão atual não é mais válida.
proceedWithAction=» Clique aqui para continuar

requiredAction.CONFIGURE_TOTP=Configurar Autenticação de Dois Fatores
requiredAction.TERMS_AND_CONDITIONS=Termos e Condições
requiredAction.UPDATE_PASSWORD=Atualizar Senha
requiredAction.UPDATE_PROFILE=Atualizar Perfil
requiredAction.VERIFY_EMAIL=Verificar Endereço de E-mail

doX509Login=Você será logado como\:
clientCertificate=Certificado X509 do cliente\:
noCertificate=[Sem Certificado]


pageNotFound=Página não encontrada
internalServerError=Ocorreu um erro interno no servidor

console-username=Nome de usuário:
console-password=Senha:
console-otp=Código de uso único:
console-new-password=Nova Senha:
console-confirm-password=Confirmação de Senha:
console-update-password=Você precisa atualizar a sua senha.
console-verify-email=Você precisa verificar o seu endereço de e-mail. Enviamos um e-mail para {0} que contém um código de verificação. Digite o código enviado no campo abaixo.
console-email-code=Código do e-mail:
console-accept-terms=Aceita os Termos? [s/n]:
console-accept=s

# Openshift messages
openshift.scope.user_info=Informações do usuário
openshift.scope.user_check-access=Informações de acesso do usuário
openshift.scope.user_full=Acesso Completo
openshift.scope.list-projects=Listar projetos

# SAML authentication
saml.post-form.title=Redirecionamento de Autenticação
saml.post-form.message=Redirecionando... Por favor, aguarde.
saml.post-form.js-disabled=O JavaScript está desabilitado. É altamente recomendável habilitá-lo. Clique no botão abaixo para continuar. 

#authenticators
otp-display-name=Aplicativo Autenticador
otp-help-text=Insira o código de verificação do aplicativo autenticador.
password-display-name=Senha
password-help-text=Faça o login digitando sua senha.
auth-username-form-display-name=Nome de usuário
auth-username-form-help-text=Faça o login digitando seu nome de usuário.
auth-username-password-form-display-name=Nome de usuário e senha
auth-username-password-form-help-text=Faça o login digitando seu nome de usuário e senha.

# WebAuthn
webauthn-display-name=Chave de Segurança
webauthn-help-text=Use sua chave de segurança para fazer login.
webauthn-passwordless-display-name=Chave de Segurança
webauthn-passwordless-help-text=Use sua chave de segurança para fazer login sem senha.
webauthn-login-title=Login com Chave de Segurança
webauthn-registration-title=Registrar Chave de Segurança
webauthn-available-authenticators=Autenticadores disponíveis
webauthn-unsupported-browser-text=WebAuthn não é suportada pelo seu navegador. Tente outro navegador ou entre em contato com um administrador.

# WebAuthn Error
webauthn-error-title=Erro de Chave de Segurança
webauthn-error-registration=Falha ao registrar sua Chave de Segurança.
webauthn-error-api-get=Falha ao autenticar usando a Chave de Segurança.
webauthn-error-different-user=O primeiro usuário autenticado não corresponde àquele autenticado pela Chave de Segurança.
webauthn-error-auth-verification=O resultado da autenticação da Chave de Segurança é inválido.<br /> {0}
webauthn-error-register-verification=O resultado do registro da Chave de Segurança é inválido.<br /> {0}
webauthn-error-user-not-found=Usuário desconhecido autenticado pela Chave de Segurança.

# Identity provider
identity-provider-redirector=Conecte-se com outro Provedor de Identidade
identity-provider-login-label=Ou entre com

finalDeletionConfirmation=Se você apagar a sua conta, ela não poderá ser recuperada. Para manter a sua conta, clique em Cancelar.
irreversibleAction=Esta ação é irreversível
deleteAccountConfirm=Confirmação de descadastramento

deletingImplies=Apagar a sua conta implica em:
errasingData=Remover todos os seus dados
loggingOutImmediately=Sair da aplicação imediatamente
accountUnusable=Qualquer uso subsequente da aplicação não será possível com esta conta
userDeletedSuccessfully=Usuário excluído com sucesso

readOnlyUsernameMessage=Você não pode atualizar o seu nome de usuário, uma vez que é apenas de leitura.

# new RP-initiated logout
frontchannel-logout.title=Saindo
frontchannel-logout.message=Você está saindo dos seguintes aplicativos
logoutConfirmTitle=Saindo
logoutConfirmHeader=Você realmente deseja sair?
doLogout=Sair

