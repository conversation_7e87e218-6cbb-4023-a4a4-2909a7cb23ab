package otbs.vehicule.service;

import java.io.IOException;
import java.nio.file.Paths;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.nio.file.Path;
import otbs.vehicule.dto.VidangeDto;
import otbs.vehicule.model.Vehicule;
import otbs.vehicule.model.Vidange;
import otbs.vehicule.repository.VehiculeRepo;
import otbs.vehicule.repository.VidangeRepo;
import jakarta.transaction.Transactional;

@Service
public class VidangeService {
    private final VidangeRepo vidangeRepo;
    private final VehiculeRepo vehiculeRepo;
    @Value("${storage.path}")
    private String storagePath;
    private final FileStorageService fileStorageService;
    private static final String MONTANT_KEY = "montant";
    private static final String MOIS_KEY = "mois";
    
    public VidangeService(VidangeRepo vidangeRepo, VehiculeRepo vehiculeRepo, FileStorageService fileStorageService) {
        this.vidangeRepo = vidangeRepo;
        this.vehiculeRepo = vehiculeRepo;
        this.fileStorageService=fileStorageService;
    }

    // Conversion d'Entité Vidange vers DTO
    public static VidangeDto entityToDTO(Vidange vidange) {
        VidangeDto vidangeDTO = new VidangeDto();
        vidangeDTO.setIdVidange(vidange.getIdVidange());
        vidangeDTO.setDate(vidange.getDate());
        vidangeDTO.setKilometrage(vidange.getKilometrage());
        vidangeDTO.setKilometrageProchainVidange(vidange.getKilometrageProchainVidange());
        vidangeDTO.setMontant(vidange.getMontant());
        vidangeDTO.setFilePath(vidange.getFilePath());
        vidangeDTO.setImmatriculation(vidange.getVehicule().getImmatriculation()); 
        return vidangeDTO;
    }
    


    public VidangeDto addVidange(Long idVehicule, VidangeDto vidangeDTO, String extension) {
        Vehicule vehicule = vehiculeRepo.findById(idVehicule)
                .orElseThrow(() -> new RuntimeException("Véhicule non trouvé"));
    
        // Créer une nouvelle vidange
        Vidange vidange = new Vidange();
        vidange.setDate(vidangeDTO.getDate());
        vidange.setKilometrage(vidangeDTO.getKilometrage());
        vidange.setKilometrageProchainVidange(vidangeDTO.getKilometrageProchainVidange());
        vidange.setMontant(vidangeDTO.getMontant());
        vidange.setVehicule(vehicule);
    
        // Sauvegarder la vidange pour générer l'ID
        Vidange savedVidange = vidangeRepo.save(vidange);
    
        // Définir le filePath avec l'extension et l'ID de la vidange
        String filePath = savedVidange.getIdVidange().toString() + "." + extension;
        savedVidange.setFilePath(filePath);
    
        // Sauvegarder la vidange avec le filePath mis à jour
        savedVidange = vidangeRepo.save(savedVidange);
    
        // Retourner l'objet VidangeDto
        return entityToDTO(savedVidange);
    }
    
    // Mettre à jour une vidange par ID
    public VidangeDto updateVidange(Long idVidange, VidangeDto vidangeDTO) {
        Optional<Vidange> optionalVidange = vidangeRepo.findByIdVidange(idVidange);
        if (optionalVidange.isPresent()) {
            Vidange vidange = optionalVidange.get();
            vidange.setDate(vidangeDTO.getDate());
            vidange.setKilometrage(vidangeDTO.getKilometrage());
            vidange.setKilometrageProchainVidange(vidangeDTO.getKilometrageProchainVidange());
            vidange.setMontant(vidangeDTO.getMontant());
            vidange = vidangeRepo.save(vidange);

            return entityToDTO(vidange);
        } else {
            return null; 
        }
    }

  @Transactional
public void removeVidange(Long idVidange) {
    // Récupérer la vidange
    VidangeDto vidangeDto = getByIdVidange(idVidange);
    if (vidangeDto == null) {
        throw new NoSuchElementException("Vidange avec l'ID " + idVidange + " non trouvée");
    }

    // Vérifie que filePath contient l'extension (ex: "10.pdf")
    String fileName = vidangeDto.getFilePath();
    if (fileName == null || fileName.isEmpty()) {
        throw new IllegalStateException("Fichier associé à la vidange introuvable");
    }

    // Construire le chemin complet du fichier
    Path filePath = Paths.get(storagePath, "Vidange", fileName);

    try {
        fileStorageService.delete(filePath);
    } catch (IOException e) {
        throw new IllegalStateException("Erreur lors de la suppression du fichier de la vidange", e);
    }

    // Supprimer la vidange de la base
    vidangeRepo.removeByIdVidange(idVidange);
}

    
    public List<VidangeDto> getVidangeByIdVehicule(Long idVehicule) {
        Vehicule vehicule = vehiculeRepo.findByIdVehicule(idVehicule)
                                       .orElseThrow(() -> new RuntimeException("Véhicule non trouvé"));
        List<Vidange> vidanges = vehicule.getVidangeList();
        
        // Avoid NullPointerException by initializing vidanges as an empty list if it is null
        if (vidanges == null) {
            vidanges = new ArrayList<>();
        }
        
        List<VidangeDto> vidangesDTO = new ArrayList<>();
        
        for (Vidange vidange : vidanges) {
            vidangesDTO.add(entityToDTO(vidange));
        }
        return vidangesDTO;
    }
    
        public VidangeDto getByIdVidange(Long idVidange) {
            Vidange vidange = vidangeRepo.getReferenceById(idVidange);
            return entityToDTO(vidange);
        }
        
        public List<VidangeDto> getAllVidange() {
            List<Vidange> vidanges = vidangeRepo.findAll();
            List<VidangeDto> vidangesDTO=new ArrayList<>();
            for (Vidange vidange : vidanges) {
                vidangesDTO.add(entityToDTO(vidange));
            }
            return vidangesDTO;
        }

        public double calculateTotalVidangeMontantForYear(int year) {
            List<Vidange> vidanges = vidangeRepo.findAllByYear(year);
            double total = 0;
            for (Vidange v : vidanges) {
                total += v.getMontant();
            }
            return total;
        }
        public List<VidangeDto> getVidangesEstimees(long kilometrageRange) {
            List<Vidange> vidanges = vidangeRepo.findVidangesWithKilometrageNearNext(kilometrageRange);
        
            // Convertir les Vidange en VidangeDto avec toList() (Java 16+)
            return vidanges.stream()
                    .map(vidange -> new VidangeDto(
                            vidange.getIdVidange(),
                            vidange.getDate(),
                            vidange.getKilometrage(),
                            vidange.getKilometrageProchainVidange(),
                            vidange.getMontant(),
                            vidange.getVehicule().getImmatriculation(),
                            vidange.getFilePath()
                    ))
                    .toList(); // remplacement ici
        }
        
                
        public long countVidangesInMonth() {
            Calendar cal = Calendar.getInstance();
            int currentMonth = cal.get(Calendar.MONTH);
            int currentYear = cal.get(Calendar.YEAR);
            cal.set(currentYear, currentMonth, 1); // Début du mois
            Date startDate = new Date(cal.getTimeInMillis());
            cal.set(currentYear, currentMonth, cal.getActualMaximum(Calendar.DAY_OF_MONTH)); // Fin du mois
            Date endDate = new Date(cal.getTimeInMillis());
            
            return vidangeRepo.countVidangesInMonth(startDate, endDate);
        }

        public long countVidangesInTrimester() {
            Calendar cal = Calendar.getInstance();
            int currentMonth = cal.get(Calendar.MONTH);
            int currentYear = cal.get(Calendar.YEAR);
            int startMonth = currentMonth - (currentMonth % 3); // Début du trimestre
            cal.set(currentYear, startMonth, 1);
            Date startDate = new Date(cal.getTimeInMillis());
            
            cal.set(currentYear, startMonth + 2, cal.getActualMaximum(Calendar.DAY_OF_MONTH)); // Fin du trimestre
            Date endDate = new Date(cal.getTimeInMillis());
            
            return vidangeRepo.countVidangesInTrimester(startDate, endDate);
        }

        public long countVidangesInYear() {
            Calendar cal = Calendar.getInstance();
            int currentYear = cal.get(Calendar.YEAR);
            cal.set(currentYear, Calendar.JANUARY, 1); // Début de l'année
            Date startDate = new Date(cal.getTimeInMillis());
            
            cal.set(currentYear, Calendar.DECEMBER, 31); // Fin de l'année
            Date endDate = new Date(cal.getTimeInMillis());
            
            return vidangeRepo.countVidangesInYear(startDate, endDate);
        }
            
        public List<Map<String, Object>> getMonthlyTotalByYear(int year) {
            return vidangeRepo.getTotalMontantGroupedByMonth(year).stream()
                    .map(row -> Map.of(MOIS_KEY, row[0], MONTANT_KEY, row[1]))
                    .toList();
        }
        
        
        public List<Map<String, Object>> getTrimesterTotalByYear(int year) {
            return vidangeRepo.getTotalMontantGroupedByTrimester(year).stream()
                    .map(row -> Map.of("trimestre", row[0], "MONTANT_KEY", row[1]))
                    .toList();
        }
        
        public List<Map<String, Object>> getWeeklyTotalByYear(int year) {
            return vidangeRepo.getTotalMontantGroupedByWeekAndMonth(year).stream()
                    .map(row -> Map.of("mois", row[0], "semaine", row[1], "MONTANT_KEY", row[2]))
                    .toList();
        }
        
          
        
  
            
}
          
      
