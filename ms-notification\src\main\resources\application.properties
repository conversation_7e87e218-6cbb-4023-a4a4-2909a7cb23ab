# Server configuration
spring.application.name=ms-notification
server.port=9004

# Configuration de la DB
spring.datasource.url=${DB_URL:*****************************************}
spring.datasource.username=${DB_USERNAME:parcauto}
spring.datasource.password=${DB_PASSWORD:parcauto}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Hikari Connection Pool
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000

# Configuration Swagger UI
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.docExpansion=none
springdoc.swagger-ui.defaultModelsExpandDepth=-1
springdoc.swagger-ui.displayRequestDuration=true
springdoc.swagger-ui.filter=true

# Eureka client configuration
eureka.client.service-url.defaultZone=${Eureka-url:http://localhost:9102/eureka}
eureka.client.enabled=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true

# Logging configuration
logging.level.root=INFO
logging.level.otbs.ms_notification=DEBUG
logging.level.com.netflix.discovery=DEBUG
logging.level.com.netflix.eureka=DEBUG
logging.level.org.springframework.security=DEBUG

# Actuator configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always

# Configuration OAuth2 - Resource Server (JWT)
spring.security.oauth2.resourceserver.jwt.issuer-uri=${ISSUER_URI:http://localhost:8080/realms/parc-auto}
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${JWK_SET_URI:${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs}
jwt.auth.converter.resource-id=${JWT_RESOURCE_ID:ms_notification}
jwt.auth.converter.principle-attribute=${JWT_PRINCIPLE_ATTRIBUTE:preferred_username}

# Configuration OAuth2 - Client (pour Swagger UI)
spring.security.oauth2.client.registration.keycloak.client-id=${KEYCLOAK_CLIENT_ID:ms_notification}
spring.security.oauth2.client.registration.keycloak.client-secret=${KEYCLOAK_CLIENT_SECRET:}
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.keycloak.scope=openid,profile,email
spring.security.oauth2.client.provider.keycloak.issuer-uri=${spring.security.oauth2.resourceserver.jwt.issuer-uri}

# Configuration Swagger UI avec OAuth2
springdoc.swagger-ui.oauth.client-id=${SWAGGER_CLIENT_ID:${spring.security.oauth2.client.registration.keycloak.client-id}}
springdoc.swagger-ui.oauth.client-secret=${SWAGGER_CLIENT_SECRET:}

# Configuration de Feign pour activer les fallbacks
feign.circuitbreaker.enabled=true
feign.client.config.default.connectTimeout=5000
feign.client.config.default.readTimeout=5000

# Configuration RabbitMQ
spring.rabbitmq.host=${RABBITMQ_HOST:localhost}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}

# Configuration Email
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true








