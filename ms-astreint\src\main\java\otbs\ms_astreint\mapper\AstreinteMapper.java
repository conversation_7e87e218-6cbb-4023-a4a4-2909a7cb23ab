package otbs.ms_astreint.mapper;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import otbs.ms_astreint.client.keycloak.KeycloakService;
import otbs.ms_astreint.dto.AstreinteDto;
import otbs.ms_astreint.model.Astreinte;
import otbs.ms_astreint.model.ConsultantAstreinte;

import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class AstreinteMapper {

    private final KeycloakService keycloakService;

    public AstreinteDto toDto(Astreinte astreinte) {
        if (astreinte == null) {
            return null;
        }

        AstreinteDto dto = new AstreinteDto();
        dto.setId(astreinte.getIdAstreinte());
        dto.setDateDebut(astreinte.getDateDebut());
        dto.setDateFin(astreinte.getDateFin());
        dto.setDescription(astreinte.getDescription());
        
        if (astreinte.getConsultants() != null) {
            dto.setConsultants(astreinte.getConsultants().stream()
                    .map(this::toConsultantDto)
                    .collect(Collectors.toList()));
        }
        
        return dto;
    }
    
    private AstreinteDto.ConsultantDto toConsultantDto(ConsultantAstreinte consultantAstreinte) {
        String consultantId = consultantAstreinte.getConsultant();
        String consultantName = keycloakService.getFullNameFromUserId(consultantId);
        
        return new AstreinteDto.ConsultantDto(
                consultantAstreinte.getIdConsultantAstreinte(),
                consultantId,
                consultantName,
                consultantAstreinte.getNiveauAstreinte()
        );
    }

    public List<AstreinteDto> toDtoList(List<Astreinte> astreintes) {
        return astreintes.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public Astreinte toEntity(AstreinteDto dto) {
        if (dto == null) {
            return null;
        }

        Astreinte astreinte = new Astreinte(dto.getDateDebut(), dto.getDateFin(), dto.getDescription());
        if (dto.getId() != null) {
            astreinte.setIdAstreinte(dto.getId());
        }
        
        // Les consultants seront ajoutés via la méthode ajouterConsultant
        if (dto.getConsultants() != null) {
            dto.getConsultants().forEach(consultantDto -> 
                astreinte.ajouterConsultant(consultantDto.getConsultant(), consultantDto.getNiveau())
            );
        }
        
        return astreinte;
    }
} 