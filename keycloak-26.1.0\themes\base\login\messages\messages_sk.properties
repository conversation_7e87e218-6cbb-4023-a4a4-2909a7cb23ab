doLogIn=Prihlásenie
doRegister=Registrácia
doRegisterSecurityKey=Registrácia
doCancel=Zrušiť
doSubmit=Odoslať
doBack=Späť
doYes=Áno
doNo=Nie
doContinue=Pokračovať
doIgnore=Ignorovať
doAccept=Potvrdiť
doDecline=Odmietnuť
doForgotPassword=Zabudli ste heslo?
doClickHere=Kliknite tu
doImpersonate=Prevteliť
doTryAgain=Skúste to znova
doTryAnotherWay=Skúste iný spôsob
doConfirmDelete=Potvrdiť vymazanie
errorDeletingAccount=Pri odstraňovaní účtu došlo k chybe
deletingAccountForbidden=Nemáte dostatočné oprávnenie na vymazanie vlastného účtu, kontaktujte administrátora.
kerberosNotConfigured=Kerberos nie je nakonfigurovaný
kerberosNotConfiguredTitle=Kerberos nie je nakonfigurovaný
bypassKerberosDetail=Buď nie ste prihlásený cez Kerberos, alebo v<PERSON><PERSON> nie je nastavený na prihlásenie do Kerberos. Kliknutím na tlačidlo Pokračovať sa prihláste iným spôsobom
kerberosNotSetUp=Kerberos nie je nastavený. Nemôžete sa prihlásiť.
registerTitle=Registrácia
loginAccountTitle=Prihláste sa do svojho účtu
loginTitle=Prihlásenie do {0}
loginTitleHtml={0}
impersonateTitle={0} prevteliť sa
impersonateTitleHtml=<strong>{0}</strong> Prevteliť sa</strong>
realmChoice=Realm
unknownUser=Neznámy používateľ
loginTotpTitle=Nastavenie mobilného autentifikátora
loginProfileTitle=Aktualizácia informácií o účte
loginIdpReviewProfileTitle=Aktualizácia informácií o účte
loginTimeout=Prihlasovanie trvalo príliš dlho. Prihlasovací proces začína od začiatku.
reauthenticate=Pre pokračovanie sa prosím prihláste znova
oauthGrantTitle=Poskytnúť prístup {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Uistite sa, že dôverujete {0}. Zistite, ako bude {0} narábať s vašimi údajmi. 
oauthGrantReview=Môžete si prezrieť 
oauthGrantTos=podmienky služby.
oauthGrantPolicy=zásady ochrany osobných údajov.
errorTitle=Je nám ľúto ...
errorTitleHtml=<strong>Ospravedlňujeme</strong> sa ...
emailVerifyTitle=Overenie e-mailom
emailForgotTitle=Zabudli ste heslo?
updateEmailTitle=Aktualizovať e-mail 
emailUpdateConfirmationSentTitle=Odoslaný potvrdzujúci e-mail
emailUpdateConfirmationSent=Na adresu {0} bol odoslaný potvrdzujúci e-mail. Na dokončenie aktualizácie e-mailu postupujte podľa pokynov.
emailUpdatedTitle=Email aktualizovaný
emailUpdated=Email účtu bol úspešne aktualizovaný na {0}.
updatePasswordTitle=Aktualizácia hesla
codeSuccessTitle=Kód úspechu
codeErrorTitle=Kód chyby\: {0}
displayUnsupported=Nepodporovaný typ displeja 
browserRequired=Pre prihlásenie je potrebný prehliadač
browserContinue=Pre dokončenie prihlásenia je potrebný prehliadač
browserContinuePrompt=Otvoriť prehliadač a pokračovať v prihlasovaní? [a/n]:
browserContinueAnswer=a

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Interný 
unknown=Neznámy

termsTitle=Zmluvné podmienky
termsText=<p>Zmluvné podmienky, ktoré sa majú definovať</p>
termsPlainText=Podmienky, ktoré sa majú definovať. 
termsAcceptanceRequired=Musíte súhlasiť s našimi podmienkami.
acceptTerms=Súhlasím s podmienkami

recaptchaFailed=Neplatné Recaptcha
recaptchaNotConfigured=Recaptcha sa vyžaduje, ale nie je nakonfigurovaná
consentDenied=Súhlas bol zamietnutý.

noAccount=Nový používateľ?
username=Prihlasovacie meno
usernameOrEmail=Prihlasovacie meno alebo e-mail
firstName=Meno
givenName=Meno pri narodení
fullName=Celé meno
lastName=Priezvisko
familyName=Rodné meno
email=E-mail
password=Heslo
passwordConfirm=Potvrdenie hesla
passwordNew=Nové heslo
passwordNewConfirm=Potvrdenie nového hesla
hidePassword=Skryť heslo
showPassword=Zobraziť heslo
rememberMe=Zapamätať si ma
authenticatorCode=Jednorazový kód
address=Adresa
street=Ulica
locality=Mesto alebo lokalita
region=Kraj
postal_code=PSČ
country=Štát
emailVerified=E-mail overený
website=Webová stránka 
phoneNumber=Telefónne číslo
phoneNumberVerified=Overené telefónne číslo
gender=Pohlavie
birthday=Dátum narodenia
zoneinfo=Časové pásmo
gssDelegationCredential=GSS delegované oprávnenie
logoutOtherSessions=Odhlásenie z iných zariadení 

profileScopeConsentText=Profil používateľa 
emailScopeConsentText=Emailová adresa
addressScopeConsentText=Adresa
phoneScopeConsentText=Telefónne číslo
offlineAccessScopeConsentText=Offline prístup
samlRoleListScopeConsentText=Moje role
rolesScopeConsentText=Užívateľské roly

restartLoginTooltip=Znovu spustiť prihlásenie

loginTotpIntro=Pre prístup k tomuto účtu je potrebné nastaviť generátor jednorazových kódov
loginTotpStep1=Nainštalujte <a href="https://freeotp.github.io/" target="_blank">FreeOTP</a> alebo Google Authenticator na mobil. Obidve aplikácie sú k dispozícii v <a href="https://play.google.com">Google Play</a> a Apple App Store.
loginTotpStep2=Otvorte aplikáciu a skenujte čiarový kód alebo zadajte kľúč
loginTotpStep3=Zadajte jednorazový kód poskytnutý aplikáciou a kliknutím na tlačidlo Odoslať dokončite nastavenie
loginTotpStep3DeviceName=Uveďte názov zariadenia, ktorý vám pomôže spravovať zariadenia OTP. 
loginTotpManualStep2=Otvorte aplikáciu a zadajte kľúč
loginTotpManualStep3=Používajte nasledujúce hodnoty konfigurácie, ak aplikácia umožňuje ich nastavenie
loginTotpUnableToScan=Nemožno skenovať?
loginTotpScanBarcode=Skenovať čiarový kód?
loginCredential=Prihlasovacie údaje 
loginOtpOneTime=Jednorazový kód
loginTotpType=Typ
loginTotpAlgorithm=Algoritmus
loginTotpDigits=Číslica
loginTotpInterval=Interval
loginTotpCounter=Počítadlo
loginTotpDeviceName=Názov zariadenia 

loginTotp.totp=Založené na čase
loginTotp.hotp=Založené na počítadle

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=Vyberte spôsob prihlásenia

oauthGrantRequest=Udeľujete tieto prístupové oprávnenia?
inResource=v

oauth2DeviceVerificationTitle=Prihlásenie zariadenia 
verifyOAuth2DeviceUserCode=Zadajte kód poskytnutý vaším zariadením a kliknite na tlačidlo Odoslať
oauth2DeviceInvalidUserCodeMessage=Neplatný kód, skúste to prosím znova.
oauth2DeviceExpiredUserCodeMessage=Kód stratil platnosť. Vráťte sa prosím k svojmu zariadeniu a skúste sa pripojiť znova.
oauth2DeviceVerificationCompleteHeader=Úspešné prihlásenie zariadenia
oauth2DeviceVerificationCompleteMessage=Môžete zavrieť toto okno prehliadača a vrátiť sa k svojmu zariadeniu.
oauth2DeviceVerificationFailedHeader=Prihlásenie zariadenia zlyhalo
oauth2DeviceVerificationFailedMessage=Môžete zavrieť toto okno prehliadača, vrátiť sa k svojmu zariadeniu a skúsiť sa pripojiť znova.
oauth2DeviceConsentDeniedMessage=Súhlas s pripojením zariadenia zamietnutý.
oauth2DeviceAuthorizationGrantDisabledMessage=Klientovi nie je povolené iniciovať OAuth 2.0 Device Authorization Grant. Tento flow je pre klienta zakázaný.

emailVerifyInstruction1=Bol Vám odoslaný e-mail s pokynmi na overenie vašej e-mailovej adresy.
emailVerifyInstruction2=Nezískali ste v e-maile overovací kód?
emailVerifyInstruction3=opätovne odoslať e-mail.

emailLinkIdpTitle=Odkaz {0}
emailLinkIdp1=Bol vám odoslaný e-mail s pokynmi na prepojenie účtu {0} {1} s vaším účtom {2}.
emailLinkIdp2=Nezískali ste v e-maile overovací kód?
emailLinkIdp3=opätovne poslať e-mail.
emailLinkIdp4=Ak ste už overili e-mail v inom prehliadači
emailLinkIdp5=pokračovať.

backToLogin=&laquo; Späť na prihlásenie

emailInstruction=Zadajte svoje používateľské meno alebo e-mailovú adresu a my vám zašleme pokyny na vytvorenie nového hesla.
emailInstructionUsername=Zadajte svoje používateľské meno a my vám zašleme pokyny na vytvorenie nového hesla.

copyCodeInstruction=Prosím skopírujte tento kód a vložte ho do vašej aplikácie:

pageExpiredTitle=Platnosť stránky vypršala
pageExpiredMsg1=Pre reštartovanie prihlasovacieho procesu
pageExpiredMsg2=Pokračovanie prihlasovacieho procesu

personalInfo=Osobné informácie:
role_admin=Administrátor
role_realm-admin=Realm administrátor
role_create-realm=Vytvoriť realm
role_create-client=Vytvoriť klienta
role_view-realm=Zobraziť realm
role_view-users=Zobraziť používateľov
role_view-applications=Zobraziť aplikácie
role_view-clients=Zobrazenie klientov
role_view-events=Zobraziť udalosti
role_view-identity-providers=Zobrazenie poskytovateľov identity
role_manage-realm=Spravovať realm
role_manage-users=Spravovať používateľov
role_manage-applications=Spravovať aplikácie
role_manage-identity-providers=Spravovať poskytovateľov identity
role_manage-clients=Spravovať klientov
role_manage-events=Spravovať udalosti
role_view-profile=Zobraziť profil
role_manage-account=Spravovať účty
role_manage-account-links=Spravovať odkazy na účty
role_read-token=Čítať token
role_offline-access=Offline prístup
client_account=Účet klienta
client_account-console=Užívateľská konzola
client_security-admin-console=Administrátorská bezpečnostná konzola klienta
client_admin-cli=Správca CLI
client_realm-management=Správa realmov klienta
client_broker=Broker

requiredFields=Povinné polia 

invalidUserMessage=Neplatné používateľské meno alebo heslo.
invalidUsernameMessage=Neplatné používateľské meno. 
invalidUsernameOrEmailMessage=Neplatné používateľské meno alebo e-mail.
invalidPasswordMessage=Neplatné heslo.
invalidEmailMessage=Neplatná e-mailová adresa.
accountDisabledMessage=Účet je zakázaný, kontaktujte administrátora.
accountTemporarilyDisabledMessage=Neplatné používateľské meno alebo heslo.
accountPermanentlyDisabledMessage=Neplatné používateľské meno alebo heslo.
accountTemporarilyDisabledMessageTotp=Neplatný kód autentifikátora.
accountPermanentlyDisabledMessageTotp=Neplatný kód autentifikátora.
expiredCodeMessage=Platnosť prihlásenia vypršala. Prihláste sa znova.
expiredActionMessage=Akcia vypršala. Pokračujte prihlásením.
expiredActionTokenNoSessionMessage=Akcia vypršala.
expiredActionTokenSessionExistsMessage=Platnosť vypršala. Začnite znova.
sessionLimitExceeded=Prekročený limit relácií 

missingFirstNameMessage=Zadajte krstné meno.
missingLastNameMessage=Zadajte priezvisko.
missingEmailMessage=Zadajte e-mail.
missingUsernameMessage=Zadajte používateľské meno.
missingPasswordMessage=Zadajte prosím heslo.
missingTotpMessage=Prosím, zadajte kód autentifikátora.
missingTotpDeviceNameMessage=Prosím, zadajte názov zariadenia. 
notMatchPasswordMessage=Heslá sa nezhodujú.

error-invalid-value=Neplatná hodnota. 
error-invalid-blank=Prosím uveďte hodnotu.
error-empty=Prosím uveďte hodnotu.
error-invalid-length=Dĺžka musí byť medzi {1} a {2}.
error-invalid-length-too-short=Minimálna dĺžka je {1}.
error-invalid-length-too-long=Maximálna dĺžka je {2}.
error-invalid-email=Neplatná e-mailová adresa.
error-invalid-number=Neplatné číslo.
error-number-out-of-range=Číslo musí byť medzi {1} a {2}.
error-number-out-of-range-too-small=Číslo musí mať minimálnu hodnotu {1}.
error-number-out-of-range-too-big=Číslo musí mať maximálnu hodnotu {2}.
error-pattern-no-match=Neplatná hodnota.
error-invalid-uri=Neplatná adresa URL.
error-invalid-uri-scheme=Neplatná schéma URL.
error-invalid-uri-fragment=Neplatný fragment URL.
error-user-attribute-required=Prosím, uveďte toto pole.
error-invalid-date=Neplatný dátum.
error-user-attribute-read-only=Toto pole je určené len na čítanie.
error-username-invalid-character=Hodnota obsahuje neplatný znak.
error-person-name-invalid-character=Hodnota obsahuje neplatný znak.
error-reset-otp-missing-id=Prosím, vyberte konfiguráciu OTP.

invalidPasswordExistingMessage=Neplatné existujúce heslo.
invalidPasswordBlacklistedMessage=Neplatné heslo: heslo je na čiernej listine.
invalidPasswordConfirmMessage=Potvrdenie hesla sa nezhoduje.
invalidTotpMessage=Neplatný kód autentifikátora.

usernameExistsMessage=Užívateľské meno už existuje.
emailExistsMessage=E-mail už existuje.

federatedIdentityExistsMessage=Používateľ s {0} {1} už existuje. Ak chcete prepojiť účet, prihláste sa na správu účtov.
federatedIdentityUnavailableMessage=Používateľ {0} autentifikovaný poskytovateľom identity {1} neexistuje. Kontaktujte svojho administrátora.
federatedIdentityUnmatchedEssentialClaimMessage=Identifikačný token vydaný poskytovateľom identity sa nezhoduje s nakonfigurovaným essential claim. Obráťte sa na svojho správcu.

confirmLinkIdpTitle=Účet už existuje
federatedIdentityConfirmLinkMessage=Používateľ s {0} {1} už existuje. Ako chcete pokračovať?
federatedIdentityConfirmReauthenticateMessage=Overiť prepojiť váš účet s {0}
nestedFirstBrokerFlowMessage=Užívateľ {0} {1} nie je prepojený so žiadnym známym používateľom.
confirmLinkIdpReviewProfile=Skontrolujte profil
confirmLinkIdpContinue=Pridať do existujúceho účtu

configureTotpMessage=Na aktiváciu vášho účtu musíte nastaviť aplikáciu Mobile Authenticator.
configureBackupCodesMessage=Pre aktiváciu vášho účtu musíte nastaviť záložné kódy.
updateProfileMessage=Ak chcete aktivovať svoj účet, musíte aktualizovať svoj užívateľský profil.
updatePasswordMessage=Ak chcete aktivovať svoj účet, musíte zmeniť heslo.
updateEmailMessage=Pre aktiváciu účtu musíte aktualizovať svoju e-mailovú adresu.
resetPasswordMessage=Potrebujete zmeniť svoje heslo.
verifyEmailMessage=Ak chcete aktivovať svoj účet, musíte overiť svoju e-mailovú adresu.
linkIdpMessage=Potrebujete si overiť svoju e-mailovú adresu a prepojiť svoj účet s {0}.

emailSentMessage=Zakrátko by ste mali dostať e-mail s ďalšími pokynmi.
emailSendErrorMessage=Nepodarilo sa odoslať e-mail, skúste to znova neskôr.

accountUpdatedMessage=Váš účet bol aktualizovaný.
accountPasswordUpdatedMessage=Vaše heslo bolo aktualizované.

delegationCompleteHeader=Úspešné prihlásenie
delegationCompleteMessage=Môžete zavrieť toto okno prehliadača a vrátiť sa do konzolovej aplikácie.
delegationFailedHeader=Prihlásenie zlyhalo
delegationFailedMessage=Môžete zavrieť toto okno prehliadača, vrátiť sa do konzolovej aplikácie a skúsiť sa prihlásiť znova.

noAccessMessage=Žiadny prístup

invalidPasswordMinLengthMessage=Neplatné heslo: minimálna dĺžka {0}.
invalidPasswordMaxLengthMessage=Neplatné heslo: maximálna dĺžka {0}.
invalidPasswordMinDigitsMessage=Neplatné heslo: musí obsahovať aspoň {0} číslic.
invalidPasswordMinLowerCaseCharsMessage=Neplatné heslo: musí obsahovať minimálne {0} malé písmená.
invalidPasswordMinUpperCaseCharsMessage=Neplatné heslo: musí obsahovať aspoň {0} veľké písmená.
invalidPasswordMinSpecialCharsMessage=Neplatné heslo: musí obsahovať aspoň {0} špeciálne znaky.
invalidPasswordNotUsernameMessage=Neplatné heslo: nesmie byť rovnaké ako používateľské meno.
invalidPasswordNotEmailMessage=Neplatné heslo: nesmie sa rovnať e-mailu.
invalidPasswordRegexPatternMessage=Neplatné heslo: nezhoduje sa vzormi regulárneho výrazu.
invalidPasswordHistoryMessage=Neplatné heslo: nesmie sa rovnať žiadnemu z posledných {0} hesiel.
invalidPasswordGenericMessage=Neplatné heslo: nové heslo nezodpovedá pravidlám hesiel.

failedToProcessResponseMessage=Nepodarilo sa spracovať odpoveď
httpsRequiredMessage=Vyžaduje sa HTTPS
realmNotEnabledMessage=Realm nie je povolený
invalidRequestMessage=Neplatná požiadavka
successLogout=Odhlásili ste sa
failedLogout=Odhlásenie zlyhalo
unknownLoginRequesterMessage=Neznámy žiadateľ o prihlásenie
loginRequesterNotEnabledMessage=Žiadateľ o prihlásenie nie je povolený
bearerOnlyMessage=Aplikácie bearer-only nesmú inicializovať prihlásenie pomocou prehliadača
standardFlowDisabledMessage=Klient nesmie iniciovať prihlásenie do prehliadača s daným typom odpovede. Štandardný tok je pre klienta zakázaný.
implicitFlowDisabledMessage=Klient nemôže iniciovať prihlásenie do prehliadača s daným typom odpovede. Implicitný tok je pre klienta zakázaný.
invalidRedirectUriMessage=Neplatné redirect uri
unsupportedNameIdFormatMessage=Nepodporovaný NameIDFormat
invalidRequesterMessage=Neplatný žiadateľ
registrationNotAllowedMessage=Registrácia nie je povolená
resetCredentialNotAllowedMessage=Obnovenie poverenia nie je povolené

permissionNotApprovedMessage=Povolenie nie je schválené.
noRelayStateInResponseMessage=Neexistuje relay state v odpovedi od poskytovateľa identity.
insufficientPermissionMessage=Nedostatočné povolenia na prepojenie identít.
couldNotProceedWithAuthenticationRequestMessage=Nemožno pokračovať s požiadavkou na autentifikáciu poskytovateľa identity.
couldNotObtainTokenMessage=Nemožno získať token od poskytovateľa identity.
unexpectedErrorRetrievingTokenMessage=Neočakávaná chyba pri získavaní tokenu od poskytovateľa identity.
unexpectedErrorHandlingResponseMessage=Neočakávaná chyba pri spracovaní odpovede od poskytovateľa identity.
identityProviderAuthenticationFailedMessage=Overenie zlyhalo. Nepodarilo sa autentizovať s poskytovateľom identity.
couldNotSendAuthenticationRequestMessage=Nemožno odoslať žiadosť o autentifikáciu poskytovateľovi identity.
unexpectedErrorHandlingRequestMessage=Neočakávaná chyba pri spracovaní žiadosti o autentifikáciu poskytovateľovi identity.
invalidAccessCodeMessage=Neplatný prístupový kód.
sessionNotActiveMessage=Session nie je aktívna.
invalidCodeMessage=Vyskytla sa chyba, prihláste sa znova prostredníctvom svojej aplikácie.
cookieNotFoundMessage=Súbor cookie nebol nájdený. Uistite sa, že sú vo vašom prehliadači povolené súbory cookie.
insufficientLevelOfAuthentication=Požadovaná úroveň overenia nebola splnená.
identityProviderUnexpectedErrorMessage=Neočakávaná chyba pri autentifikácii s poskytovateľom identity
identityProviderMissingStateMessage=V odpovedi poskytovateľa identity chýba parameter state.
identityProviderMissingCodeOrErrorMessage=V odpovedi poskytovateľa identity chýba parameter code alebo error.
identityProviderInvalidResponseMessage=Neplatná odpoveď od poskytovateľa identity.
identityProviderInvalidSignatureMessage=Neplatný podpis v odpovedi od poskytovateľa identity.
identityProviderNotFoundMessage=Nepodarilo sa nájsť poskytovateľa identity s identifikátorom.
identityProviderLinkSuccess=Svoj e-mail ste úspešne overili. Vráťte sa späť do pôvodného prehliadača a pokračujte tam s prihlasovacími údajmi.
staleCodeMessage=Táto stránka už nie je platná, vráťte sa späť do aplikácie a znova sa prihláste
realmSupportsNoCredentialsMessage=Realm nepodporuje žiadny typ poverenia.
credentialSetupRequired=Nie je môžné sa prihlásiť, vyžaduje sa nastavenie prihlasovacích údajov.
identityProviderNotUniqueMessage=Realm podporuje viacerých poskytovateľov identity. Nepodarilo sa určiť, ktorý poskytovateľ totožnosti sa má používať na autentifikáciu.
emailVerifiedMessage=Vaša e-mailová adresa bola overená.
emailVerifiedAlreadyMessage=Vaša e-mailová adresa už bola overená.
staleEmailVerificationLink=Odkaz, na ktorý ste klikli, je starý odkaz a už nie je platný. Možno ste už overili svoj e-mail?
identityProviderAlreadyLinkedMessage=Federatívna identita vrátená {0} je už prepojená s iným používateľom.
confirmAccountLinking=Potvrďte prepojenie účtu {0} poskytovateľa totožnosti {1} s vaším účtom.
confirmEmailAddressVerification=Potvrďte platnosť e-mailovej adresy {0}.
confirmExecutionOfActions=Vykonajte nasledujúce akcie

backToApplication=&laquo; Späť na aplikáciu
missingParameterMessage=Chýbajúce parametre : {0}
clientNotFoundMessage=Klient sa nenašiel.
clientDisabledMessage=Klient bol zneplatnený.
invalidParameterMessage=Neplatný parameter : {0}
alreadyLoggedIn=Už ste prihlásený.
differentUserAuthenticated=V tejto relácii ste už boli overení ako iný používateľ '' {0} ''. Najskôr sa odhláste.
brokerLinkingSessionExpired=Požadované prepojenie s účtom brokera, ale aktuálna relácia už nie je platná.
proceedWithAction=&raquo; Ak chcete pokračovať, kliknite sem
acrNotFulfilled=Nesplnené požiadavky na overenie

requiredAction.CONFIGURE_TOTP=Konfigurácia OTP
requiredAction.TERMS_AND_CONDITIONS=Zmluvné podmienky
requiredAction.UPDATE_PASSWORD=Aktualizovať heslo
requiredAction.UPDATE_PROFILE=Aktualizovať profil
requiredAction.VERIFY_EMAIL=Overiť e-mail
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generovať kódy obnovy
requiredAction.webauthn-register-passwordless=Webauthn Passwordless Registrácia

invalidTokenRequiredActions=Požadované akcie zahrnuté v odkaze nie sú platné

doX509Login=Budete prihlásení ako\:
clientCertificate=certifikát klienta X509\:
noCertificate=[Bez certifikátu]


pageNotFound=Stránka nebola nájdená
internalServerError=Vyskytla sa interná chyba servera

console-username=Používateľské meno:
console-password=Heslo:
console-otp=Jednorazové heslo:
console-new-password=Nové heslo:
console-confirm-password=Potvrdiť heslo:
console-update-password=Vyžaduje sa aktualizácia hesla.
console-verify-email=Potrebujete overiť svoju e-mailovú adresu. Odoslali sme e-mail na adresu {0}, ktorý obsahuje overovací kód. Zadajte tento kód do poľa nižšie.
console-email-code=Emailový kód:
console-accept-terms=Prijímate podmienky? [a/n]:
console-accept=a

# Openshift messages
openshift.scope.user_info=Informácie o používateľovi
openshift.scope.user_check-access=Informácie o prístupe používateľa
openshift.scope.user_full=Plný prístup
openshift.scope.list-projects=Zoznam projektov

# SAML authentication
saml.post-form.title=Presmerovanie overenia
saml.post-form.message=Presmerovanie, počkajte prosím.
saml.post-form.js-disabled=JavaScript je vypnutý. Dôrazne odporúčame ho zapnúť. Ak chcete pokračovať, kliknite na tlačidlo nižšie. 
saml.artifactResolutionServiceInvalidResponse=Rozpoznanie SAML Artefaktu zlyhalo.

#authenticators
otp-display-name=Authenticator Application
otp-help-text=Zadajte overovací kód z aplikácie autentifikátora.
otp-reset-description=Ktorú konfiguráciu OTP treba odstrániť?
password-display-name=Heslo
password-help-text=Prihlásenie zadaním hesla.
auth-username-form-display-name=Používateľské meno
auth-username-form-help-text=Začnite prihlasovanie zadaním svojho používateľského mena
auth-username-password-form-display-name=Používateľské meno a heslo
auth-username-password-form-help-text=Prihláste sa zadaním svojho používateľského mena a hesla.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Overovací kód pre obnovenie
auth-recovery-authn-code-form-help-text=Zadajte overovací kód obnovy z vopred vygenerovaného zoznamu.
auth-recovery-code-info-message=Zadajte zadaný kód obnovy.
auth-recovery-code-prompt=Kód obnovy #{0}
auth-recovery-code-header=Prihlásenie pomocou overovacieho kódu obnovy
recovery-codes-error-invalid=Neplatný overovací kód obnovy
recovery-code-config-header=Overovacie kódy obnovy
recovery-code-config-warning-title=Tieto kódy obnovenia sa po opustení tejto stránky už nezobrazia
recovery-code-config-warning-message=Uistite sa, že ste si ich vytlačili, stiahli alebo skopírovali do správcu hesiel a uložili. Zrušenie tohto nastavenia odstráni tieto kódy na obnovenie z vášho účtu.
recovery-codes-print=Tlačiť
recovery-codes-download=Stiahnuť
recovery-codes-copy=Kopírovať
recovery-codes-copied=Kopírovať
recovery-codes-confirmation-message=Uložil som tieto kódy na bezpečné miesto
recovery-codes-action-complete=Ukončiť nastavenie
recovery-codes-action-cancel=Zrušiť nastavenie
recovery-codes-download-file-header=Uchovajte tieto kódy na obnovu na bezpečnom mieste.
recovery-codes-download-file-description=Kódy na obnovenie sú jednorázové prístupové kódy, ktoré vám umožnia prihlásiť sa do vášho účtu, ak nemáte prístup k autentifikátoru.
recovery-codes-download-file-date=Tieto kódy boli vygenerované dňa
recovery-codes-label-default=Kódy na obnovenie

# WebAuthn
webauthn-display-name=Prihlasovací kľúč
webauthn-help-text=Použite svoj prihlasovací kľúč na prihlásenie.
webauthn-passwordless-display-name=Prihlasovací kľúč
webauthn-passwordless-help-text=Použite svoj prihlasovací kľúč na prihlásenie bez hesla.
webauthn-login-title=Prihlásenie prihlasovacím kľúčom
webauthn-registration-title=Registrácia prihlasovacím kľúčom
webauthn-available-authenticators=Dostupné prihlasovacie kľúče
webauthn-unsupported-browser-text=WebAuthn nie je podporovaný týmto prehliadačom. Skúste iný alebo kontaktujte svojho administrátora.
webauthn-doAuthenticate=Prihlásenie pomocou prihlasovacieho kľúča
webauthn-createdAt-label=Vytvorené

# WebAuthn Error
webauthn-error-title=Chybný prihlasovací kľúč
webauthn-error-registration=Nepodarilo sa zaregistrovať prihlasovací kľúč.<br/> {0}
webauthn-error-api-get=Nepodarilo sa overiť pomocou prihlasovacieho kľúča.<br/> {0}
webauthn-error-different-user=Prvý overený používateľ nie je ten, ktorý bol overený prihlasovacím kľúčom.
webauthn-error-auth-verification=Výsledok overenia prihlasovacieho kľúča je neplatný.<br/> {0}
webauthn-error-register-verification=Výsledok registrácie prihlasovacieho kľúča je neplatný.<br/> {0}
webauthn-error-user-not-found=Neznámy používateľ overený prihlasovacím kľúčom.

# Identity provider
identity-provider-redirector=Pripojenie k inému poskytovateľovi identít
identity-provider-login-label=Prihláste sa pomocou
idp-email-verification-display-name=Overenie e-mailu
idp-email-verification-help-text=Pripojte svoj účet overením e-mailu.
idp-username-password-form-display-name=Prihlasovacie meno a heslo
idp-username-password-form-help-text=Pripojte svoj účet prihlásením.

finalDeletionConfirmation=Ak vymažete svoj účet, nebude možné ho obnoviť. Ak si chcete účet ponechať, kliknite na tlačidlo Zrušiť.
irreversibleAction=Táto akcia je nezvratná
deleteAccountConfirm=Potvrdenie odstránenia účtu

deletingImplies=Odstránenie vášho účtu znamená:
errasingData=Vymazanie všetkých vašich údajov
loggingOutImmediately=Okamžité odhlásenie
accountUnusable=Žiadne ďalšie použitie aplikácie nebude s týmto účtom možné
userDeletedSuccessfully=Užívateľ bol úspešne odstránený

access-denied=Prístup zamietnutý
access-denied-when-idp-auth=Prístup zamietnutý pri overovaní pomocou {0}

frontchannel-logout.title=Odhlásenie
frontchannel-logout.message=Odhlasujete sa z nasledujúcich aplikácií
logoutConfirmTitle=Odhlásenie
logoutConfirmHeader=Chcete sa odhlásiť?
doLogout=Odhlásiť

readOnlyUsernameMessage=Nemôžete aktualizovať svoje používateľské meno, pretože je iba na čítanie.
error-invalid-multivalued-size=Atribút {0} musí mať najmenej {1} {1,choice,0#hodnôt|1#hodnotu|1<hodnoty|4<hodnôt} a najviac {2} {1,choice,0#hodnôt|1#hodnotu|1<hodnoty|4<hodnôt}.