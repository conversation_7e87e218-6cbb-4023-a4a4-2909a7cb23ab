emailVerificationSubject=Vérification du courriel
emailVerificationBody=Quelqu''un vient de créer un compte "{2}" avec votre courriel. Si vous êtes à l''origine de cette requête, veuillez cliquer sur le lien ci-dessous afin de vérifier votre adresse de courriel\n\n{0}\n\nCe lien expire dans {3}.\n\nSinon, veuillez ignorer ce message.
emailVerificationBodyHtml=<p>Quelqu''un vient de créer un compte "{2}" avec votre courriel. Si vous êtes à l''origine de cette requête, veuillez cliquer sur le lien ci-dessous afin de vérifier votre adresse de courriel</p><p><a href="{0}">Lien pour confirmer l''adresse e-mail</a></p><p>Ce lien expire dans {3}.</p><p>Sinon, veuillez ignorer ce message.</p>
emailUpdateConfirmationSubject=Vérification du nouveau courriel
emailUpdateConfirmationBody=Afin d''utiliser le courriel {1} dans votre compte {2}, cliquez sur le lien ci-dessous\n\n{0}\n\nCe lien expire dans {3}.\n\nSinon, veuillez ignorer ce message.
emailUpdateConfirmationBodyHtml=<p>Afin d''utiliser le courriel {1} dans votre compte {2}, cliquez sur le lien ci-dessous</p><p><a href="{0}">{0}</a></p><p>Ce lien expirera dans {3}.</p><p>Sinon, veuillez ignorer ce message.</p>
identityProviderLinkSubject=Lien {0}
identityProviderLinkBody=Quelqu''un souhaite lier votre compte "{1}" au compte "{0}" de l''utilisateur {2} . Si vous êtes à l''origine de cette requête, veuillez cliquer sur le lien ci-dessous pour lier les comptes\n\n{3}\n\nCe lien expire dans {5}.\n\nSinon, veuillez ignorer ce message ; aucun changement ne sera effectué sur votre compte. Si vous liez les comptes, vous pourrez vous connecter à {1} via {0}.
identityProviderLinkBodyHtml=<p>Quelqu''un souhaite lier votre compte <b>{1}</b> au compte <b>{0}</b> de l''utilisateur {2}. Si vous êtes à l''origine de cette requête, veuillez cliquer sur le lien ci-dessous pour lier les comptes</p><p><a href="{3}">Lien pour confirmer la liaison des comptes</a></p><p>Ce lien expire dans {5}.</p><p>Sinon, veuillez ignorer ce message ; aucun changement ne sera effectué sur votre compte. Si vous liez les comptes, vous pourrez vous connecter à {1} via {0}.</p>
passwordResetSubject=Réinitialiser le mot de passe
passwordResetBody=Quelqu''un vient de demander une réinitialisation de mot de passe pour votre compte {2}. Si vous êtes à l''origine de cette requête, veuillez cliquer sur le lien ci-dessous pour le mettre à jour.\n\n{0}\n\nCe lien expire dans {3}.\n\nSinon, veuillez ignorer ce message ; aucun changement ne sera effectué sur votre compte.
passwordResetBodyHtml=<p>Quelqu''un vient de demander une réinitialisation de mot de passe pour votre compte {2}. Si vous êtes à l''origine de cette requête, veuillez cliquer sur le lien ci-dessous pour le mettre à jour.</p><p><a href="{0}">Lien pour réinitialiser votre mot de passe</a></p><p>Ce lien expire dans {3}.</p><p>Sinon, veuillez ignorer ce message ; aucun changement ne sera effectué sur votre compte.</p>
executeActionsSubject=Mettre à jour votre compte
executeActionsBody=Votre administrateur vient de demander une mise à jour de votre compte {2} pour réaliser les actions suivantes : {3}. Veuillez cliquer sur le lien ci-dessous afin de commencer le processus.\n\n{0}\n\nCe lien expire dans {4}.\n\nSi vous n''êtes pas à l''origine de cette requête, veuillez ignorer ce message ; aucun changement ne sera effectué sur votre compte.
executeActionsBodyHtml=<p>Votre administrateur vient de demander une mise à jour de votre compte {2} pour réaliser les actions suivantes : {3}. Veuillez cliquer sur le lien ci-dessous afin de commencer le processus.</p><p><a href="{0}">Lien pour la mise à jour du compte</a></p><p>Ce lien expire dans {4}.</p><p>Si vous n''êtes pas à l''origine de cette requête, veuillez ignorer ce message ; aucun changement ne sera effectué sur votre compte.</p>
eventLoginErrorSubject=Erreur de connexion
eventLoginErrorBody=Une tentative de connexion a été détectée sur votre compte {0} depuis {1}. Si vous n''êtes pas à l''origine de cette requête, veuillez contacter votre administrateur.
eventLoginErrorBodyHtml=<p>Une tentative de connexion a été détectée sur votre compte {0} depuis {1}. Si vous n''êtes pas à l''origine de cette requête, veuillez contacter votre administrateur.</p>
eventRemoveTotpSubject=Suppression du OTP
eventRemoveTotpBody=Le OTP a été supprimé de votre compte {0} depuis {1}. Si vous n''étiez pas à l''origine de cette requête, veuillez contacter votre administrateur.
eventRemoveTotpBodyHtml=<p>Le OTP a été supprimé de votre compte {0} depuis {1}. Si vous n''étiez pas à l''origine de cette requête, veuillez contacter votre administrateur.</p>
eventUpdatePasswordSubject=Mise à jour du mot de passe
eventUpdatePasswordBody=Votre mot de passe pour votre compte {0} a été modifié depuis {1}. Si vous n''étiez pas à l''origine de cette requête, veuillez contacter votre administrateur.
eventUpdatePasswordBodyHtml=<p>Votre mot de passe pour votre compte {0} a été modifié depuis {1}. Si vous n''étiez pas à l''origine de cette requête, veuillez contacter votre administrateur.</p>
eventUpdateTotpSubject=Mise à jour du OTP
eventUpdateTotpBody=Le OTP a été mis à jour pour votre compte {0} depuis {1}. Si vous n''étiez pas à l''origine de cette requête, veuillez contacter votre administrateur.
eventUpdateTotpBodyHtml=<p>Le OTP a été mis à jour pour votre compte {0} depuis {1}. Si vous n''étiez pas à l''origine de cette requête, veuillez contacter votre administrateur.</p>
requiredAction.CONFIGURE_TOTP=Configurer un OTP
requiredAction.TERMS_AND_CONDITIONS=Conditions générale d''utilisation
requiredAction.UPDATE_PASSWORD=Mise à jour du mot de passe
requiredAction.UPDATE_PROFILE=Mise à jour du profile
requiredAction.VERIFY_EMAIL=Vérification de l''adresse courriel

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#secondes|1#seconde|1<secondes}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minutes|1#minute|1<minutes}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#heures|1#heure|1<heures}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#jours|1#jour|1<jours}
emailVerificationBodyCode=Veuillez vérifier votre adresse de courriel en saisissant le code suivant.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Veuillez vérifier votre adresse de courriel en saisissant le code suivant.</p><p><b>{0}</b></p>
