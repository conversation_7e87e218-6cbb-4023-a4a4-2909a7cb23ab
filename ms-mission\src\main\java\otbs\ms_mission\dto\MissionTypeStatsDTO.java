package otbs.ms_mission.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO pour les statistiques des missions par type.
 * Supporte les types granulaires (PREVENTIVE/CURATIVE) côté backend
 * tout en maintenant la compatibilité avec le frontend unifié (MAINTENANCE).
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MissionTypeStatsDTO {

    /**
     * Nombre de missions SITESURVEY.
     */
    private Long sitesurvey;

    /**
     * Nombre de missions VISITEAVANTVENTE.
     */
    private Long visiteavantvente;

    /**
     * Nombre de missions VISITEPREVENTIVE (granulaire backend).
     */
    private Long visitepreventive;

    /**
     * Nombre de missions VISITECURATIVE (granulaire backend).
     */
    private Long visitecurative;

    /**
     * Nombre de missions PROJET.
     */
    private Long projet;

    /**
     * Nombre total de missions.
     */
    private Long total;

    /**
     * Calcule le total des missions.
     *
     * @return Total calculé
     */
    public Long calculateTotal() {
        return (sitesurvey != null ? sitesurvey : 0L) +
               (visiteavantvente != null ? visiteavantvente : 0L) +
               (visitepreventive != null ? visitepreventive : 0L) +
               (visitecurative != null ? visitecurative : 0L) +
               (projet != null ? projet : 0L);
    }

    /**
     * Calcule le pourcentage pour un type donné.
     *
     * @param count Nombre pour ce type
     * @return Pourcentage (0-100)
     */
    public double calculatePercentage(Long count) {
        if (total == null || total == 0 || count == null) {
            return 0.0;
        }
        return (count.doubleValue() / total.doubleValue()) * 100.0;
    }

    /**
     * Calcule le pourcentage de missions SITESURVEY.
     *
     * @return Pourcentage SITESURVEY
     */
    public double getSitesurveyPercentage() {
        return calculatePercentage(sitesurvey);
    }

    /**
     * Calcule le pourcentage de missions VISITEAVANTVENTE.
     *
     * @return Pourcentage VISITEAVANTVENTE
     */
    public double getVisiteavantventePercentage() {
        return calculatePercentage(visiteavantvente);
    }

    /**
     * Calcule le pourcentage de missions VISITEPREVENTIVE.
     *
     * @return Pourcentage VISITEPREVENTIVE
     */
    public double getVisitepreventivePercentage() {
        return calculatePercentage(visitepreventive);
    }

    /**
     * Calcule le pourcentage de missions VISITECURATIVE.
     *
     * @return Pourcentage VISITECURATIVE
     */
    public double getVisitecurativePercentage() {
        return calculatePercentage(visitecurative);
    }

    /**
     * Calcule le pourcentage de missions PROJET.
     *
     * @return Pourcentage PROJET
     */
    public double getProjetPercentage() {
        return calculatePercentage(projet);
    }
    
    /**
     * Pour la compatibilité avec le frontend qui utilise le type unifié VISITEMAINTENANCE.
     * @return La somme des missions préventives et curatives
     */
    public Long getVisitemaintenance() {
        return (visitepreventive != null ? visitepreventive : 0L) + 
               (visitecurative != null ? visitecurative : 0L);
    }
    
    /**
     * Calcule le pourcentage de missions VISITEMAINTENANCE (unifié frontend).
     *
     * @return Pourcentage VISITEMAINTENANCE
     */
    public double getVisitemaintenancePercentage() {
        return calculatePercentage(getVisitemaintenance());
    }
}
