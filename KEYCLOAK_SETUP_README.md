# 🔐 Configuration Keycloak pour le Projet Parc Auto

Ce guide vous explique comment configurer Keycloak pour l'architecture de microservices du projet Parc Auto.

## 📋 Prérequis

- Keycloak 26.1.0 installé localement (dossier `keycloak-26.1.0`)
- Java 17+ installé
- Navigateur web

## 🚀 Étape 1 : Démarrage de Keycloak

### Démarrer Keycloak en mode développement

```bash
# Ouvrir un terminal dans le répertoire du projet
cd keycloak-26.1.0\bin

# Démarrer Keycloak (Windows)
.\kc.bat start-dev --http-port=8080

# Ou sur Linux/Mac
./kc.sh start-dev --http-port=8080
```

### Vérifier le démarrage

- Ouvrez votre navigateur sur : **http://localhost:8080**
- Vous devriez voir la page d'accueil de Keycloak

## 🏗️ Étape 2 : Accès à la Console d'Administration

### Se connecter à l'interface d'administration

1. Allez sur : **http://localhost:8080/admin**
2. Connectez-vous avec les identifiants par défaut :
   - **Username :** `admin`
   - **Password :** `admin`

> ⚠️ **Note :** Si c'est la première fois, Keycloak vous demandera de créer un compte administrateur.

## 🌍 Étape 3 : Création du Realm "parc-auto"

### Créer le nouveau realm

1. Dans la console d'administration, cliquez sur le menu déroulant **"Master"** (en haut à gauche)
2. Cliquez sur **"Create Realm"**
3. Remplissez les informations :
   - **Realm name :** `parc-auto`
   - **Display name :** `Parc Auto`
   - **Enabled :** ✅ (coché)
4. Cliquez sur **"Create"**

### Configurer les paramètres du realm

1. Allez dans **Realm Settings** → **General**
2. Configurez les durées de session :
   - **Access Token Lifespan :** `1 hour`
   - **SSO Session Idle :** `30 minutes`
   - **SSO Session Max :** `10 hours`
3. Cliquez sur **"Save"**

## 👥 Étape 4 : Création des Clients pour les Microservices

Pour chaque microservice, créez un client avec les paramètres suivants :

### Client 1 : ms_reservation (Port 9006)

1. Allez dans **Clients** → **Create client**
2. **General Settings :**
   - **Client type :** `OpenID Connect`
   - **Client ID :** `ms_reservation`
3. **Capability config :**
   - **Client authentication :** ✅ ON
   - **Authorization :** ❌ OFF
   - **Standard flow :** ✅ ON
   - **Direct access grants :** ✅ ON
   - **Service accounts roles :** ✅ ON
4. **Login settings :**
   - **Valid redirect URIs :** 
     ```
     http://localhost:9006/*
     http://localhost:9006/swagger-ui/*
     ```
   - **Web origins :** `http://localhost:9006`
5. Cliquez sur **"Save"**

### Client 2 : ms_astreint (Port 9007)

Répétez les mêmes étapes avec :
- **Client ID :** `ms_astreint`
- **Valid redirect URIs :** 
  ```
  http://localhost:9007/*
  http://localhost:9007/swagger-ui/*
  ```
- **Web origins :** `http://localhost:9007`

### Client 3 : ms_vehicule (Port 9003)

Répétez les mêmes étapes avec :
- **Client ID :** `ms_vehicule`
- **Valid redirect URIs :** 
  ```
  http://localhost:9003/*
  http://localhost:9003/swagger-ui/*
  ```
- **Web origins :** `http://localhost:9003`

### Client 4 : ms_incident (Port 9002)

Répétez les mêmes étapes avec :
- **Client ID :** `ms_incident`
- **Valid redirect URIs :** 
  ```
  http://localhost:9002/*
  http://localhost:9002/swagger-ui/*
  ```
- **Web origins :** `http://localhost:9002`

### Client 5 : ms_mission (Port 9005)

Répétez les mêmes étapes avec :
- **Client ID :** `ms_mission`
- **Valid redirect URIs :** 
  ```
  http://localhost:9005/*
  http://localhost:9005/swagger-ui/*
  ```
- **Web origins :** `http://localhost:9005`

### Client 6 : ms_notification (Port 9004)

Répétez les mêmes étapes avec :
- **Client ID :** `ms_notification`
- **Valid redirect URIs :** 
  ```
  http://localhost:9004/*
  http://localhost:9004/swagger-ui/*
  ```
- **Web origins :** `http://localhost:9004`

## 🔑 Étape 5 : Récupération des Client Secrets

Pour chaque client créé :

1. Allez dans **Clients** → **[nom-du-client]** → **Credentials**
2. Copiez le **Client Secret**
3. Notez-le pour la configuration des microservices

### Exemple de secrets à noter :

```
ms_reservation: [SECRET_1]
ms_astreint: [SECRET_2]
ms_vehicule: [SECRET_3]
ms_incident: [SECRET_4]
ms_mission: [SECRET_5]
ms_notification: [SECRET_6]
```

## 🎭 Étape 6 : Création des Rôles

### Créer les rôles métier

1. Allez dans **Realm roles** → **Create role**
2. Créez les rôles suivants :

| Nom du Rôle | Description |
|--------------|-------------|
| `DIRECTEUR_GENERAL` | Directeur général - Accès complet |
| `RESPONSABLE_SERVICE_GENERAUX` | Responsable des services généraux |
| `CONSULTANT` | Consultant - Accès limité |
| `PROJECT_MANAGER` | Chef de projet |
| `FINANCE_MANAGER` | Responsable financier |

Pour chaque rôle :
- **Role name :** [nom du rôle]
- **Description :** [description appropriée]
- Cliquez sur **"Save"**

## 👤 Étape 7 : Création d'Utilisateurs de Test

### Créer un utilisateur administrateur

1. Allez dans **Users** → **Add user**
2. Remplissez les informations :
   - **Username :** `admin.parc`
   - **Email :** `<EMAIL>`
   - **First name :** `Admin`
   - **Last name :** `Parc Auto`
   - **Email verified :** ✅ ON
   - **Enabled :** ✅ ON
3. Cliquez sur **"Create"**

### Définir le mot de passe

1. Allez dans l'onglet **Credentials**
2. Cliquez sur **"Set password"**
3. **Password :** `admin123`
4. **Temporary :** ❌ OFF
5. Cliquez sur **"Save"**

### Assigner des rôles

1. Allez dans l'onglet **Role mapping**
2. Cliquez sur **"Assign role"**
3. Sélectionnez `DIRECTEUR_GENERAL`
4. Cliquez sur **"Assign"**

### Créer d'autres utilisateurs de test

Répétez le processus pour créer :

| Username | Rôle | Password |
|----------|------|----------|
| `consultant.test` | CONSULTANT | `test123` |
| `manager.test` | PROJECT_MANAGER | `test123` |
| `finance.test` | FINANCE_MANAGER | `test123` |
| `service.test` | RESPONSABLE_SERVICE_GENERAUX | `test123` |

## ⚙️ Étape 8 : Configuration des Microservices

### Mettre à jour les client secrets

Dans chaque microservice, mettez à jour le fichier `application.properties` :

```properties
# Exemple pour ms-reservation
springdoc.swagger-ui.oauth.client-secret=[CLIENT_SECRET_MS_RESERVATION]

# Exemple pour ms-astreint  
springdoc.swagger-ui.oauth.client-secret=[CLIENT_SECRET_MS_ASTREINT]
```

## ✅ Étape 9 : Vérification de la Configuration

### URLs importantes à tester

- **Console Admin :** http://localhost:8080/admin
- **Realm parc-auto :** http://localhost:8080/realms/parc-auto
- **Configuration OpenID :** http://localhost:8080/realms/parc-auto/.well-known/openid_configuration
- **Certificats JWT :** http://localhost:8080/realms/parc-auto/protocol/openid-connect/certs

### Test de connexion

1. Démarrez un microservice (ex: ms-reservation)
2. Allez sur http://localhost:9006/swagger-ui.html
3. Cliquez sur **"Authorize"**
4. Connectez-vous avec un utilisateur de test
5. Vérifiez que l'authentification fonctionne

## 🔧 Dépannage

### Problèmes courants

**Erreur "Invalid redirect URI" :**
- Vérifiez que les URLs de redirection sont correctement configurées dans le client

**Erreur "Client not found" :**
- Vérifiez que le client ID correspond exactement à celui configuré

**Erreur de certificat JWT :**
- Vérifiez que l'URL du realm est correcte dans la configuration des microservices

### Logs utiles

```bash
# Logs Keycloak
tail -f keycloak-26.1.0/data/log/keycloak.log

# Logs microservice
mvn spring-boot:run -Dlogging.level.org.springframework.security=DEBUG
```

## 🎉 Configuration Terminée !

Votre realm Keycloak est maintenant configuré et prêt à être utilisé avec l'architecture de microservices du projet Parc Auto.

### Prochaines étapes

1. Démarrer tous les microservices
2. Tester l'authentification via Swagger UI
3. Créer des utilisateurs supplémentaires selon les besoins
4. Configurer des politiques d'autorisation avancées si nécessaire
