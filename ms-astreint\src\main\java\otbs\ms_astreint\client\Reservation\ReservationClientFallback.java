package otbs.ms_astreint.client.reservation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import otbs.ms_astreint.client.reservation.dto.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Component
@Slf4j
public class ReservationClientFallback implements ReservationClient {

    @Override
    public ReservationDto addReservation(ReservationDto reservationDto) {
        log.warn("Fallback activé pour addReservation");
        if (reservationDto != null) {
            return reservationDto;
        }
        return createDefaultReservation();
    }

    @Override
    public FullReservationDto getReservationsByDescription(int page, int size, String description) {
        log.warn("Fallback activé pour getReservationsByDescription avec description: {}", description);
        return createDefaultFullReservation();
    }

    @Override
    public ConsommationCarburantDto addConsommationCarburant(ConsommationCarburantDto dto, MultipartFile file) {
        log.warn("Fallback activé pour addConsommationCarburant");
        if (dto != null) {
            return dto;
        }
        return createDefaultConsommationCarburant();
    }

    @Override
    public ConsommationCarburantDto updateConsommation(Long id, ConsommationCarburantDto dto, MultipartFile file) {
        log.warn("Fallback activé pour updateConsommation avec id: {}", id);
        if (dto != null) {
            dto.setId(id);
            return dto;
        }
        return createDefaultConsommationCarburant();
    }

    @Override
    public ConsommationTelepeageDto addNewConsommationTelepeageByReservation(ConsommationTelepeageDto consommationTelepeageDto) {
        log.warn("Fallback activé pour addNewConsommationTelepeageByReservation");
        if (consommationTelepeageDto != null) {
            return consommationTelepeageDto;
        }
        return createDefaultConsommationTelepeage();
    }

    @Override
    public ConsommationTelepeageDto updateConsommationTelepeageByReservation(ConsommationTelepeageDto consommationTelepeageDto) {
        log.warn("Fallback activé pour updateConsommationTelepeageByReservation");
        if (consommationTelepeageDto != null) {
            return consommationTelepeageDto;
        }
        return createDefaultConsommationTelepeage();
    }

    @Override
    public TicketRestaurantDto addTicket(TicketRestaurantDto dto) {
        log.warn("Fallback activé pour addTicket");
        if (dto != null) {
            return dto;
        }
        return createDefaultTicketRestaurant();
    }

    @Override
    public TicketRestaurantDto updateTicketByReservationId(Long reservationId, TicketRestaurantDto dto) {
        log.warn("Fallback activé pour updateTicketByReservationId avec reservationId: {}", reservationId);
        if (dto != null) {
            dto.setReservationId(reservationId);
            return dto;
        }
        return createDefaultTicketRestaurant();
    }

    @Override
    public List<TelepeageDto> getAllTelepeages() {
        log.warn("Fallback activé pour getAllTelepeages");
        return new ArrayList<>();
    }

    @Override
    public List<VehiculeDto> getAllAvailableCarsByPeriod(LocalDateTime dateDebut, LocalDateTime dateFin) {
        log.warn("Fallback activé pour getAllAvailableCarsByPeriod avec période: {} - {}", dateDebut, dateFin);
        return new ArrayList<>();
    }

    @Override
    public void deleteReservation(Long reservationId) {
        log.warn("Fallback activé pour deleteReservation avec id: {}", reservationId);
    }


    @Override
    public TelepeageDto getTelepeageByReservation(Long reservationId) {
        log.warn("Fallback activé pour getTelepeageByReservation avec reservationId: {}", reservationId);
        return new TelepeageDto();
    }

    @Override
    public Double getConsommationParReservation(Long reservationId) {
        log.warn("Fallback activé pour getConsommationParReservation avec reservationId: {}", reservationId);
        return 0.0;
    }

    @Override
    public List<PaimentCarburantCashDto> getPaimentsByReservation(Long reservationId) {
        log.warn("Fallback activé pour getPaimentsByReservation avec reservationId: {}", reservationId);
        return new ArrayList<>();
    }

    @Override
    public PaimentCarburantCashDto uploadPaiement(PaimentCarburantCashDto dto) {
        log.warn("Fallback activé pour uploadPaiement");
        return new PaimentCarburantCashDto();
    }

    @Override
    public ConsommationCarburantDto ajouterConsommationCash(ConsommationCarburantDto dto, MultipartFile file) {
        log.warn("Fallback activé pour ajouterConsommationCash");
        return createDefaultConsommationCarburant();
    }

    @Override
    public ConsommationCarburantDto reimburseCash(Long id) {
        log.warn("Fallback activé pour reimburseCash avec id: {}", id);
        return createDefaultConsommationCarburant();
    }

    @Override
    public List<ConsommationCarburantDto> getConsommationsByReservation(Long reservationId) {
        log.warn("Fallback activé pour getConsommationsByReservation avec reservationId: {}", reservationId);
        return new ArrayList<>();
    }

    @Override
    public List<ConsommationCarburantDto> getConsommationsByReservationAndType(Long reservationId, TypeConsommation type) {
        log.warn("Fallback activé pour getConsommationsByReservationAndType avec reservationId: {} et type: {}", reservationId, type);
        return new ArrayList<>();
    }

    @Override
    public CarteCarburantDto getCarteCarburantById(Long id) {
        log.warn("Fallback activé pour getCarteCarburantById avec id: {}", id);
        return new CarteCarburantDto();
    }

    @Override
    public List<CarteCarburantDto> getAllCartesCarburant() {
        log.warn("Fallback activé pour getAllCartesCarburant");
        return new ArrayList<>();
    }


    private ReservationDto createDefaultReservation() {
        return new ReservationDto(
            LocalDateTime.now(),                   // dateDebut
            LocalDateTime.now().plusHours(1),      // dateFin
            1L,                                    // idVehicule
            "Service indisponible"                 // description
        );
    }

    private FullReservationDto createDefaultFullReservation() {
        return new FullReservationDto(
            1L,                                    // reservationId
            new ArrayList<>(),                     // missionIds
            "Destination par défaut",              // destination
            LocalDateTime.now(),                   // dateDebut
            LocalDateTime.now().plusHours(1),      // dateFin
            "Immatriculation par défaut",          // immatriculation
            new ArrayList<>(),                     // numerosCartes
            "Badge par défaut",                    // numeroBadge
            0,                                     // nombreTicketResto
            0.0,                                   // frais
            "Service indisponible",                // description
            "user-default",                        // creatorId
            "Projet par défaut"                    // projectName
        );
    }

    private ConsommationCarburantDto createDefaultConsommationCarburant() {
        return new ConsommationCarburantDto(
            1L,                                    // id
            new java.util.Date(),                  // date
            0.0,                                   // montantSortie
            0.0,                                   // montantConsommer
            "default-file-path",                   // filePath
            TypeConsommation.CASH,                 // typeConsommation
            1L,                                    // reservationId
            null,                                  // carteCarburantId
            null                                   // paiementCarburantCashId
        );
    }

    private ConsommationTelepeageDto createDefaultConsommationTelepeage() {
        return new ConsommationTelepeageDto(
            1L,                                    // id
            0.0,                                   // montant
            null,                                  // telepeageId
            1L                                     // reservationId
        );
    }

    private TicketRestaurantDto createDefaultTicketRestaurant() {
        return new TicketRestaurantDto(
            1L,                                    // idTicketRestaurant
            0,                                     // nbTicket
            0.0,                                   // montant
            1L                                     // reservationId
        );
    }
}
