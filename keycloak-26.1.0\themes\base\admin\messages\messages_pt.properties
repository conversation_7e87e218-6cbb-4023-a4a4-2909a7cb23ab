invalidPasswordMinLengthMessage=Palavra-passe inválida: deve conter pelo menos {0} caracteres.
invalidPasswordMaxLengthMessage=Palavra-passe inválida: deve conter no máximo {0} caracteres.
invalidPasswordMinLowerCaseCharsMessage=Palavra-passe inválida: deve conter pelo menos {0} caracteres minúsculos.
invalidPasswordMinDigitsMessage=Palavra-passe inválida: deve conter pelo menos {0} caracteres numéricos.
invalidPasswordMinUpperCaseCharsMessage=Palavra-passe inválida: deve conter pelo menos {0} caracteres maiúsculos.
invalidPasswordMinSpecialCharsMessage=Palavra-passe inválida: deve conter pelo menos {0} caracteres especiais.
invalidPasswordNotUsernameMessage=Palavra-passe inválida: não deve ser igual ao nome de utilizador.
invalidPasswordNotEmailMessage=Palavra-passe inválida: não deve ser igual ao e-mail.
invalidPasswordRegexPatternMessage=Palavra-passe inválida: falha ao passar pelos padrões definidos.
invalidPasswordHistoryMessage=Palavra-passe inválida: não deve ser igual às últimas {0} palavras-passes.
invalidPasswordBlacklistedMessage=Palavra-passe inválida: palavra-passe está bloqueada.
invalidPasswordGenericMessage=Palavra-passe inválida: a nova palavra-passe nao respeita a política de palavras-passe.

ldapErrorEditModeMandatory=Modo de Edição é obrigatório
ldapErrorInvalidCustomFilter=Filtro LDAP não inicia com "(" ou não termina com ")".
ldapErrorConnectionTimeoutNotNumber=Tempo limite de conexão deve ser um número
ldapErrorReadTimeoutNotNumber=O tempo limite de leitura deve ser um número
ldapErrorMissingClientId=O ID do cliente tem que ser fornecido na configuração quando o Mapeamento de Funções do Domínio não é utilizado.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Não é possível preservar herança de grupos e usar tipo de associação de UID em simultâneo.
ldapErrorCantWriteOnlyForReadOnlyLdap=Não é possível definir modo de apenas escrita quando o provedor LDAP não suporta escrita
ldapErrorCantWriteOnlyAndReadOnly=Não é possível definir apenas escrita e apenas leitura em simultâneo
ldapErrorCantEnableStartTlsAndConnectionPooling=Não é possível ativar o StartTLS e o grupo de conexões.
ldapErrorCantEnableUnsyncedAndImportOff=Não é possível desativar a importação de utilizadores quando o modo do provedor LDAP não está sincronizado
ldapErrorMissingGroupsPathGroup=O grupo de caminhos de grupos não existe - crie primeiro o grupo no caminho especificado
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=A Política de Validação de Palavras-passe é aplicável apenas com o modo de edição GRAVÁVEL

clientRedirectURIsFragmentError=URIs de redirecionamento não podem conter fragmentos de URI
clientRootURLFragmentError=URL raiz não pode conter fragmentos de URL
clientRootURLIllegalSchemeError=URL raiz usa um esquema ilegal
clientBaseURLIllegalSchemeError=URL base usa um esquema ilegal
backchannelLogoutUrlIllegalSchemeError=URL de saída do backchannel usa um esquema ilegal
clientRedirectURIsIllegalSchemeError=Um URI de redirecionamento usa um esquema ilegal
clientBaseURLInvalid=URL base não é um URL válido
clientRootURLInvalid=URL raiz não é um URL válido
clientRedirectURIsInvalid=Um URI de redirecionamento não é um URI válido
backchannelLogoutUrlIsInvalid=O URL de saída do backchannel não é um URL válido


pairwiseMalformedClientRedirectURI=O cliente continha um URI de redirecionamento inválido.
pairwiseClientRedirectURIsMissingHost=Os URIs de redirecionamento do cliente devem conter um componente de anfitrião válido.
pairwiseClientRedirectURIsMultipleHosts=Sem um URI de identificador de setor configurado, os URIs de redirecionamento de cliente não devem conter vários componentes de anfitrião.
pairwiseMalformedSectorIdentifierURI=URI de identificador de setor malformado.
pairwiseFailedToGetRedirectURIs=Falha ao obter URIs de redirecionamento do URI do identificador de setor.
pairwiseRedirectURIsMismatch=Os URIs de redirecionamento do cliente não correspondem aos URIs de redirecionamento obtidos do URI do identificador de setor.

duplicatedJwksSettings="Usar JWKS" e "Usar URL JWKS" não podem estar ligados em simultâneo.

error-invalid-value=Valor inválido.
error-invalid-blank=Especifique o valor.
error-empty=Especifique o valor.
error-invalid-length=O atributo {0} deve ter um comprimento entre {1} e {2}.
error-invalid-length-too-short=O atributo {0} deve ter comprimento mínimo de {1}.
error-invalid-length-too-long=O atributo {0} deve ter comprimento máximo de {2}.
error-invalid-email=Endereço de e-mail inválido.
error-invalid-number=Número inválido.
error-number-out-of-range=O atributo {0} deve ser um número entre {1} e {2}.
error-number-out-of-range-too-small=O atributo {0} deve ter o valor mínimo de {1}.
error-number-out-of-range-too-big=O atributo {0} deve ter o valor máximo de {2}.
error-pattern-no-match=Valor inválido.
error-invalid-uri=URL inválido.
error-invalid-uri-scheme=Esquema de URL inválido.
error-invalid-uri-fragment=Fragmento de URL inválido.
error-user-attribute-required=Especifique o atributo {0}.
error-invalid-date=O atributo {0} é uma data inválida.
error-user-attribute-read-only=O atributo {0} é apenas de leitura.
error-username-invalid-character={0} contém caracteres inválidos.
error-person-name-invalid-character={0} contém caracteres inválidos.
error-invalid-multivalued-size=O atributo {0} tem que conter no mínimo {1} e no máximo {2} valores.
