doLogIn=ورود
doRegister=ثبت نام
doRegisterSecurityKey=ثبت نام
doCancel=لغو
doSubmit=ارسال
doBack=بازگشت
doYes=بله
doNo=خیر
doContinue=ادامه
doIgnore=نادیده‌گیری
doAccept=پذیرفتن
doDecline=رد کردن
doForgotPassword=رمز عبور خود را فراموش کرده اید؟
doClickHere=اینجا را کلیک کنید
doImpersonate=تقلید کردن
doTryAgain=تلاش مجدد
doTryAnotherWay=راه دیگری را امتحان کنید
doConfirmDelete=تایید حذف
errorDeletingAccount=خطایی هنگام حذف حساب رخ داد
deletingAccountForbidden=.شما مجوز کافی برای حذف حساب خود را ندارید، با ادمین تماس بگیرید
kerberosNotConfigured=Kerberos تنظیم نشده است
kerberosNotConfiguredTitle=Kerberos تنظیم نشده است
bypassKerberosDetail=یا توسط Kerberos وارد نشده اید یا مرورگر شما برای ورود به Kerberos تنظیم نشده است. لطفا روی ادامه کلیک کنید تا از راه های دیگر وارد شوید
kerberosNotSetUp=Kerberos راه اندازی نشده است. شما نمی توانید وارد شوید.
registerTitle=ثبت نام
loginAccountTitle=به حساب خود وارد شوید
loginTitle=به {0} وارد شوید
loginTitleHtml={0}
impersonateTitle={0} تقلید کاربر
impersonateTitleHtml=<strong>{0}</strong> تقلید کاربر
realmChoice=قلمرو
unknownUser=کاربر ناشناس
loginTotpTitle=راه اندازی دستگاه تأیید اعتبار موبایل
loginProfileTitle=به روز رسانی اطلاعات حساب
loginIdpReviewProfileTitle=به روز رسانی اطلاعات حساب
loginTimeout=.زمان تلاش برای ورود شما به پایان رسید. ورود از ابتدا شروع خواهد شد
reauthenticate=لطفاً برای ادامه دوباره احراز هویت کنید
oauthGrantTitle=اعطای دسترسی به {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=.با یادگیری نحوه مدیریت {0} با داده های شما، مطمئن شوید که به {0} اعتماد دارید
oauthGrantReview=شما می توانید بررسی کنید
oauthGrantTos=.شرایط استفاده از خدمات
oauthGrantPolicy=.سیاست حفظ حریم خصوصی
errorTitle=...متاسفیم
errorTitleHtml=...<strong>متاسفیم</strong> ما
emailVerifyTitle=تایید ایمیل
emailForgotTitle=رمز عبور خود را فراموش کرده اید؟
updateEmailTitle=ایمیل را به روز کنید
emailUpdateConfirmationSentTitle=ایمیل تایید فرستاده شد
emailUpdateConfirmationSent=.یک ایمیل تأیید به {0} ارسال شده است. برای تکمیل به‌روزرسانی ایمیل، باید دستورالعمل‌های قبلی را دنبال کنید
emailUpdatedTitle=ایمیل به روز شد
emailUpdated=.ایمیل حساب با موفقیت به {0} به روز شد
updatePasswordTitle=رمز عبور را به روز کنید
codeSuccessTitle=کد موفقیت
codeErrorTitle=کد خطا\: {0}
displayUnsupported=نوع نمایش درخواستی پشتیبانی نمی شود
browserRequired=برای ورود به سیستم مرورگر لازم است
browserContinue=برای تکمیل ورود به مرورگر نیاز است
browserContinuePrompt=مرورگر را باز کنید و به ورود ادامه دهید؟ [y/n]:
browserContinueAnswer=y

# Transports
usb=USB
nfc=NFC
bluetooth=بلوتوث
internal=داخلی
unknown=ناشناخته

termsTitle=شرایط و ضوابط
termsText=<p>شرایط و شرایطی که باید تعریف شوند</p>
termsPlainText=.شرایط و ضوابط باید تعریف شود

recaptchaFailed=Recaptcha نامعتبر است
recaptchaNotConfigured=Recaptcha مورد نیاز است، اما پیکربندی نشده است
consentDenied=.رضایت رد شد

noAccount=کاربر جدید هستید؟
username=نام کاربری
usernameOrEmail=نام کاربری یا پست الکترونیک
firstName=نام
givenName=لقب
fullName=نام کامل
lastName=نام خانوادگی
familyName=نام خانوادگی
email=ایمیل
password=رمز عبور
passwordConfirm=تائید رمز عبور
passwordNew=رمز عبور جدید
passwordNewConfirm=تائید رمز عبور جدید
rememberMe=مرا به خاطر بسپار
authenticatorCode=کد یکبار مصرف
address=آدرس
street=خیابان
locality=شهر یا محله
region=ایالت، استان یا منطقه
postal_code=کد پستی
country=کشور
emailVerified=ایمیل تأییده شده است
website=صفحه وب
phoneNumber=شماره تلفن
phoneNumberVerified=شماره تلفن تایید شد
gender=جنسیت
birthday=تاریخ تولد
zoneinfo=منطقه زمانی
gssDelegationCredential=اعتبارنامه نمایندگی GSS
logoutOtherSessions=از دستگاه های دیگر خارج شوید

profileScopeConsentText=مشخصات کاربر
emailScopeConsentText=آدرس ایمیل
addressScopeConsentText=نشانی
phoneScopeConsentText=شماره تلفن
offlineAccessScopeConsentText=دسترسی آفلاین
samlRoleListScopeConsentText=نقش های من
rolesScopeConsentText=نقش های کاربر

restartLoginTooltip=ورود مجدد را راه اندازی کنید

loginTotpIntro=برای دسترسی به این حساب، باید یک تولید کننده رمز یک بار مصرف راه اندازی کنید
loginTotpStep1=یکی از برنامه های زیر را روی موبایل خود نصب کنید:
loginTotpStep2=برنامه را باز کنید و بارکد را اسکن کنید:
loginTotpStep3=.کد یکبار مصرف ارائه شده توسط برنامه را وارد کنید و برای تکمیل تنظیمات روی ارسال کلیک کنید
loginTotpStep3DeviceName=یک نام دستگاه برای کمک به مدیریت دستگاه های OTP خود ارائه دهید
loginTotpManualStep2=:برنامه را باز کنید و کلید را وارد کنید
loginTotpManualStep3=:اگر برنامه اجازه تنظیم آنها را می دهد، از مقادیر پیکربندی زیر استفاده کنید
loginTotpUnableToScan=نمی توانید اسکن کنید؟
loginTotpScanBarcode=اسکن بارکد؟
loginCredential=اعتبارنامه
loginOtpOneTime=کد یکبار مصرف
loginTotpType=نوع
loginTotpAlgorithm=الگوریتم
loginTotpDigits=ارقام
loginTotpInterval=فاصله
loginTotpCounter=شمارنده
loginTotpDeviceName=نام دستگاه

loginTotp.totp=مبتنی بر زمان
loginTotp.hotp=مبتنی بر شمارنده

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=روش ورود را انتخاب کنید

oauthGrantRequest=آیا به این امتیازات دسترسی می دهید؟
inResource=در

oauth2DeviceVerificationTitle=ورود به دستگاه
verifyOAuth2DeviceUserCode=کد ارائه شده توسط دستگاه خود را وارد کرده و روی ارسال کلیک کنید
oauth2DeviceInvalidUserCodeMessage=.کد نامعتبر است، لطفا دوباره امتحان کنید
oauth2DeviceExpiredUserCodeMessage=.کد منقضی شده است. لطفاً به دستگاه خود برگردید و دوباره سعی کنید وصل شوید
oauth2DeviceVerificationCompleteHeader=ورود دستگاه با موفقیت انجام شد
oauth2DeviceVerificationCompleteMessage=.می توانید این پنجره مرورگر را ببندید و به دستگاه خود بازگردید
oauth2DeviceVerificationFailedHeader=ورود دستگاه ناموفق بود
oauth2DeviceVerificationFailedMessage=.می توانید این پنجره مرورگر را ببندید و به دستگاه خود بازگردید و دوباره سعی کنید وصل شوید
oauth2DeviceConsentDeniedMessage=.رضایت برای اتصال دستگاه رد شد
oauth2DeviceAuthorizationGrantDisabledMessage=مشتری مجاز به شروع OAuth 2.0 Device Authorization Grant نیست. جریان برای مشتری غیرفعال است.

emailVerifyInstruction1=.یک ایمیل حاوی دستورالعمل هایی برای تأیید آدرس ایمیل شما به آدرس شما {0} ارسال شده است
emailVerifyInstruction2=آیا کد تأیید در ایمیل خود دریافت نکرده اید؟
emailVerifyInstruction3=برای ارسال مجدد ایمیل

emailLinkIdpTitle=پیوند {0}
emailLinkIdp1=.یک ایمیل با دستورالعمل پیوند {0} حساب {1} با حساب {2} شما برای شما ارسال شده است
emailLinkIdp2=آیا کد تأیید در ایمیل خود دریافت نکرده اید؟
emailLinkIdp3=برای ارسال مجدد ایمیل
emailLinkIdp4=اگر قبلاً ایمیل را در مرورگر دیگری تأیید کرده اید
emailLinkIdp5=.ادامه

backToLogin=&laquo; بازگشت به صفحه ورود

emailInstruction=.نام کاربری یا آدرس ایمیل خود را وارد کنید و ما دستورالعمل هایی در مورد نحوه ایجاد رمز عبور جدید برای شما ارسال خواهیم کرد
emailInstructionUsername=.نام کاربری خود را وارد کنید و ما دستورالعمل هایی در مورد نحوه ایجاد رمز عبور جدید برای شما ارسال خواهیم کرد

copyCodeInstruction=:لطفا این کد را کپی کرده و در برنامه خود قرار دهید

pageExpiredTitle=صفحه منقضی شده است
pageExpiredMsg1=برای راه اندازی مجدد فرآیند ورود
pageExpiredMsg2=برای ادامه روند ورود

personalInfo=اطلاعات شخصی:
role_admin=ادمین
role_realm-admin=ادمین قلمرو
role_create-realm=ایجاد قلمرو
role_create-client=ایجاد مشتری
role_view-realm=مشاهده قلمرو
role_view-users=مشاهده کاربران
role_view-applications=مشاهده اپلیکیشن‌ها
role_view-clients=مشاهده مشتری‌ها
role_view-events=مشاهده رویداد
role_view-identity-providers=مشاهده ارائه‌دهندگان هویت
role_manage-realm=مدیریت قلمرو
role_manage-users=میدیریت کاربران
role_manage-applications=مدیریت اپلیکیشن‌ها
role_manage-identity-providers=مدیریت ارائه‌دهندگان هویت
role_manage-clients=مدیریت مشتریان
role_manage-events=مدیریت رویدادها
role_view-profile=مشاهده پروفایل
role_manage-account=میدریت حساب
role_manage-account-links=مدیریت پیوندهای حساب
role_read-token=خواندن Token
role_offline-access=دسترسی آفلاین
client_account=حساب
client_account-console=کنسول حساب
client_security-admin-console=کنسول مدیریت امنیت
client_admin-cli=ادمین CLI
client_realm-management=مدیریت قلمرو
client_broker=دلال

requiredFields=فیلدهای مورد نیاز

invalidUserMessage=.نام کاربری یا رمز عبور نامعتبر است
invalidUsernameMessage=.نام کاربری نامعتبر است
invalidUsernameOrEmailMessage=.نام کاربری یا ایمیل نامعتبر است
invalidPasswordMessage=رمز عبور نامعتبر
invalidEmailMessage=.آدرس ایمیل نامعتبر است
accountDisabledMessage=.حساب غیرفعال است، با سرپرست خود تماس بگیرید
accountTemporarilyDisabledMessage=.نام کاربری یا رمز عبور نامعتبر است
accountPermanentlyDisabledMessage=.نام کاربری یا رمز عبور نامعتبر است
accountTemporarilyDisabledMessageTotp=.کد احراز هویت نامعتبر است
accountPermanentlyDisabledMessageTotp=.کد احراز هویت نامعتبر است
expiredCodeMessage=.اتمام مدت ورود. لطفاً دوباره وارد شوید
expiredActionMessage=.اقدام منقضی شده است. لطفا همین الان با ورود به سیستم ادامه دهید
expiredActionTokenNoSessionMessage=اقدام منقضی شده است.
expiredActionTokenSessionExistsMessage=.اقدام منقضی شده است. لطفا دوباره شروع کنید
sessionLimitExceeded=تعداد جلسات بسیار زیاد است

missingFirstNameMessage=لطفا نام را مشخص کنید
missingLastNameMessage=لطفا نام خانوادگی را مشخص کنید
missingEmailMessage=لطفا ایمیل را مشخص کنید
missingUsernameMessage=لطفا نام کاربری را مشخص کنید
missingPasswordMessage=لطفا رمز عبور را مشخص کنید
missingTotpMessage=.لطفا کد احراز هویت را مشخص کنید
missingTotpDeviceNameMessage=.لطفا نام دستگاه را مشخص کنید
notMatchPasswordMessage=گذرواژه ها مطابقت ندارند

error-invalid-value=.مقدار نامعتبر است
error-invalid-blank=لطفا مقدار را مشخص کنید
error-empty=لطفا مقدار را مشخص کنید
error-invalid-length=.طول باید بین {1} و {2} باشد
error-invalid-length-too-short=.حداقل طول {1} است
error-invalid-length-too-long=.حداکثر طول {2} است
error-invalid-email=.آدرس ایمیل نامعتبر است
error-invalid-number=.عدد نامعتبر
error-number-out-of-range=.شماره باید بین {1} و {2} باشد
error-number-out-of-range-too-small=.عدد باید حداقل مقدار {1} را داشته باشد
error-number-out-of-range-too-big=.عدد باید حداکثر مقدار {2} را داشته باشد
error-pattern-no-match=.مقدار نامعتبر است
error-invalid-uri=URL نامعتبر است.
error-invalid-uri-scheme=طرح URL نامعتبر است.
error-invalid-uri-fragment=قطعه URL نامعتبر است.
error-user-attribute-required=.لطفا این فیلد را مشخص کنید
error-invalid-date=.تاریخ نامعتبر است
error-user-attribute-read-only=.این فیلد فقط خواندنی است
error-username-invalid-character=.مقدار حاوی کاراکتر نامعتبر است
error-person-name-invalid-character=.مقدار حاوی نویسه نامعتبر است

invalidPasswordExistingMessage=.رمز عبور موجود نامعتبر است
invalidPasswordBlacklistedMessage=.رمز عبور نامعتبر: رمز عبور در لیست سیاه قرار گرفته است
invalidPasswordConfirmMessage=.تأیید رمز عبور مطابقت ندارد
invalidTotpMessage=.کد احراز هویت نامعتبر است

usernameExistsMessage=.نام کاربری از قبل وجود دارد
emailExistsMessage=.ایمیل از قبل وجود دارد

federatedIdentityExistsMessage=.کاربر با {0} {1} از قبل وجود دارد. لطفا برای پیوند دادن حساب به مدیریت حساب وارد شوید
federatedIdentityUnavailableMessage=.کاربر {0} احراز هویت شده با ارائه دهنده هویت {1} وجود ندارد. لطفا با ادمین خود تماس بگیرید

confirmLinkIdpTitle=حساب از قبل وجود دارد
federatedIdentityConfirmLinkMessage=کاربر با {0} {1} از قبل وجود دارد. چگونه می خواهید ادامه دهید؟
federatedIdentityConfirmReauthenticateMessage=برای پیوند دادن حساب خود با {0} احراز هویت
nestedFirstBrokerFlowMessage=.کاربر {0} {1} به هیچ کاربر شناخته شده ای پیوند داده نشده است
confirmLinkIdpReviewProfile=بررسی نمایه
confirmLinkIdpContinue=اضافه‌‌کردن به حساب موحود

configureTotpMessage=.برای فعال کردن حساب خود باید اعتبارسنجی از طریق موبایل را راه اندازی کنید
configureBackupCodesMessage=.برای فعال کردن حساب خود باید کدهای پشتیبان را تنظیم کنید
updateProfileMessage=.برای فعال کردن حساب کاربری خود باید پروفایل کاربری خود را به روز کنید
updatePasswordMessage=.برای فعال کردن حساب خود باید رمز عبور خود را تغییر دهید
updateEmailMessage=.برای فعال کردن حساب خود باید آدرس ایمیل خود را به روز کنید
resetPasswordMessage=.شما باید رمز عبور خود را تغییر دهید
verifyEmailMessage=.برای فعال کردن حساب خود باید آدرس ایمیل خود را تأیید کنید
linkIdpMessage=.برای پیوند دادن حساب خود با {0}، باید آدرس ایمیل خود را تأیید کنید

emailSentMessage=.شما باید به زودی یک ایمیل با دستورالعمل های بیشتر دریافت کنید
emailSendErrorMessage=.ایمیل ارسال نشد، لطفاً بعداً دوباره امتحان کنید

accountUpdatedMessage=.حساب شما به روز شده است
accountPasswordUpdatedMessage=.رمز عبور شما به روز شده است

delegationCompleteHeader=ورود با موفقیت
delegationCompleteMessage=.می توانید این پنجره مرورگر را ببندید و به برنامه کنسول خود بازگردید
delegationFailedHeader=ورود ناموفق بود
delegationFailedMessage=.می توانید این پنجره مرورگر را ببندید و به برنامه کنسول خود برگردید و دوباره سعی کنید وارد شوید

noAccessMessage=بذون دسترسی

invalidPasswordMinLengthMessage=.رمز عبور نامعتبر: حداقل طول {0}
invalidPasswordMaxLengthMessage=.رمز عبور نامعتبر: حداکثر طول {0}
invalidPasswordMinDigitsMessage=.رمز عبور نامعتبر: باید حداقل دارای {0} رقم عددی باشد
invalidPasswordMinLowerCaseCharsMessage=.رمز عبور نامعتبر: باید حداقل دارای {0} نویسه کوچک باشد
invalidPasswordMinUpperCaseCharsMessage=.رمز عبور نامعتبر: باید حداقل دارای {0} نویسه بزرگ باشد
invalidPasswordMinSpecialCharsMessage=.رمز عبور نامعتبر: باید حداقل دارای {0} کاراکتر خاص باشد
invalidPasswordNotUsernameMessage=.رمز عبور نامعتبر: نباید برابر با نام کاربری باشد
invalidPasswordNotEmailMessage=.رمز عبور نامعتبر: نباید برابر با ایمیل باشد
invalidPasswordRegexPatternMessage=رمز عبور نامعتبر: با الگو(های) regex مطابقت ندارد.
invalidPasswordHistoryMessage=.گذرواژه نامعتبر: نباید با هیچ یک از رمزهای عبور اخیر {0} برابر باشد
invalidPasswordGenericMessage=.رمز عبور نامعتبر: رمز عبور جدید با خط مشی های رمز عبور مطابقت ندارد

failedToProcessResponseMessage=پاسخ پردازش نشد
httpsRequiredMessage=HTTPS مورد نیاز است
realmNotEnabledMessage=قلمرو فعال نیست
invalidRequestMessage=درخواست نامعتبر
successLogout=شما از سیستم خارج شده اید
failedLogout=خروج ناموفق بود
unknownLoginRequesterMessage=درخواست کننده ورود نامشخص
loginRequesterNotEnabledMessage=درخواست کننده ورود فعال نیست
bearerOnlyMessage=برنامه های فقط حامل مجاز به شروع ورود به مرورگر نیستند
standardFlowDisabledMessage=مشتری مجاز به شروع ورود به مرورگر با response_type نیست. جریان استاندارد برای مشتری غیرفعال است.
implicitFlowDisabledMessage=مشتری مجاز به شروع ورود به مرورگر با answer_type نیست. جریان ضمنی برای مشتری غیرفعال است.
invalidRedirectUriMessage=uri تغییر مسیر نامعتبر است
unsupportedNameIdFormatMessage=NameIDFormat پشتیبانی نشده
invalidRequesterMessage=درخواست کننده نامعتبر است
registrationNotAllowedMessage=ثبت نام مجاز نیست
resetCredentialNotAllowedMessage=بازنشانی اعتبار مجاز نیست

permissionNotApprovedMessage=مجوز تایید نشد
noRelayStateInResponseMessage=.هیچ حالت رله ای در پاسخ از ارائه دهنده هویت وجود ندارد
insufficientPermissionMessage=.مجوزهای کافی برای پیوند دادن هویت ها وجود ندارد
couldNotProceedWithAuthenticationRequestMessage=.نمی‌توان با درخواست احراز هویت به ارائه‌دهنده هویت ادامه داد
couldNotObtainTokenMessage=.رمز از ارائه دهنده هویت دریافت نشد
unexpectedErrorRetrievingTokenMessage=.خطای غیرمنتظره هنگام بازیابی رمز از ارائه دهنده هویت
unexpectedErrorHandlingResponseMessage=.خطای غیرمنتظره هنگام رسیدگی به پاسخ ارائه‌دهنده هویت
identityProviderAuthenticationFailedMessage=.احراز هویت ناموفق بود. با ارائه‌دهنده هویت احراز هویت امکان‌پذیر نیست
couldNotSendAuthenticationRequestMessage=.درخواست احراز هویت به ارائه دهنده هویت ارسال نشد
unexpectedErrorHandlingRequestMessage=.خطای غیرمنتظره هنگام رسیدگی به درخواست احراز هویت به ارائه‌دهنده هویت
invalidAccessCodeMessage=.کد دسترسی نامعتبر
sessionNotActiveMessage=نشست فعال نیست
invalidCodeMessage=.خطایی رخ داده است، لطفاً دوباره از طریق برنامه خود وارد شوید
cookieNotFoundMessage=.کوکی پیدا نشد لطفا مطمئن شوید که کوکی ها در مرورگر شما فعال هستند
insufficientLevelOfAuthentication=.سطح درخواستی احراز هویت برآورده نشده است
identityProviderUnexpectedErrorMessage=خطای غیرمنتظره هنگام احراز هویت با ارائه دهنده هویت
identityProviderMissingStateMessage=.پارامتر حالت در پاسخ از ارائه دهنده هویت وجود ندارد
identityProviderInvalidResponseMessage=.پاسخ نامعتبر از ارائه دهنده هویت
identityProviderInvalidSignatureMessage=.امضای نامعتبر در پاسخ از ارائه دهنده هویت
identityProviderNotFoundMessage=.ارائه دهنده هویت با شناسه یافت نشد
identityProviderLinkSuccess=.شما با موفقیت ایمیل خود را تأیید کردید. لطفاً به مرورگر اصلی خود برگردید و در آنجا با ورود به سیستم ادامه دهید
staleCodeMessage=این صفحه دیگر معتبر نیست، لطفاً به برنامه خود برگردید و دوباره وارد شوید
realmSupportsNoCredentialsMessage=.قلمرو از هیچ نوع اعتبارنامه ای پشتیبانی نمی کند
credentialSetupRequired=.نمی توان به سیستم وارد شد، راه اندازی اعتبارنامه لازم است
identityProviderNotUniqueMessage=.قلمرو از چندین ارائه دهنده هویت پشتیبانی می کند. نمی توان تعیین کرد که با کدام ارائه دهنده هویت باید برای احراز هویت استفاده شود
emailVerifiedMessage=.آدرس ایمیل شما تایید شده است
staleEmailVerificationLink=.پیوندی که روی آن کلیک کردید یک پیوند قدیمی است و دیگر معتبر نیست. شاید قبلا ایمیل خود را تایید کرده باشید
identityProviderAlreadyLinkedMessage=.هویت فدرال بازگردانده شده توسط {0} قبلاً به کاربر دیگری پیوند داده شده است
confirmAccountLinking=.پیوند حساب {0} ارائه دهنده هویت {1} را با حساب خود تأیید کنید
confirmEmailAddressVerification=.اعتبار آدرس ایمیل {0} را تأیید کنید
confirmExecutionOfActions=عمل(های) زیر را انجام دهید

backToApplication=&laquo; بازگشت به برنامه
missingParameterMessage=پارامترهای از دست رفته\: {0}
clientNotFoundMessage=مشتری یافت نشد
clientDisabledMessage=.مشتری غیرفعال است
invalidParameterMessage=پارامتر نامعتبر\: {0}
alreadyLoggedIn=.شما پیشتر وارد شدهاید - لاگین کرده اید
differentUserAuthenticated=.شما قبلاً به عنوان کاربر دیگری ''{0}'' در این جلسه احراز هویت شده اید. لطفا ابتدا از سیستم خارج شوید
brokerLinkingSessionExpired=.درخواست پیوند حساب کارگزاری داده است، اما جلسه فعلی دیگر معتبر نیست
proceedWithAction=&raquo; برای ادامه اینجا را کلیک کنید
acrNotFulfilled=الزامات احراز هویت برآورده نشده است

requiredAction.CONFIGURE_TOTP=OTP را تنظیم کنید
requiredAction.TERMS_AND_CONDITIONS=شرایط و ضوابط
requiredAction.UPDATE_PASSWORD=رمز عبور را به روز کنید
requiredAction.UPDATE_PROFILE=بروزرسانی پروفایل
requiredAction.VERIFY_EMAIL=تأیید ایمیل
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=کدهای بازیابی را ایجاد کنید
requiredAction.webauthn-register-passwordless=Webauthn ثبت نام بدون رمز عبور

invalidTokenRequiredActions=اقدامات لازم موجود در پیوند معتبر نیستند

doX509Login=شما به عنوان\: وارد سیستم خواهید شد:
clientCertificate=گواهی مشتری X509\:
noCertificate=[بدون گواهی]


pageNotFound=صفحه یافت نشد
internalServerError=یک خطای سرور داخلی رخ داده است

console-username=:نام کاربری
console-password=:رمز عبور
console-otp=:رمز عبور یکبار مصرف
console-new-password=:رمز عبور جدید
console-confirm-password=:تایید رمز عبور
console-update-password=.به روز رسانی رمز عبور شما الزامی است
console-verify-email=.شما باید آدرس ایمیل خود را تأیید کنید. ما یک ایمیل به {0} ارسال کردیم که حاوی کد تأیید است. لطفا این کد را در ورودی زیر وارد کنید
console-email-code=:کد ایمیل
console-accept-terms=شرایط را می‌پذیرید؟ [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=اطلاعات کاربر
openshift.scope.user_check-access=اطلاعات دسترسی کاربر
openshift.scope.user_full=دسترسی کامل
openshift.scope.list-projects=لیست پروژه ها

# SAML authentication
saml.post-form.title=تغییر مسیر احراز هویت
saml.post-form.message=.در حال تغییر مسیر، لطفا صبر کنید
saml.post-form.js-disabled=.جاوا اسکریپت غیرفعال است. ما قویاً توصیه می کنیم آن را فعال کنید. برای ادامه روی دکمه زیر کلیک کنید
saml.artifactResolutionServiceInvalidResponse=.قادر به حل مصنوع نیست

#authenticators
otp-display-name=برنامه Authenticator
otp-help-text=.یک کد تأیید را از برنامه احراز هویت وارد کنید
password-display-name=رمز عبور
password-help-text=.با وارد کردن رمز عبور خود وارد شوید
auth-username-form-display-name=نام کاربری
auth-username-form-help-text=با وارد کردن نام کاربری خود وارد سیستم شوید
auth-username-password-form-display-name=نام کاربری و رمز عبور
auth-username-password-form-help-text=.با وارد کردن نام کاربری و رمز عبور خود وارد شوید

# Recovery Codes
auth-recovery-authn-code-form-display-name=کد احراز هویت بازیابی
auth-recovery-authn-code-form-help-text=.یک کد احراز هویت بازیابی را از لیستی که قبلا ایجاد شده است وارد کنید
auth-recovery-code-info-message=.کد بازیابی مشخص شده را وارد کنید
auth-recovery-code-prompt=کد بازیابی #{0}
auth-recovery-code-header=با یک کد احراز هویت بازیابی وارد شوید
recovery-codes-error-invalid=کد احراز هویت بازیابی نامعتبر است
recovery-code-config-header=کدهای احراز هویت بازیابی
recovery-code-config-warning-title=این کدهای بازیابی پس از خروج از این صفحه دیگر ظاهر نمی شوند
recovery-code-config-warning-message=.مطمئن شوید که آنها را چاپ، دانلود یا در یک مدیر رمز عبور کپی کنید و ذخیره کنید. لغو این تنظیم، این کدهای بازیابی را از حساب شما حذف می کند
recovery-codes-print=چاپ
recovery-codes-download=دانلود
recovery-codes-copy=کپی
recovery-codes-copied=کپی شد
recovery-codes-confirmation-message=من این کدها را در جایی امن ذخیره کرده ام
recovery-codes-action-complete=راه اندازی کامل
recovery-codes-action-cancel=لغو راه اندازی
recovery-codes-download-file-header=.این کدهای بازیابی را در جایی امن نگه دارید
recovery-codes-download-file-description=.کدهای بازیابی گذرواژه های یکبار مصرف هستند که به شما امکان می دهند در صورت عدم دسترسی به احراز هویت خود وارد حساب کاربری خود شوید
recovery-codes-download-file-date= این کدها در تاریخ تولید شدند
recovery-codes-label-default=کدهای بازیابی

# WebAuthn
webauthn-display-name=کلید امنیتی
webauthn-help-text=.برای ورود به سیستم از کلید امنیتی خود استفاده کنید
webauthn-passwordless-display-name=کلید امنیتی
webauthn-passwordless-help-text=.از کلید امنیتی خود برای ورود بدون رمز عبور استفاده کنید
webauthn-login-title=ورود به سیستم کلید امنیتی
webauthn-registration-title=ثبت کلید امنیتی
webauthn-available-authenticators=کلیدهای امنیتی موجود
webauthn-unsupported-browser-text=WebAuthn توسط این مرورگر پشتیبانی نمی شود. یکی دیگر را امتحان کنید یا با سرپرست خود تماس بگیرید.
webauthn-doAuthenticate=با کلید امنیتی وارد شوید
webauthn-createdAt-label=ایجاد شده در

# WebAuthn Error
webauthn-error-title=خطای کلید امنیتی
webauthn-error-registration=کلید امنیتی شما ثبت نشد.<br/> {0}
webauthn-error-api-get=با کلید امنیتی احراز هویت انجام نشد.<br/> {0}
webauthn-error-different-user=.اولین کاربر احراز هویت شده کسی نیست که توسط کلید امنیتی احراز هویت شده است
webauthn-error-auth-verification=نتیجه احراز هویت کلید امنیتی نامعتبر است.<br/> {0}
webauthn-error-register-verification=نتیجه ثبت کلید امنیتی نامعتبر است.<br/> {0}
webauthn-error-user-not-found=.کاربر ناشناس با کلید امنیتی احراز هویت شده است

# Identity provider
identity-provider-redirector=با یک ارائه‌دهنده هویت دیگر ارتباط برقرار کنید
identity-provider-login-label=یا ورود با
idp-email-verification-display-name=تایید ایمیل
idp-email-verification-help-text=.حساب خود را با اعتبارسنجی ایمیل خود پیوند دهید
idp-username-password-form-display-name=نام کاربری و رمز عبور
idp-username-password-form-help-text=.با ورود حساب کاربری خود را پیوند دهید

finalDeletionConfirmation=.اگر حساب خود را حذف کنید، قابل بازیابی نیست. برای حفظ حساب خود، روی لغو کلیک کنید
irreversibleAction=این عمل برگشت ناپذیر است
deleteAccountConfirm=تایید حذف حساب

deletingImplies=:حذف حساب به این معنی است
errasingData=پاک کردن تمام داده های شما
loggingOutImmediately=خروج بلافاصله شما
accountUnusable=هر گونه استفاده بعدی از برنامه با این حساب امکان پذیر نخواهد بود
userDeletedSuccessfully=کاربر با موفقیت حذف شد

access-denied=دسترسی رد شد
access-denied-when-idp-auth=هنگام احراز هویت با {0}، دسترسی ممنوع شد

frontchannel-logout.title=خروج از سیستم
frontchannel-logout.message=شما در حال خروج از برنامه های زیر هستید
logoutConfirmTitle=خروج
logoutConfirmHeader=آیا می خواهید از سیستم خارج شوید؟
doLogout=خروج

readOnlyUsernameMessage=.شما نمی توانید نام کاربری خود را به روز کنید زیرا فقط خواندنی است
