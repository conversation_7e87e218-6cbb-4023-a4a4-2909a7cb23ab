invalidPasswordMinLengthMessage=無效的密碼：最短長度為 {0}。
invalidPasswordMaxLengthMessage=無效的密碼：最長長度為 {0}。
invalidPasswordMinDigitsMessage=無效的密碼：至少需要 {0} 個數字。
invalidPasswordMinLowerCaseCharsMessage=無效的密碼：至少需要 {0} 個小寫字母。
invalidPasswordMinUpperCaseCharsMessage=無效的密碼：至少需要 {0} 個大寫字母。
invalidPasswordMinSpecialCharsMessage=無效的密碼：至少需要 {0} 個特殊字元。
invalidPasswordNotUsernameMessage=無效的密碼：不可與使用者名稱相同。
invalidPasswordNotContainsUsernameMessage=無效的密碼：不可包含使用者名稱。
invalidPasswordNotEmailMessage=無效的密碼：不可與電子信箱相同。
invalidPasswordRegexPatternMessage=無效的密碼：不符合密碼規則。
invalidPasswordHistoryMessage=無效的密碼：不可與前 {0} 個密碼相同。
invalidPasswordBlacklistedMessage=無效的密碼：該密碼已在黑名單中。
invalidPasswordGenericMessage=無效的密碼：密碼不符合規則。

ldapErrorEditModeMandatory=編輯模式是必要的
ldapErrorInvalidCustomFilter=自訂配置的 LDAP 過濾器不以 “(” 開頭或不以 “)” 結尾。
ldapErrorConnectionTimeoutNotNumber=連線逾時必須為數字
ldapErrorReadTimeoutNotNumber=讀取逾時必須為數字
ldapErrorMissingClientId=當不使用領域角色對應時，需要在設定中提供客戶端 ID。
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=無法同時保留群組繼承和使用 UID 成員身分類型。
ldapErrorCantWriteOnlyForReadOnlyLdap=當 LDAP 提供者不是可寫入狀態時，無法設定為僅寫入。
ldapErrorCantWriteOnlyAndReadOnly=無法同時設定只讀跟只寫。
ldapErrorCantEnableStartTlsAndConnectionPooling=無法同時啟用 StartTLS 和連線池設定。
ldapErrorCantEnableUnsyncedAndImportOff=無法在 LDAP 提供者模式為 UNSYNCED 時停用使用者匯入。
ldapErrorMissingGroupsPathGroup=群組路徑群組不存在 - 請先在指定路徑上建立群組
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=驗證密碼策略僅適用於可寫入的編輯模式

clientRedirectURIsFragmentError=重導向 URI 不得包含 URI 片段
clientRootURLFragmentError=Root URL 不得包含 URL 片段
clientRootURLIllegalSchemeError=Root URL 使用不合法的協定
clientBaseURLIllegalSchemeError=Base URL 使用不合法的協定
backchannelLogoutUrlIllegalSchemeError=Backchannel logout URL 使用不合法的協定
clientRedirectURIsIllegalSchemeError=重導向 URI 使用不合法的協定
clientBaseURLInvalid=Base URL 不是有效的 URL
clientRootURLInvalid=Root URL 不是有效的 URL
clientRedirectURIsInvalid=重導向 URI 不是有效的 URI
backchannelLogoutUrlIsInvalid=Backchannel logout URL 不是有效的 URL


pairwiseMalformedClientRedirectURI=客戶端包含了一個無效的重導向 URI。
pairwiseClientRedirectURIsMissingHost=客戶端重導向 URI 必須包含有效的主機元件。
pairwiseClientRedirectURIsMultipleHosts=沒有設定 Sector Identifier URI，客戶端重導向 URI 不得包含多個主機元件。
pairwiseMalformedSectorIdentifierURI=錯誤的 Sector Identifier URI。
pairwiseFailedToGetRedirectURIs=無法從 Sector Identifier URI 取得重導向 URI。
pairwiseRedirectURIsMismatch=客戶端重導向 URI 與從 Sector Identifier URI 取得的重導向 URI 不符。

duplicatedJwksSettings=無法同時啟用「使用 JWKS」開關和「使用 JWKS URL」開關。

error-invalid-value=無效的數值。
error-invalid-blank=請指定數值。
error-empty=請指定數值。
error-invalid-length=屬性 {0} 的長度必須介於 {1} 和 {2} 之間。
error-invalid-length-too-short=屬性 {0} 的長度不能少於 {1}。
error-invalid-length-too-long=屬性 {0} 的長度不能超過 {2}。
error-invalid-email=無效的電子信箱。
error-invalid-number=無效的號碼。
error-number-out-of-range=屬性 {0} 必須介於 {1} 和 {2} 之間。
error-number-out-of-range-too-small=屬性 {0} 最小值為 {1}。
error-number-out-of-range-too-big=屬性 {0} 最大值為 {2}。
error-pattern-no-match=無效的數值。
error-invalid-uri=無效的 URL。
error-invalid-uri-scheme=無效的 URL 協定。
error-invalid-uri-fragment=無效的 URL 片段。
error-user-attribute-required=請提供 {0} 欄位。
error-invalid-date={0} 是無效的日期。
error-user-attribute-read-only={0} 欄位為唯讀。
error-username-invalid-character={0} 含有無效字元。
error-person-name-invalid-character={0} 含有無效字元。
error-invalid-multivalued-size=屬性 {0} 必須包含至少 {1} ，最多 {2} 個值。