package otbs.ms_astreint.client.reservation.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarteCarburantDto {
    private Long idCarte;
    private String numeroCarte;
    private boolean horsParc;
    private Double soldeActuel;
    private Date dateExpiration;
    private boolean disponibilite;
    private List<Long> consommationCarburantIds;
}
