package otbs.ms_mission.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration pour la publication d'evenements RabbitMQ depuis le microservice mission.
 */
@Configuration
public class RabbitMQPublisherConfig {

    // Constantes pour les noms des exchanges et routing keys
    public static final String NOTIFICATIONS_EXCHANGE = "notifications.exchange";
    
    // Routing keys pour les evenements de mission
    public static final String MISSION_CREATED_ROUTING_KEY = "mission.created";
    public static final String MISSION_UPDATED_ROUTING_KEY = "mission.updated";
    public static final String MISSION_ASSIGNED_ROUTING_KEY = "mission.assigned";
    public static final String MISSION_COMPLETED_ROUTING_KEY = "mission.completed";
    public static final String ACCOMPANIST_ASSIGNED_ROUTING_KEY = "mission.accompanist.assigned";
    
    // Nom de la queue pour les notifications de mission
    public static final String MISSION_NOTIFICATIONS_QUEUE = "mission.notifications";

    /**
     * Cree l'exchange pour les notifications
     */
    @Bean
    public TopicExchange notificationsExchange() {
        return new TopicExchange(NOTIFICATIONS_EXCHANGE, true, false);
    }
    
    /**
     * Crée la queue pour les notifications de mission
     */
    @Bean
    public Queue missionNotificationsQueue() {
        return new Queue(MISSION_NOTIFICATIONS_QUEUE, true);
    }
    
    /**
     * Binding pour la queue mission.notifications avec le routing key mission.created
     */
    @Bean
    public Binding missionCreatedBinding(Queue missionNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(missionNotificationsQueue)
                .to(notificationsExchange)
                .with(MISSION_CREATED_ROUTING_KEY);
    }
    
    /**
     * Binding pour la queue mission.notifications avec le routing key mission.updated
     */
    @Bean
    public Binding missionUpdatedBinding(Queue missionNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(missionNotificationsQueue)
                .to(notificationsExchange)
                .with(MISSION_UPDATED_ROUTING_KEY);
    }
    
    /**
     * Binding pour la queue mission.notifications avec le routing key mission.assigned
     */
    @Bean
    public Binding missionAssignedBinding(Queue missionNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(missionNotificationsQueue)
                .to(notificationsExchange)
                .with(MISSION_ASSIGNED_ROUTING_KEY);
    }
    
    /**
     * Binding pour la queue mission.notifications avec le routing key mission.completed
     */
    @Bean
    public Binding missionCompletedBinding(Queue missionNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(missionNotificationsQueue)
                .to(notificationsExchange)
                .with(MISSION_COMPLETED_ROUTING_KEY);
    }
    
    /**
     * Binding pour la queue mission.notifications avec le routing key mission.accompanist.assigned
     */
    @Bean
    public Binding accompanistAssignedBinding(Queue missionNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(missionNotificationsQueue)
                .to(notificationsExchange)
                .with(ACCOMPANIST_ASSIGNED_ROUTING_KEY);
    }

    /**
     * Configure le convertisseur de messages pour serialiser les objets en JSON
     */
    @Bean
    public MessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * Configure le RabbitTemplate pour utiliser le convertisseur JSON
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, MessageConverter jsonMessageConverter) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(jsonMessageConverter);
        
        // Configuration pour la confirmation de publication
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                // Log en cas d'echec de publication
                System.err.println("Failed to publish message: " + cause);
            }
        });
        
        // Configuration pour la gestion des retours
        rabbitTemplate.setReturnsCallback(returned -> {
            System.err.println("Message returned: " + 
                    returned.getMessage() + 
                    " with reply code: " + 
                    returned.getReplyCode());
        });
        
        return rabbitTemplate;
    }
} 