parent=base
import=common/keycloak
styles=css/styles.css

# Personnalisation des classes
kcBodyClass=modern-theme
kcLoginClass=login-container
kcFormCardClass=card-pf
kcButtonClass=btn
kcButtonPrimaryClass=btn-primary
kcInputClass=form-control
kcAlertClass=alert
kcAlertErrorClass=alert-error
kcAlertSuccessClass=alert-success
kcHeaderClass=modern-header
kcHeaderWrapperClass=header-content
kcFormGroupClass=form-group
kcLabelClass=form-label
kcInputWrapperClass=input-wrapper
kcFormOptionsClass=form-options
kcFormButtonsClass=form-buttons
kcFormSocialAccountClass=social-providers
kcFormSocialAccountListClass=social-list
kcFormSocialAccountListButtonClass=social-button
kcResetFlowIcon=icon icon-reset
kcFeedbackSuccessIcon=icon icon-success
kcFeedbackWarningIcon=icon icon-warning
kcFeedbackErrorIcon=icon icon-error
kcFeedbackInfoIcon=icon icon-info