package otbs.ms_mission.dto.notification;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Événement spécifique aux missions.
 * Hérite de NotificationEvent et ajoute des informations spécifiques aux missions.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MissionEvent extends NotificationEvent implements Serializable {
    
    /**
     * Identifiant de la mission concernée
     */
    private Long missionId;
    
    /**
     * Type de mission (CLIENT, PROJET)
     */
    private String typeMission;

    /**
     * Constructeur pour les cas d'usage courants avec liste de destinataires
     */
    public MissionEvent(String eventType, Long missionId, List<String> destinataires, String titre, String message) {
        super(eventType, "ms-mission", destinataires, titre, message);
        this.missionId = missionId;
        this.setEntiteLieeId(missionId);
        this.setEntiteLieeType("MISSION");
        this.setUrlAction("/missions/" + missionId);
    }

    /**
     * Constructeur pour les cas d'usage avec un seul destinataire
     */
    public MissionEvent(String eventType, Long missionId, String destinataire, String titre, String message) {
        super(eventType, "ms-mission", destinataire, titre, message);
        this.missionId = missionId;
        this.setEntiteLieeId(missionId);
        this.setEntiteLieeType("MISSION");
        this.setUrlAction("/missions/" + missionId);
    }
} 