package otbs.vehicule.controller;


import otbs.vehicule.dto.VehiculeDto;
import otbs.vehicule.model.Vehicule;
import otbs.vehicule.model.enums.Categorie;
import otbs.vehicule.model.enums.Etat;
import otbs.vehicule.model.enums.TypeCarburant;
import otbs.vehicule.service.VehiculeService;


import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.util.List;

@RestController
@RequestMapping("/api/v1/vehicule")
public class VehiculeController {

    private final VehiculeService vehiculeService;

    public VehiculeController(VehiculeService vehiculeService) {
        this.vehiculeService = vehiculeService;
    }

    @Operation(summary = "Add a new car", description = "Creates a new car entry in the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "car created successfully",
                    content = @Content(schema = @Schema(implementation = VehiculeDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request")
    })
    @PostMapping("/ajouterVehicule")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICE_GENERAUX')")
    public ResponseEntity<Vehicule> addVehicule(@RequestBody VehiculeDto vehiculeDTO) {
        Vehicule savedVehicule = vehiculeService.addVehicule(vehiculeDTO);
        return new ResponseEntity<>(savedVehicule, HttpStatus.CREATED);
    }
        @Operation(summary = "Count vehicules by fuel type", description = "Fetches the count of vehicles filtered by their fuel type")
        @GetMapping("/stats/byTypeCarburant/{typeCarburant}")
        public long getVehiculesByTypeCarburant(@PathVariable TypeCarburant typeCarburant) {
        return vehiculeService.countVehiculesByTypeCarburant(typeCarburant);
        }

    @Operation(summary = "Get car by ID", description = "Fetches the details of a specific car by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "car found",
                    content = @Content(schema = @Schema(implementation = VehiculeDto.class))),
            @ApiResponse(responseCode = "404", description = "car not found")
    })
    @GetMapping("/{idVehicule}")
    @PreAuthorize("hasAnyRole('RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_TECHNIQUE')")
    public ResponseEntity<VehiculeDto> getVehicule(@PathVariable Long idVehicule) {
        VehiculeDto vehiculeDTO = vehiculeService.getVehicule(idVehicule);

        return new ResponseEntity<>(vehiculeDTO, HttpStatus.OK);
    }

    @Operation(summary = "Update car ", description = "Updates the car details based on its ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "vehicule updated successfully",
                    content = @Content(schema = @Schema(implementation = VehiculeDto.class))),
            @ApiResponse(responseCode = "404", description = "vehicule not found")
    })
    @PutMapping("/update/{idVehicule}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICE_GENERAUX')")
    public ResponseEntity<VehiculeDto> updateVehicule(@PathVariable Long idVehicule, @RequestBody VehiculeDto vehiculeDTO) {
        VehiculeDto updatedVehiculeDTO = vehiculeService.updateVehicule(idVehicule, vehiculeDTO);
        return updatedVehiculeDTO != null ? new ResponseEntity<>(updatedVehiculeDTO, HttpStatus.OK) 
                                          : new ResponseEntity<>(HttpStatus.NOT_FOUND);

    }

    @Operation(summary = "Remove car", description = "Deletes the car entry from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "car removed successfully"),
            @ApiResponse(responseCode = "404", description = "car not found")
    })
    @DeleteMapping("/remove/{idVehicule}")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICE_GENERAUX')")
    public ResponseEntity<Void> removeVehicule(@PathVariable Long idVehicule) {
        vehiculeService.removeVehicule(idVehicule);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Operation(summary = "Get all vehicules", description = "Fetches a list of all car in the system")
    @ApiResponse(responseCode = "200", description = "List of car fetched successfully")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICE_GENERAUX')")
    @GetMapping("/allVehicule")
    public List<VehiculeDto> getAllVehicules() {
        return vehiculeService.findAll();
    }

    @Operation(summary = "Get vehicule by Immatriculation", description = "Fetches the details of a car by its immatriculation number")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "vehicule found",
                    content = @Content(schema = @Schema(implementation = VehiculeDto.class))),
            @ApiResponse(responseCode = "404", description = "Vehicle not found")
    })
    @GetMapping("/findByImmatriculation/{immatriculation}")
    @PreAuthorize("hasAnyRole('RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_TECHNIQUE')")
    public VehiculeDto getVehiculeByImmat(@PathVariable String immatriculation) {

        return vehiculeService.findByImmatriculation(immatriculation);
    }

    @Operation(summary = "Get car by Brand", description = "Fetches a list of car filtered by their brand")
    @ApiResponse(responseCode = "200", description = "List of vehicule fetched successfully")
    @PreAuthorize("hasAnyRole('RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_TECHNIQUE')")
    @GetMapping("/findByMarque/{marque}")
    public List<VehiculeDto> getVehiculeByMarque(@PathVariable String marque) {
        return vehiculeService.findByMarque(marque);
    }

     @Operation(summary = "Get vehicules by Etat", description = "Fetches a list of vehicules filtered by their state")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Vehicules found",
                    content = @Content(schema = @Schema(implementation = VehiculeDto.class))),
            @ApiResponse(responseCode = "404", description = "Vehicules not found")
    })
    @GetMapping("/findByEtat/{etat}")
    @PreAuthorize("hasAnyRole('RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_TECHNIQUE')")
    public List<VehiculeDto> getVehiculesByEtat(@PathVariable Etat etat) {
        return vehiculeService.findByEtat(etat);
    }

    @Operation(summary = "Get vehicules by Categorie", description = "Fetches a list of vehicules filtered by their category")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Vehicules found",
                    content = @Content(schema = @Schema(implementation = VehiculeDto.class))),
            @ApiResponse(responseCode = "404", description = "Vehicules not found")
    })
    @GetMapping("/findByCategorie/{categorie}")
    @PreAuthorize("hasAnyRole('RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_TECHNIQUE')")
    public List<VehiculeDto> getVehiculesByCategorie(@PathVariable Categorie categorie) {
        return vehiculeService.findByCategorie(categorie);
    }

    @Operation(summary = "Count total vehicules")
        @GetMapping("/stats/total")
        public long getTotalVehicules() {
        return vehiculeService.countTotalVehicules();
        }

        @Operation(summary = "Count vehicules en service")
        @GetMapping("/stats/enService")
        public long getVehiculesEnService() {
        return vehiculeService.countVehiculesEnService();
        }
        @Operation(summary = "Count vehicules en route")
        @GetMapping("/stats/enRoute")
        public long getVehiculeEnRoute() {
        return vehiculeService.countVehiculesEnRoute();
        }

        @Operation(summary = "Count vehicules en maintenance")
        @GetMapping("/stats/enMaintenance")
        public long getVehiculesEnMaintenance() {
        return vehiculeService.countVehiculesEnMaintenance();
        }

        @Operation(summary = "Count vehicules hors service")
        @GetMapping("/stats/horsService")
        public long getVehiculesHorsService() {
        return vehiculeService.countVehiculesHorsService();
        }


        @Operation(summary = "Pourcentage des véhicules en service")
        @GetMapping("/stats/pourcentageEnService")
        public double getPourcentageVehiculesEnService() {
        return vehiculeService.percentageVehiculesEnService();
        }

      
        @Operation(summary = "Pourcentage des véhicules en maintenance")
        @GetMapping("/stats/pourcentageEnMaintenance")
        public double getPourcentageVehiculesEnMaintenance() {
        return vehiculeService.percentageVehiculesEnMaintenance();
        }

        @Operation(summary = "Kilométrage moyen des véhicules")
        @GetMapping("/stats/kilometrageMoyen")
        public double getKilometrageMoyen() {
        return vehiculeService.calculateAverageKilometrage();
        }

        @Operation(summary = "Count vehicules par catégorie")
        @GetMapping("/stats/parCategorie/{categorie}")
        public long getCountVehiculesByCategorie(@PathVariable Categorie categorie) {
        return vehiculeService.countVehiculesByCategorie(categorie);
        }

    @GetMapping("/getUnauthorizedToCirculateCars")
    @PreAuthorize("hasRole('RESPONSABLE_SERVICES_GENERAUX')")
    public ResponseEntity<List<VehiculeDto>> getUnauthorizedToCirculateCars() {
        List<VehiculeDto> vehiculeDtoList = vehiculeService.getCarsUnauthorizedToCirculate();
        return new ResponseEntity<>(vehiculeDtoList, HttpStatus.OK);
    }
 
} 