package otbs.ms_notification.model;

/**
 * Énumération des types de notifications disponibles dans le système.
 * Chaque type correspond à un événement spécifique qui peut déclencher une notification.
 */
public enum TypeNotification {
    /**
     * Notification pour un nouvel incident créé
     */
    INCIDENT_NOUVEAU,
    
    /**
     * Notification pour un incident résolu
     */
    INCIDENT_RESOLU,
    
    /**
     * Notification pour un incident critique
     */
    INCIDENT_CRITIQUE,
    
    /**
     * Notification pour une nouvelle mission assignée
     */
    MISSION_ASSIGNEE,
    
    /**
     * Notification pour une mission terminée
     */
    MISSION_TERMINEE,
    
    /**
     * Notification pour une nouvelle mission créée
     */
    MISSION_NOUVELLE,
    
    /**
     * Notification pour une mission modifiée
     */
    MISSION_MODIFIEE,
    
    /**
     * Notification pour un accompagnateur assigné à une mission
     */
    MISSION_ACCOMPAGNATEUR,
    
    /**
     * Notification pour une nouvelle astreinte
     */
    ASTREINTE_ASSIGNEE,
    
    /**
     * Notification pour une nouvelle astreinte créée
     */
    ASTREINTE_NOUVELLE,
    
    /**
     * Notification pour une astreinte modifiée
     */
    ASTREINTE_MODIFIEE,
    
    /**
     * Notification pour une astreinte annulée/supprimée
     */
    ASTREINTE_ANNULEE,
    
    /**
     * Notification pour une maintenance programmée
     */
    MAINTENANCE_PROGRAMMEE,
    
    /**
     * Notification générale d'information
     */
    INFORMATION_GENERALE,

    DOCUMENT_VEHICULE_EXPIRED
}
