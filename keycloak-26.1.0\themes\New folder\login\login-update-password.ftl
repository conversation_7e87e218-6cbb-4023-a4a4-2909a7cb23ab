<#import "template.ftl" as layout>
<#import "password-commons.ftl" as passwordCommons>
<@layout.registrationLayout displayMessage=true; section>
    <#if section = "header">
        <--${msg("updatePasswordTitle")}-->
    <#elseif section = "form">
        <div id="kc-content">
            <div id="kc-form-wrapper">
                <div id="login-container" >
                    <div class="form-header">
                        <img src="${url.resourcesPath}/img/car.png" alt="Car Logo" class="form-logo">
                        <h1 class="form-title">${msg("updatePasswordTitle")}</h1>
                        <p class="form-subtitle">${msg("passwordNew")}</p>
                    </div>

                    <form id="kc-passwd-update-form" action="${url.loginAction}" method="post">
                        <div class="form-floating">
                            <input type="password" id="password-new" name="password-new" class="form-control"
                                   autofocus autocomplete="new-password"
                                   aria-invalid="<#if messagesPerField.existsError('password','password-confirm')>true</#if>"
                                   placeholder=" " />
                            <label for="password-new" class="form-label">${msg("passwordNew")}</label>
                            <span class="form-icon password-icon"></span>
                            <button class="password-toggle" type="button" aria-label="${msg("showPassword")}"
                                    aria-controls="password-new" data-password-toggle
                                    data-label-show="${msg('showPassword')}" data-label-hide="${msg('hidePassword')}">
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </button>

                            <#if messagesPerField.existsError('password')>
                                <span id="input-error-password" class="error-message" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('password'))?no_esc}
                                </span>
                            </#if>
                        </div>

                        <div class="form-floating">
                            <input type="password" id="password-confirm" name="password-confirm" class="form-control"
                                   autocomplete="new-password"
                                   aria-invalid="<#if messagesPerField.existsError('password-confirm')>true</#if>"
                                   placeholder=" " />
                            <label for="password-confirm" class="form-label">${msg("passwordConfirm")}</label>
                            <span class="form-icon password-icon"></span>
                            <button class="password-toggle" type="button" aria-label="${msg("showPassword")}"
                                    aria-controls="password-confirm" data-password-toggle
                                    data-label-show="${msg('showPassword')}" data-label-hide="${msg('hidePassword')}">
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </button>

                            <#if messagesPerField.existsError('password-confirm')>
                                <span id="input-error-password-confirm" class="error-message" aria-live="polite">
                                    ${kcSanitize(messagesPerField.get('password-confirm'))?no_esc}
                                </span>
                            </#if>
                        </div>

                        <div class="form-group">
                            <@passwordCommons.logoutOtherSessions/>
                        </div>

                        <div class="form-buttons">
                            <#if isAppInitiatedAction??>
                                <button class="login-button" id="saveTOTPBtn" type="submit">
                                    <span class="button-text">${msg("doSubmit")}</span>
                                    <span class="button-icon"></span>
                                </button>
                                <button class="cancel-button" id="cancelTOTPBtn" name="cancel-aia" value="true" type="submit">
                                    ${msg("doCancel")}
                                </button>
                            <#else>
                                <button class="login-button" id="saveTOTPBtn" type="submit">
                                    <span class="button-text">${msg("doSubmit")}</span>
                                    <span class="button-icon"></span>
                                </button>
                            </#if>
                        </div>
                    </form>
                </div>
            </div>
            <div class="login-illustration">
                <img src="${url.resourcesPath}/img/Login.jpg" alt="Login illustration">
            </div>
        </div>
        <script type="module" src="${url.resourcesPath}/js/passwordVisibility.js"></script>
    </#if>
</@layout.registrationLayout>
