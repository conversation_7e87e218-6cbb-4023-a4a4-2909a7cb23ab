doLogIn=Inloggen
doRegister=Registreer
doRegisterSecurityKey=Registreer
doCancel=Annuleer
doSubmit=Verzenden
doBack=Terug
doYes=Ja
doNo=Nee
doContinue=Doorgaan
doIgnore=Negeer
doAccept=Accepteren
doDecline=Afwijzen
doForgotPassword=Wachtwoord vergeten?
doClickHere=Klik hier
doImpersonate=Identiteit overnemen
doTryAgain=Probeer opnieuw
doTryAnotherWay=Probeer op een ander manier
doConfirmDelete=Bevestig verwijderen
errorDeletingAccount=Onverwachte fout bij het verwijderen van account
deletingAccountForbidden=U heeft onvoldoende rechten om dit account te verwijderen, neem contact op met beheer.
kerberosNotConfigured=Kerberos is niet geconfigureerd
kerberosNotConfiguredTitle=Kerberos is niet geconfigureerd
bypassKerberosDetail=U bent niet ingelogd via Kerberos of uw browser kan niet met Kerberos inloggen. Klik op 'doorgaan' om via een andere manier in te loggen
kerberosNotSetUp=Kerberos is onjuist geconfigureerd. U kunt niet inloggen.
registerTitle=Registreer
loginAccountTitle=Inloggen met uw account
loginTitle=Log in met {0}
loginTitleHtml={0}
impersonateTitle={0} Identiteit overnemen
impersonateTitleHtml=<strong>{0}</strong> Identiteit overnemen
realmChoice=Realm
unknownUser=Onbekende gebruiker
loginTotpTitle=Mobiele Authenticator Instellen
loginProfileTitle=Update accountinformatie
loginTimeout=U bent te lang bezig geweest met inloggen. Het inlogproces begint opnieuw.
reauthenticate=Log opnieuw in om verder te gaan
oauthGrantTitle=Verleen Toegang
oauthGrantTitleHtml={0}
errorTitle=Er is een fout opgetreden...
errorTitleHtml=Er is een fout opgetreden...
emailVerifyTitle=E-mailadres-verificatie
emailForgotTitle=Wachtwoord vergeten?
updatePasswordTitle=Wachtwoord updaten
codeSuccessTitle=Succescode
codeErrorTitle=Foutcode: {0}
displayUnsupported=Opgevraagde weergave type is niet ondersteund
browserRequired=Om in te loggen is een browser vereist
browserContinue=Om het loginproces af te ronden is een browser vereist
browserContinuePrompt=Open een browser en ga door met inloggen? [y/n]:
browserContinueAnswer=y


termsTitle=Voorwaarden
termsText=<p>Gedefinieerde voorwaarden</p>
termsPlainText=Voorwaarden
recaptchaFailed=Ongeldige Recaptcha
recaptchaNotConfigured=Recaptcha is verplicht, maar niet geconfigureerd
consentDenied=Toestemming geweigerd.
noAccount=Nieuwe gebruiker?
username=Gebruikersnaam
usernameOrEmail=Gebruikersnaam of e-mailadres
firstName=Voornaam
givenName=Voornaam
fullName=Volledige naam
lastName=Achternaam
familyName=Familienaam
email=E-mailadres
password=Wachtwoord
passwordConfirm=Bevestig wachtwoord
passwordNew=Nieuw wachtwoord
passwordNewConfirm=Bevestiging nieuwe wachtwoord
rememberMe=Ingelogd blijven
authenticatorCode=Authenticatiecode
address=Adres
street=Straat
locality=Woonplaats
region=Provincie of regio
postal_code=Postcode
country=Land
emailVerified=E-mailadres geverifieerd
gssDelegationCredential=GSS delegatie Credential
profileScopeConsentText=Gebruikersprofiel
emailScopeConsentText=E-mailadres
addressScopeConsentText=Adres
phoneScopeConsentText=Telefoonnummer
offlineAccessScopeConsentText=Offline toegang
samlRoleListScopeConsentText=Mijn rollen
rolesScopeConsentText=Gebruikersrollen
loginTotpIntro=U bent verplicht om tweefactor-authenticatie in te stellen om dit account te kunnen gebruiken
loginTotpStep1=Installeer een van de volgende applicaties op uw mobile telefoon
loginTotpStep2=Open de applicatie en scan de barcode
loginTotpStep3=Voer de eenmalige code die door de applicatie is aangeleverd in en klik op 'Verzenden' om de setup te voltooien
loginTotpManualStep2=Open de applicatie en voer de sleutel in
loginTotpManualStep3=Gebruik de volgende configuratiewaarden (als de applicatie dit ondersteund)
loginTotpUnableToScan=Lukt het scannen niet?
loginTotpScanBarcode=Scan barcode?
loginOtpOneTime=Eenmalige code
loginTotpType=Type
loginTotpAlgorithm=Algoritme
loginTotpDigits=Cijfers
loginTotpInterval=Interval
loginTotpCounter=Teller
loginTotpDeviceName=Apparaatnaam
loginTotp.totp=Time-based
loginTotp.hotp=Counter-based


oauthGrantRequest=Wilt u deze toegangsrechten verlenen?
inResource=in
emailVerifyInstruction1=Een e-mail met instructies om uw e-mailadres te verifiëren is zojuist verzonden.
emailVerifyInstruction2=Heeft u geen verificatiecode ontvangen in uw e-mail?
emailVerifyInstruction3=om opnieuw een e-mail te versturen.
emailLinkIdpTitle=Link {0}
emailLinkIdp1=Er is een e-mail met instructies verzonden om {0} account {1} te koppelen met uw {2} account.
emailLinkIdp2=Heeft u geen verificatiecode in uw e-mail ontvangen?
emailLinkIdp3=om opnieuw een e-mail te versturen.
emailLinkIdp4=Als u deze mail al geverifieerd hebt in een andere browser
emailLinkIdp5=om door te gaan.
backToLogin=&laquo; Terug naar Inloggen
emailInstruction=Voer uw gebruikersnaam of e-mailadres in en wij sturen u een e-mailbericht met instructies voor het aanmaken van een nieuw wachtwoord.
copyCodeInstruction=Kopieer deze code en plak deze in uw applicatie:
pageExpiredTitle=Sessie is verlopen
pageExpiredMsg1=Om het loginproces opnieuw te doen
pageExpiredMsg2=Om door te gaan met het loginproces
personalInfo=Persoonlijke informatie:
role_admin=Admin
role_realm-admin=Realm beheren
role_create-realm=Realm aanmaken
role_create-client=Client aanmaken
role_view-realm=Bekijk realm
role_view-users=Bekijk gebruikers
role_view-applications=Bekijk applicaties
role_view-clients=Bekijk clients
role_view-events=Bekijk gebeurtenissen
role_view-identity-providers=Bekijk identity providers
role_manage-realm=Beheer realm
role_manage-users=Gebruikers beheren
role_manage-applications=Beheer applicaties
role_manage-identity-providers=Beheer identity providers
role_manage-clients=Beheer clients
role_manage-events=Beheer gebeurtenissen
role_view-profile=Profiel bekijken
role_manage-account=Beheer account
role_manage-account-links=Beheer accountlinks
role_read-token=Token lezen
role_offline-access=Offline toegang
client_account=Account
client_security-admin-console=Security Admin Console
client_admin-cli=Admin CLI
client_realm-management=Realm-beheer
client_broker=Broker
invalidUserMessage=Ongeldige gebruikersnaam of wachtwoord.
invalidEmailMessage=Ongeldig e-mailadres.
accountDisabledMessage=Account is uitgeschakeld, neem contact op met beheer.
accountTemporarilyDisabledMessage=Ongeldige gebruikersnaam of wachtwoord.
accountPermanentlyDisabledMessage=Ongeldige gebruikersnaam of wachtwoord.
accountTemporarilyDisabledMessageTotp=Ongeldige authenticatiecode.
accountPermanentlyDisabledMessageTotp=Ongeldige authenticatiecode.
expiredCodeMessage=Logintijd verlopen. Gelieve opnieuw in te loggen.
expiredActionMessage=Actietijd verlopen. Log daarom opnieuw in.
expiredActionTokenNoSessionMessage=Actietijd verlopen.
expiredActionTokenSessionExistsMessage=Actietijd verlopen. Gelieve de actie opnieuw doen.
missingFirstNameMessage=Voer uw voornaam in.
missingLastNameMessage=Voer uw achternaam in.
missingEmailMessage=Voer uw e-mailadres in.
missingUsernameMessage=Voer uw gebruikersnaam in.
missingPasswordMessage=Voer uw wachtwoord in.
missingTotpMessage=Voer uw authenticatiecode in.
notMatchPasswordMessage=Wachtwoorden komen niet overeen.
invalidPasswordExistingMessage=Ongeldig bestaand wachtwoord.
invalidPasswordBlacklistedMessage=Ongeldig wachtwoord: wachtwoord is geblacklist.
invalidPasswordConfirmMessage=Wachtwoord komt niet overeen met wachtwoordbevestiging.
invalidTotpMessage=Ongeldige authenticatiecode.
usernameExistsMessage=Gebruikersnaam bestaat al.
emailExistsMessage=E-mailadres bestaat al.
federatedIdentityExistsMessage=Gebruiker met {0} {1} bestaat al. Log in met het beheerdersaccount om het account te koppelen.
confirmLinkIdpTitle=Account bestaat al
federatedIdentityConfirmLinkMessage=Gebruiker met {0} {1} bestaat al. Hoe wilt u doorgaan?
federatedIdentityConfirmReauthenticateMessage=Authenticeer om uw account te koppelen {0}
confirmLinkIdpReviewProfile=Nalopen profiel
confirmLinkIdpContinue=Voeg toe aan bestaande account
configureTotpMessage=U moet de Mobile Authenticator configuren om uw account te activeren.
updateProfileMessage=U moet uw gebruikersprofiel bijwerken om uw account te activeren.
updatePasswordMessage=U moet uw wachtwoord wijzigen om uw account te activeren.
resetPasswordMessage=U moet uw wachtwoord wijzigen.
verifyEmailMessage=U moet uw e-mailadres verifiëren om uw account te activeren.
linkIdpMessage=U moet uw e-mailadres verifiëren om uw account te koppelen aan {0}.
emailSentMessage=U ontvangt binnenkort een e-mail met verdere instructies.
emailSendErrorMessage=Het versturen van de e-mail is mislukt, probeer het later opnieuw.
accountUpdatedMessage=Uw account is gewijzigd.
accountPasswordUpdatedMessage=Uw wachtwoord is gewijzigd.
delegationCompleteHeader=Login gelukt
delegationCompleteMessage=U mag uw browser sluiten en terug gaan naar uw console applicatie
delegationFailedHeader=Login mislukt
delegationFailedMessage=U mag uw browser sluiten en terug gaan naar uw console applicatie om daar het loginproces nogmaalt te proberen.
noAccessMessage=Geen toegang
invalidPasswordMinLengthMessage=Ongeldig wachtwoord, de minimumlengte is {0} karakters.
invalidPasswordMinDigitsMessage=Ongeldig wachtwoord, deze moet minstens {0} cijfers bevatten.
invalidPasswordMinLowerCaseCharsMessage=Ongeldig wachtwoord, deze moet minstens {0} kleine letters bevatten.
invalidPasswordMinUpperCaseCharsMessage=Ongeldig wachtwoord, deze moet minstens {0} hoofdletters bevatten.
invalidPasswordMinSpecialCharsMessage=Ongeldig wachtwoord, deze moet minstens {0} speciale tekens bevatten.
invalidPasswordNotUsernameMessage=Ongeldig wachtwoord, deze mag niet overeen komen met de gebruikersnaam.
invalidPasswordRegexPatternMessage=Ongeldig wachtwoord, deze komt niet overeen met opgegeven reguliere expressie(s).
invalidPasswordHistoryMessage=Ongeldig wachtwoord, deze mag niet overeen komen met een van de laatste {0} wachtwoorden.
invalidPasswordGenericMessage=Ongeldig wachtwoord: het nieuwe wachtwoord voldoet niet aan de opgestelde eisen.
failedToProcessResponseMessage=Het verwerken van de respons is mislukt
httpsRequiredMessage=HTTPS vereist
realmNotEnabledMessage=Realm niet geactiveerd
invalidRequestMessage=Ongeldige request
failedLogout=Afmelden is mislukt
unknownLoginRequesterMessage=De login requester is onbekend
loginRequesterNotEnabledMessage=De login requester is niet geactiveerd
bearerOnlyMessage=Bearer-only applicaties mogen geen browserlogin initiëren
standardFlowDisabledMessage=Client mag geen browserlogin starten met het opgegeven response_type. Standard flow is uitgeschakeld voor de client.
implicitFlowDisabledMessage=Client mag geen browserlogin starten met opgegeven response_type. Implicit flow is uitgeschakeld voor de klant.
invalidRedirectUriMessage=Ongeldige redirect-URI
unsupportedNameIdFormatMessage=Niet-ondersteunde NameIDFormat
invalidRequesterMessage=Ongeldige requester
registrationNotAllowedMessage=Registratie is niet toegestaan
resetCredentialNotAllowedMessage=Het opnieuw instellen van de aanmeldgegevens is niet toegestaan
permissionNotApprovedMessage=Recht verworpen.
noRelayStateInResponseMessage=Geen relay state in antwoord van de identity provider.
insufficientPermissionMessage=Onvoldoende rechten om identiteiten te koppelen.
couldNotProceedWithAuthenticationRequestMessage=Het authenticatieverzoek naar de identity provider wordt afgebroken.
couldNotObtainTokenMessage=Kon geen token bemachtigen van de identity provider.
unexpectedErrorRetrievingTokenMessage=Onverwachte fout bij het ophalen van de token van de identity provider.
unexpectedErrorHandlingResponseMessage=Onverwachte fout bij het verwerken van de respons van de identity provider.
identityProviderAuthenticationFailedMessage=Verificatie mislukt. Er kon niet worden geauthenticeerd met de identity provider.
couldNotSendAuthenticationRequestMessage=Kan het authenticatieverzoek niet verzenden naar de identity provider.
unexpectedErrorHandlingRequestMessage=Onverwachte fout bij het verwerken van het authenticatieverzoek naar de identity provider.
invalidAccessCodeMessage=Ongeldige toegangscode.
sessionNotActiveMessage=Sessie inactief.
invalidCodeMessage=Er is een fout opgetreden, probeer nogmaals in te loggen.
identityProviderUnexpectedErrorMessage=Onverwachte fout tijdens de authenticatie met de identity provider
identityProviderNotFoundMessage=Geen identity provider gevonden met deze naam.
identityProviderLinkSuccess=Uw account is met succes gekoppeld aan {0} account {1}.
staleCodeMessage=Deze pagina is verlopen. Keer terug naar uw applicatie om opnieuw in te loggen.
realmSupportsNoCredentialsMessage=Realm ondersteunt geen enkel soort aanmeldgegeven.
identityProviderNotUniqueMessage=Realm ondersteunt meerdere identity providers. Er kon niet bepaald worden welke identity provider er gebruikt zou moeten worden tijdens de authenticatie.
emailVerifiedMessage=Uw e-mailadres is geverifieerd.
staleEmailVerificationLink=De link die u gebruikt is verlopen, wellicht omdat u uw e-mailadres al eerder geverifieerd heeft.
identityProviderAlreadyLinkedMessage=De door {0} teruggegeven gefedereerde identiteit is al aan een andere gebruiker gekoppeld.
confirmAccountLinking=Bevestig dat het account {0} van identity provider {1} overeenkomt met uw account.
confirmEmailAddressVerification=Bevestig dat e-mailadres {0} valide is.
confirmExecutionOfActions=Voer de volgende actie(s) uit
backToApplication=&laquo; Terug naar de applicatie
missingParameterMessage=Missende parameters: {0}
clientNotFoundMessage=Client niet gevonden.
clientDisabledMessage=Client is inactief.
invalidParameterMessage=Ongeldige parameter: {0}
alreadyLoggedIn=U bent al ingelogd.
differentUserAuthenticated=U bent in deze sessie al als de gebruiker "{0}" aangemeld. Log eerst uit.
brokerLinkingSessionExpired=Broker account linking aangevraagd, maar de huidige sessie in verlopen.
proceedWithAction=&raquo; Klik hier om verder te gaan
requiredAction.CONFIGURE_TOTP=Configureer OTP
requiredAction.TERMS_AND_CONDITIONS=Voorwaarden
requiredAction.UPDATE_PASSWORD=Update wachtwoord
requiredAction.UPDATE_PROFILE=Update profiel
requiredAction.VERIFY_EMAIL=Verifieer e-mail
doX509Login=U wordt ingelogd als\:
clientCertificate=X509 client certificate\:
noCertificate=[No Certificate]


pageNotFound=Pagina niet gevonden
internalServerError=Er is een interne serverfout opgetreden
console-username=Gebruikersnaam:
console-password=Wachtwoord:
console-otp=Eenmalige code:
console-new-password=Nieuw wachtwoord:
console-confirm-password=Bevestig wachtwoord:
console-update-password=Een update van uw wachtwoord is verplicht.
console-verify-email=U bent verplicht om uw e-mailadres te verifiëren. Een e-mail met de verificatiecode is naar {0} gestuurd. Voer deze code hieronder in.
console-email-code=E-mail Code:
console-accept-terms=Accepteert u de voorwaarden? [y/n]:
console-accept=y

# Identity provider
identity-provider-redirector=Gebruik een andere Identity Provider
identity-provider-login-label=Of login met
idp-email-verification-display-name=E-mail Verificatie
idp-email-verification-help-text=Bevestig uw account per e-mail.
idp-username-password-form-display-name=Gebruikersnaam en wachtwoord
idp-username-password-form-help-text=Bevestig uw account door in te loggen.
oauthGrantTos=algemene voorwaarden.
oauthGrantPolicy=privacybeleid.
authenticateStrong=Sterke authenticatie vereist om verder te gaan
