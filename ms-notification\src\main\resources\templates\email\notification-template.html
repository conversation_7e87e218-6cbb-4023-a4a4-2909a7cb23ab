<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${subject}">Notification Parc Auto</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333333;
        }
        
        .email-container {
            max-width: 650px;
            margin: 20px auto;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .notification-icon {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .notification-id {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 10px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #495057;
        }
        
        .notification-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #667eea;
            padding: 25px;
            border-radius: 8px;
            margin: 25px 0;
        }
        
        .summary-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .summary-text {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
        }
        
        .metadata {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .metadata h3 {
            color: #495057;
            font-size: 18px;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .metadata-grid {
            display: grid;
            gap: 15px;
        }
        
        .metadata-item {
            display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #f8f9fa;
            align-items: flex-start;
        }
        
        .metadata-item:last-child {
            border-bottom: none;
        }
        
        .metadata-label {
            font-weight: 600;
            color: #6c757d;
            min-width: 160px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .metadata-value {
            color: #333333;
            font-size: 14px;
            flex: 1;
            line-height: 1.5;
        }
        
        .metadata-value.important {
            font-weight: 600;
            color: #dc3545;
        }
        
        .metadata-value.success {
            font-weight: 600;
            color: #28a745;
        }
        
        .action-section {
            text-align: center;
            margin: 30px 0;
        }
        
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }
        
        .footer {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
        .footer-content {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .footer p {
            font-size: 12px;
            color: #6c757d;
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .footer-logo {
            font-weight: 700;
            color: #495057;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .header {
                padding: 25px 20px;
            }
            
            .header h1 {
                font-size: 22px;
            }
            
            .content {
                padding: 25px 20px;
            }
            
            .metadata {
                padding: 20px;
            }
            
            .metadata-item {
                flex-direction: column;
                gap: 5px;
            }
            
            .metadata-label {
                min-width: auto;
                font-weight: 700;
            }
            
            .footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="header-content">
                <div class="notification-icon">📢</div>
                <h1 th:text="${title}">Notification Parc Auto</h1>
                <div class="notification-id" th:if="${entiteLieeId != null}">
                    Notification #<span th:text="${entiteLieeId}">001</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <p class="greeting">Bonjour,</p>
            
            <div class="notification-summary">
                <div class="summary-title" th:text="${title}">Nouvelle notification</div>
                <div class="summary-text" th:text="${message}">
                    Vous avez reçu une nouvelle notification du système Parc Auto.
                </div>
            </div>
            
            <div class="metadata" th:if="${metadata != null && !metadata.isEmpty()}">
                <h3>📋 Informations détaillées</h3>
                <div class="metadata-grid">
                    <div class="metadata-item" th:each="entry : ${metadata}">
                        <span class="metadata-label" th:text="${entry.key + ':'}">Clé:</span>
                        <span class="metadata-value" th:text="${entry.value}">Valeur</span>
                    </div>
                </div>
            </div>
            
            <div class="action-section" th:if="${urlAction != null}">
                <a th:href="${urlAction}" class="action-button">
                    🔍 Voir les détails
                </a>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-content">
                <div class="footer-logo">OTBS - OneTech Business Solutions</div>
                <p>Ce message a été envoyé automatiquement par la plateforme Parc Auto.</p>
                <p>© 2025 OTBS - OneTech Business Solutions - Tous droits réservés</p>
                <p>📢 Système de notification intelligent Parc Auto</p>
            </div>
        </div>
    </div>
</body>
</html> 