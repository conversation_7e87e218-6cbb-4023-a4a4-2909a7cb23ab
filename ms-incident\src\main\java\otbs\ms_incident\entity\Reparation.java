package otbs.ms_incident.entity;

import otbs.ms_incident.enums.StatusReparation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Entité représentant une réparation suite à un incident")
public class Reparation extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Identifiant unique de la réparation", example = "1")
    private Long id;

    @Schema(description = "Date de la réparation", example = "2023-06-01")
    private LocalDate dateReparation;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Schema(description = "Statut de la réparation")
    private StatusReparation status = StatusReparation.EN_COURS;

    @Column(length = 1000)
    @Schema(description = "Description détaillée des travaux de réparation",
            example = "Remplacement de l'aile avant gauche et peinture")
    private String description;

    @Schema(description = "Coût total de la réparation", example = "1250.50")
    private BigDecimal cout;

    @Schema(description = "Garage ayant effectué la réparation", example = "Garage Central")
    private String garage;
    
    @Schema(description = "Indique si la réparation a été remboursée par l'assurance", example = "true")
    private Boolean rembourse;

    @Schema(description = "Montant couvert par l'assurance", example = "1000.00")
    private BigDecimal montantCouverture;

    @ManyToOne
    @JoinColumn(name = "incident_id")
    @Schema(description = "Incident associé à cette réparation")
    private Incident incident;
}