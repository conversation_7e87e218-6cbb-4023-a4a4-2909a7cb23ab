package otbs.ms_astreint.client.reservation.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * DTO pour représenter un véhicule provenant du microservice ms-reservation.
 * Cette classe contient les informations nécessaires pour représenter un véhicule
 * dans le contexte du microservice ms-astreinte.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VehiculeDto {
    private Long idVehicule;
    private String immatriculation;
    private String marque;
    private int kilometrage;
    private Date datePrelevementKilometrage;
    private String categorie;
    private String typeCarburant;
    private int nombreDePlaces;
    private boolean remisCles;
    private boolean astreinte;
    private String etat;
}
