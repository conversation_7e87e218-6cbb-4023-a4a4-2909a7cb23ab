package otbs.ms_notification.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import otbs.ms_notification.client.keycloak.KeycloakUserService;
import otbs.ms_notification.client.keycloak.UserDTO;
import otbs.ms_notification.client.keycloak.UserSelectorOptionDTO;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserService {

    private final KeycloakUserService keycloakUserService;

    public String getEmailFromUserId(String userId) {
        return keycloakUserService.getEmailFromUserId(userId);
    }
    
    public Optional<UserDTO> getUserInfo(String userId) {
        return keycloakUserService.getUserInfo(userId);
    }

    public String getFullNameFromUserId(String userId) {
        return keycloakUserService.getFullNameFromUserId(userId);
    }

    public boolean userExists(String userId) {
        return keycloakUserService.userExists(userId);
    }

    public boolean isUserActive(String userId) {
        return keycloakUserService.isUserActive(userId);
    }

    public List<UserDTO> searchUsersByEmail(String email) {
        return keycloakUserService.searchUsersByEmail(email);
    }

    public List<UserDTO> searchUsersByUsername(String username) {
        return keycloakUserService.searchUsersByUsername(username);
    }

    public List<UserDTO> getAllActiveUsers() {
        return keycloakUserService.getAllActiveUsers();
    }

    public List<UserDTO> getAllUsers() {
        return keycloakUserService.getAllUsers();
    }

    public List<UserSelectorOptionDTO> getUsersForSelector() {
        return keycloakUserService.getUsersForSelector();
    }

    public List<UserDTO> searchUsersByName(String name) {
        return keycloakUserService.searchUsersByName(name);
    }
}