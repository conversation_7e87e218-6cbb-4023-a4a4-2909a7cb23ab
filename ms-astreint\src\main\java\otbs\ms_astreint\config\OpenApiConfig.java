package otbs.ms_astreint.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.security.OAuthFlow;
import io.swagger.v3.oas.models.security.OAuthFlows;
import io.swagger.v3.oas.models.security.Scopes;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Value("${spring.application.name:ms-astreint}")
    private String applicationName;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUri;

    @Value("${springdoc.swagger-ui.oauth.client-id:ms-astreint-client}")
    private String clientId;

    @Value("${springdoc.swagger-ui.oauth.client-secret:}")
    private String clientSecret;

    @Bean
    public OpenAPI customOpenAPI() {
        final String securitySchemeName = "oauth2";
        String realm = issuerUri.substring(issuerUri.lastIndexOf("/") + 1);
        String authServerUrl = issuerUri.replace("/realms/" + realm, "");
        
        return new OpenAPI()
                .info(new Info()
                        .title("API de Gestion des Astreintes")
                        .description("API pour gérer les astreintes des consultants")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("OTBS")
                                .url("https://otbs.tn")
                                .email("<EMAIL>")))
                .addSecurityItem(new SecurityRequirement().addList(securitySchemeName))
                .components(new Components()
                        .addSecuritySchemes(securitySchemeName, new SecurityScheme()
                                .name(securitySchemeName)
                                .type(SecurityScheme.Type.OAUTH2)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .flows(new OAuthFlows()
                                        .password(new OAuthFlow()
                                                .tokenUrl(authServerUrl + "/realms/" + realm + "/protocol/openid-connect/token")
                                                .refreshUrl(authServerUrl + "/realms/" + realm + "/protocol/openid-connect/token"))
                                        .authorizationCode(new OAuthFlow()
                                                .authorizationUrl(authServerUrl + "/realms/" + realm + "/protocol/openid-connect/auth")
                                                .tokenUrl(authServerUrl + "/realms/" + realm + "/protocol/openid-connect/token")
                                                .refreshUrl(authServerUrl + "/realms/" + realm + "/protocol/openid-connect/token")))));
    }
}
