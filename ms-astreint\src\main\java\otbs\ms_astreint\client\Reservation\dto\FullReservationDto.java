package otbs.ms_astreint.client.reservation.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FullReservationDto {
    private Long reservationId;
    private List<Long> missionIds;
    private String destination;
    private LocalDateTime dateDebut;
    private LocalDateTime dateFin;
    private String immatriculation;
    private List<String> numerosCartes;
    private String numeroBadge;
    private int nombreTicketResto;
    private double frais;
    private String description;
    private String creatorId;
    private String projectName;
}
