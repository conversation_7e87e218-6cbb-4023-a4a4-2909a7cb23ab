doLogIn=Prisijungti
doRegister=Registruotis
doRegisterSecurityKey=Registruotis
doCancel=Atšaukti
doSubmit=Patvirtinti
doYes=Taip
doNo=Ne
doContinue=Tęsti
doAccept=Patvirtinti
doDecline=Atšaukti
doForgotPassword=Pamiršote slaptažodį?
doClickHere=Spauskite čia
doImpersonate=Apsimesti kaip
kerberosNotConfigured=Kerberos nesukonfigūruotas
kerberosNotConfiguredTitle=Kerberos nesukonfigūruotas
bypassKerberosDetail=Jūs neprisijungęs per Kerberos arba Jūsų naršyklė nesukonfigūruota Kerberos prisijungimui.  Tęskite ir pasirinkite kitą prisijungimo būdą
kerberosNotSetUp=Kerberos nesukonfigūruotas.  Jūs negalite prisijungti.
registerWithTitle=Registruotis su {0}
registerWithTitleHtml={0}
loginTitle=Prisijungti su {0}
loginTitleHtml={0}
impersonateTitle=Apsimesti kaip naudotojas {0} 
impersonateTitleHtml=Apsimesti kaip <strong>{0}</strong>
realmChoice=Sritis
unknownUser=Nežinomas naudotojas
loginTotpTitle=Mobilaus autentifikatoriaus nustatymas
loginProfileTitle=Atnaujinti paskyros informaciją
loginTimeout=Užtrukote per ilgai. Prisijungimo procesas pradedamas iš naujo.
oauthGrantTitle=Suteitikti prieigą
oauthGrantTitleHtml={0}
errorTitle=Atsiprašome ...
errorTitleHtml=<strong>Atsiprašome</strong> ...
emailVerifyTitle=El. pašto adreso patvirtinimas
emailForgotTitle=Pamiršote slaptažodį?
updatePasswordTitle=Atnaujinti slaptažodį
codeSuccessTitle=Sėkmė
codeErrorTitle=Klaidos kodas\: {0}

termsTitle=Naudojimo sąlygos
termsTitleHtml=Naudojimo sąlygos
termsText=<p>Naudojimo sąlygos nenurodytos</p>

recaptchaFailed=Recaptcha neteisingas
recaptchaNotConfigured=Reikalingas Recaptcha nesukonfigūruotas
consentDenied=Prieiga draudžiama.

noAccount=Dar neturite paskyros?
username=Naudotojo vardas
usernameOrEmail=Naudotojo vardas arba el. pašto adresas
firstName=Vardas
givenName=Vardas
fullName=Pavardė
lastName=Pavardė
familyName=Pavardė
email=El. paštas
password=Slaptažodis
passwordConfirm=Pakartotas slaptažodis
passwordNew=Naujas slaptažodis
passwordNewConfirm=Pakartotas naujas slaptažodis
rememberMe=Prisiminti mane
authenticatorCode=Vienkartinis kodas
address=Adresas
street=Gatvė
locality=Miestas arba vietovė
region=Rajonas
postal_code=Pašto kodas
country=Šalis
emailVerified=El. pašto adresas patvirtintas
gssDelegationCredential=GSS prisijungimo duomenų delegavimas

loginTotpStep1=Įdiekite <a href="https://freeotp.github.io/" target="_blank">FreeOTP</a> arba Google Authenticator savo įrenginyje. Programėlės prieinamos <a href="https://play.google.com">Google Play</a> ir Apple App Store.
loginTotpStep2=Atidarykite programėlę ir nuskenuokite barkodą arba įveskite kodą.
loginTotpStep3=Įveskite programėlėje sugeneruotą vieną kartą galiojantį kodą ir paspauskite Saugoti norėdami prisijungti.
loginOtpOneTime=Vienkartinis kodas

oauthGrantRequest=Ar Jūs suteikiate šias prieigos teises?
inResource=į

emailVerifyInstruction1=El. paštas su instrukcijomis ir patvirtinimo nuoroda nusiųsti į Jūsų el. paštą.
emailVerifyInstruction2=El. paštu negavote patvirtinimo kodo?
emailVerifyInstruction3=pakartotoinai siųsti el. laišką.

emailLinkIdpTitle=Susieti {0}
emailLinkIdp1=El. pašto laiškas su instrukcijomis susieti {0} paskyrą {1} su {2} buvo nusiųstas.
emailLinkIdp2=Negavote patvirtinimo kodo el. paštu?
emailLinkIdp3=pakartotoinai siųsti el. laišką.

backToLogin=&laquo; Grįžti į prisijungimo langą

emailInstruction=Įveskite naudotojo vardą arba slaptažodį ir slaptažodžio pakeitimo instrukcijos bus atsiųstos Jums el. paštu 

copyCodeInstruction=Nukopijuokite šį kodą į Jūsų programą:

personalInfo=Asmeninė informacija:
role_admin=Administratorius
role_realm-admin=Srities administravimas
role_create-realm=Kurti sritį
role_create-client=Kurti programą
role_view-realm=Peržiūrėti sritį
role_view-users=Peržiūrėti naudotojus
role_view-applications=Peržiūrėti programas
role_view-clients=Peržiūrėti klientines programas
role_view-events=Peržiūrėti įvykių žurnalą
role_view-identity-providers=Peržiūrėti tapatybės teikėjus
role_manage-realm=Valdyti sritis
role_manage-users=Valdyti naudotojus
role_manage-applications=Valdyti programas
role_manage-identity-providers=Valdyti tapatybės teikėjus
role_manage-clients=Valdyti programas
role_manage-events=Valdyti įvykius
role_view-profile=Peržiūrėti paskyrą
role_manage-account=Valdyti paskyrą
role_read-token=Skaityti prieigos rakšą
role_offline-access=Darbas neprisijungus
client_account=Paskyra
client_security-admin-console=Saugumo administravimo konsolė
client_admin-cli=Administravimo CLI
client_realm-management=Srities valdymas
client_broker=Tarpininkas

invalidUserMessage=Neteisingas naudotojo vardas arba slaptažodis.
invalidEmailMessage=Neteisingas el. pašto adresas.
accountDisabledMessage=Paskyros galiojimas sustabdytas, kreipkitės į administratorių.
accountTemporarilyDisabledMessage=Neteisingas naudotojo vardas arba slaptažodis.
accountPermanentlyDisabledMessage=Neteisingas naudotojo vardas arba slaptažodis.
accountTemporarilyDisabledMessageTotp=Neteisingas autentifikacijos kodas.
accountPermanentlyDisabledMessageTotp=Neteisingas autentifikacijos kodas.
expiredCodeMessage=Prisijungimo laikas baigėsi. Bandykite dar kartą.

missingFirstNameMessage=Prašome įvesti vardą.
missingLastNameMessage=Prašome įvesti pavardę.
missingEmailMessage=Prašome įvesti el. pašto adresą.
missingUsernameMessage=Prašome įvesti naudotojo vardą.
missingPasswordMessage=Prašome įvesti slaptažodį.
missingTotpMessage=Prašome įvesti autentifikacijos kodą.
notMatchPasswordMessage=Slaptažodžiai nesutampa.

invalidPasswordExistingMessage=Neteisingas dabartinis slaptažodis.
invalidPasswordConfirmMessage=Pakartotas slaptažodis nesutampa.
invalidTotpMessage=Neteisingas autentifikacijos kodas.

usernameExistsMessage=Toks naudotojas jau egzistuoja.
emailExistsMessage=El. pašto adresas jau egzistuoja.

federatedIdentityExistsMessage=Naudotojas {0} {1} jau egzistuoja. Prašome prsijungti prie naudotojų valdymo posistemės paskyrų susiejimui.

confirmLinkIdpTitle=Paskyra jau egzistuoja
federatedIdentityConfirmLinkMessage=Naudotojas {0} {1} jau egzistuoja. Ar tęsti?
federatedIdentityConfirmReauthenticateMessage=Prisijunkite norėdami susieti paskyrą su  {0}
confirmLinkIdpReviewProfile=Peržiūrėti naudotojo profilio informaciją
confirmLinkIdpContinue=Susieti su egzistuojančia paskyra

configureTotpMessage=Paskyros aktyvavimui Jums reikalingas Mobilus autentifikatorius.
updateProfileMessage=Paskyros aktyvavimui Jums reikia atnaujinti profilio informaciją.
updatePasswordMessage=Paskyros aktyvavimui Jums reikia pakeisti slaptažodį.
verifyEmailMessage=Paskyros aktyvavimui Jums reikia patvirtinti el. pašto adresą.
linkIdpMessage=El. pašto adreso susiejimui su Jūsu paskyra {0} reikalingas patvirtinimas.

emailSentMessage=Netrukus turėtumėte gauti el. pašto adresą su instrukcijomis.
emailSendErrorMessage=Klaida siunčiant el. paštą, bandykite vėliau.

accountUpdatedMessage=Jųsų paskyros informacija atnaujinta.
accountPasswordUpdatedMessage=Jūsų slaptažodis pakeistas.

noAccessMessage=Prieiga negalima

invalidPasswordMinLengthMessage=Neteisingas slaptažodis: privalomi bent {0} simboliai.
invalidPasswordMinDigitsMessage=Neteisingas slaptažodis: privalomi bent {0} skaitmenys.
invalidPasswordMinLowerCaseCharsMessage=Neteisingas slaptažodis: privalomos bent {0} mažosios raidės.
invalidPasswordMinUpperCaseCharsMessage=Neteisingas slaptažodis: privalomos bent {0} didžiosios raidės.
invalidPasswordMinSpecialCharsMessage=Neteisingas slaptažodis: privalomi bent {0} specialūs simboliai.
invalidPasswordNotUsernameMessage=Neteisingas slaptažodis: negali sutapti su naudotojo vardu.
invalidPasswordRegexPatternMessage=Neteisingas slaptažodis: neatitinka regexp taisyklės.
invalidPasswordHistoryMessage=Neteisingas slaptažodis: negali sutapti su prieš tai naudotais {0} slaptažodžiais.

failedToProcessResponseMessage=Klaida apdorojant atsakymą
httpsRequiredMessage=Privalomas HTTPS
realmNotEnabledMessage=Srities galiojimas išjungtas
invalidRequestMessage=Neteisinga užklausa
failedLogout=Nepavyko užbaigti sesijos
unknownLoginRequesterMessage=Nežinomas prisijungimo prašytojas
loginRequesterNotEnabledMessage=Prisijungimo prašytojo galiojimas išjungtas
bearerOnlyMessage=Programos, sukonfigūruotos tik kaip perdavėjai, negali inicijuoti prisijungimą per naršyklę.
standardFlowDisabledMessage=Su pateiktu atsakymo tipu prisijungimas per naršyklę šiam klientui negalimas. Šiam klientui neįgalinta standartinė seka.
implicitFlowDisabledMessage=Su pateiktu atsakymo tipu prisijungimas per naršyklę šiam klientui negalimas. Šiam klientui neįgalinta išreikštinė seka.
invalidRedirectUriMessage=Neteisinga nukreipimo nuoroda
unsupportedNameIdFormatMessage=Nepalaikomas NameIDFormat
invalidRequesterMessage=Neteisingas prašytojas
registrationNotAllowedMessage=Registracija negalima
resetCredentialNotAllowedMessage=Prisijungimo duomenų atkūrimas negalimas

permissionNotApprovedMessage=Teisį nepatvirtinta.
noRelayStateInResponseMessage=Tapatybės teikėjo atsakyme trūksta perdavimo būsenos.
insufficientPermissionMessage=Trūksta teisių tapatybių susiejimui.
couldNotProceedWithAuthenticationRequestMessage=Nepavyksta pradėti tapatybės teikėjo autentifikacijos užklausos.
couldNotObtainTokenMessage=Negaunamas prieigos raktas iš tapatybės teikėjo.
unexpectedErrorRetrievingTokenMessage=Prieigos rakšo gavimo iš tapatybės teikėjo metu įvyko netikėta klaida.
unexpectedErrorHandlingResponseMessage=Tapatybės teikėjo atsakymo apdorojimo metu įvyko netikėta klaida.
identityProviderAuthenticationFailedMessage=Autentifikacijos klaida. Nepavyksta autentifikacija su tapatybės teikėju.
couldNotSendAuthenticationRequestMessage=Tapatybės teikėjui nepavyksta nusiųsti autentifikacijos užklausos.
unexpectedErrorHandlingRequestMessage=Užklausos tapatybės teikėjui formavimo metu įvyko netikėta klaida.
invalidAccessCodeMessage=Neteisingas prieigos kodas.
sessionNotActiveMessage=Sesija neaktyvi.
invalidCodeMessage=Įvyko klaida. Prašome bandyti prisijungti dar kartą.
identityProviderUnexpectedErrorMessage=Autentifikavimo su išoriniu tapatybės teikėju metu įvyko netikėta klaida.
identityProviderNotFoundMessage=Su nurodytu identifikatoriumi nerastas tapatybės teikėjas.
identityProviderLinkSuccess=Jūsų naudotojo paskyra buvo sėkmingai susieta su {0} paskyra {1} .
staleCodeMessage=Šis puslapis nebegalioja. Prašome grįžti į programą ir bandyti prisijungti iš naujo.
realmSupportsNoCredentialsMessage=Sritis nepalaiko prisijungimų naudojant prisijungimo duomenis.
identityProviderNotUniqueMessage=Sritis palaiko daugiau nei vieną tapatybės teikėją. Negalima nustatyti kuris tapatybės teikėjas turi būti naudojamas autentifikacijai.
emailVerifiedMessage=Jūsų el. pašto adresas patvirtintas.
staleEmailVerificationLink=Nuoroda, kurią paspaudėte nebegalioja? Galbūt Jūs jau patvirtinote el. pašto adresą?

backToApplication=&laquo; Grįžti į programą
missingParameterMessage=Nenurodytas parametras\: {0}
clientNotFoundMessage=Nenurodytas klientas.
clientDisabledMessage=Kliento galiojimas išjungtas.
invalidParameterMessage=Neteisingas parametras\: {0}
alreadyLoggedIn=Jūs jau esate prisijungę.
