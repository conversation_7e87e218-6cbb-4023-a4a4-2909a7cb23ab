doLogIn=Belépés
doRegister=Regisztráció
doRegisterSecurityKey=Regisztráció
doCancel=Mégsem
doSubmit=Elküld
doBack=Vissza
doYes=Igen
doNo=Nem
doContinue=Folytat
doIgnore=Mellőz
doAccept=Elfogad
doDecline=Elutasít
doForgotPassword=Elfelejtette a jelszavát?
doClickHere=Kattintson ide
doImpersonate=Megszemélyesítés
doTryAgain=Próbálja újra
doTryAnotherWay=Pórbálja máshogyan
doConfirmDelete=Törlés megerősítése
errorDeletingAccount=Hiba történt a fiók törlése közben
deletingAccountForbidden=Nincs jogosultsága a felhasználói fiókjának törléséhez. Kérem, vegye fel a kapcsolatot az alkalmazás adminisztrátorával.
kerberosNotConfigured=Nincs beállítva Kerberos
kerberosNotConfiguredTitle=Nincs beállítva Kerberos
bypassKerberosDetail=Vagy nem Kerberosszal lépett be, vagy a böngészője nem kezeli a Kerberos alapú belépést. Kérem, kattintson a Folytat gombra, egy másik belépési módhoz.
kerberosNotSetUp=Nincs beállítva Kerberos, nem lehet belépni.
registerTitle=Regisztráció
loginAccountTitle=Jelentkezzen be a fiókjába
loginTitle=Belépés ide: {0}
loginTitleHtml={0}
impersonateTitle={0} megszemélyesített felhasználó
impersonateTitleHtml=<strong>{0}</strong> megszemélyesített felhasználó
realmChoice=Tartomány
unknownUser=Ismeretlen felhasználó
loginTotpTitle=Mobil hitelesítő eszköz beállítása
loginProfileTitle=Felhasználói fiók adatok módosítása
loginIdpReviewProfileTitle=Fiók adatainak módosítása
loginTimeout=Belépési kísérlete időtúllépés miatt meghiúsult, a belépési eljárás újraindul.
reauthenticate=Kérem, jelentkezzen be újra a folytatáshoz
oauthGrantTitle=Hozzáférés engedélyezése a következő számára: {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Győződjön meg arról, hogy megbízik a(z) {0} alkalmazásban azáltal, hogy megismeri, hogyan kezeli az adatait.
oauthGrantReview=Tekintse át 
oauthGrantTos=a Felhasználási feltételeket.
oauthGrantPolicy=az Adatvédelmi irányelveket.
errorTitle=Nagyon sajnáljuk...
errorTitleHtml=Nagyon <strong>sajnáljuk</strong> ...
emailVerifyTitle=E-mail ellenőrzés
emailForgotTitle=Elfelejtette a jelszavát?
updateEmailTitle=E-mail cím módosítása
emailUpdateConfirmationSentTitle=Megerősítő e-mail elküldve
emailUpdateConfirmationSent=Egy megerősítő e-mail került elküldésre a(z) {0} címre. Kövesse az abban található utasításokat a változtatások véglegesítéséhez.
emailUpdatedTitle=E-mail cím módosítva
emailUpdated=A fiók e-mail címe a következőre módosult: {0}.
updatePasswordTitle=Jelszó módosítása
codeSuccessTitle=Sikeres kérés kódja
codeErrorTitle=Hibakód\: {0}
displayUnsupported=A kért megjelenítési mód nem támogatott
browserRequired=A belépéshez böngésző szükséges
browserContinue=A belépés befejezéséhez böngésző szükséges
browserContinuePrompt=Megnyitja a böngészőt és folytatja a belépést? [i/n]:
browserContinueAnswer=i

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Belső
unknown=Ismeretlen

termsTitle=Felhasználási feltételek
termsText=<p>Felhasználási feltételek helye</p>
termsPlainText=Felhasználási feltételek helye
termsAcceptanceRequired=El kell fogadnia a felhasználási feltételeket.
acceptTerms=Elfogadom a felhasználási feltételeket

recaptchaFailed=Érvénytelen Recaptcha
recaptchaNotConfigured=Recaptcha szükséges, de nincsen beállítva
consentDenied=Jóváhagyó nyilatkozat elutasítva.

noAccount=Új felhasználó?
username=Felhasználónév
usernameOrEmail=Felhasználónév vagy email
firstName=Keresztnév
givenName=Keresztnév
fullName=Teljes név
lastName=Vezetéknév
familyName=Vezetéknév
email=E-mail cím
password=Jelszó
passwordConfirm=Jelszó megerősítése
passwordNew=Új jelszó
passwordNewConfirm=Új jelszó megerősítése
rememberMe=Automatikus bejelentkezés
authenticatorCode=Egyszer használatos hitelesítő kód
address=Cím
street=Közterület
locality=Település
region=Állam, Tartomány, Megye, Régió
postal_code=Irányítószám
country=Ország
emailVerified=Ellenőrzött e-mail cím
website=Weboldal
phoneNumber=Telefonszám
phoneNumberVerified=Ellenőrzött telefonszám
gender=Nem
birthday=Születési dátum
zoneinfo=Időzóna
gssDelegationCredential=GSS delegált hitelesítés
logoutOtherSessions=Kijelentkezés más eszközökről

profileScopeConsentText=Felhasználói fiók
emailScopeConsentText=E-mail cím
addressScopeConsentText=Cím
phoneScopeConsentText=Telefonszám
offlineAccessScopeConsentText=Offline hozzáférés
samlRoleListScopeConsentText=Szerepköreim
rolesScopeConsentText=Felhasználói szerepkörök

restartLoginTooltip=Belépés újrakezdése

loginTotpIntro=A felhasználói fiók hozzáféréshez be kell állítania egy egyszer használatos jelszót (OTP) generáló alkalmazást.
loginTotpStep1=Kérem, telepítse az itt felsorolt alkalmazások egyikét a mobil eszközére:
loginTotpStep2=Indítsa el az alkalmazást a mobil eszközén és olvassa be ezt a (QR) kódot:
loginTotpStep3=Adja meg az alkalmazás által generált egyszer használatos kódot majd kattintson az Elküld gombra a beállítás befejezéséhez.
loginTotpStep3DeviceName=Adja meg a mobil eszköz nevét. Ez a későbbiekben segíthet az eszköz azonosításában.
loginTotpManualStep2=Indítsa el az alkalmazást és adja meg a következő kulcsot:
loginTotpManualStep3=Használja a következő beállításokat, ha az alkalmazása támogatja ezeket:
loginTotpUnableToScan=Nem tud (QR) kódot beolvasni?
loginTotpScanBarcode=Inkább (QR) kódot olvasna be?
loginCredential=Jelszó
loginOtpOneTime=Egyszer használatos kód
loginTotpType=Típus
loginTotpAlgorithm=Algoritmus
loginTotpDigits=Számjegyek
loginTotpInterval=Intervallum
loginTotpCounter=Számláló
loginTotpDeviceName=Eszköz neve

loginTotp.totp=Idő alapú
loginTotp.hotp=Számláló alapú

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=Válasszon egy belépési módszert

oauthGrantRequest=Engedélyezi a következő hozzáférés jogosultságokat?
inResource=Itt:

oauth2DeviceVerificationTitle=Eszköz hitelesítése
verifyOAuth2DeviceUserCode=Írja be az eszköz által generált kódot, majd kattintson az Elküld gombra
oauth2DeviceInvalidUserCodeMessage=Érvénytelen kód, kérem, próbálja újra.
oauth2DeviceExpiredUserCodeMessage=A kód érvényessége lejárt. Kérem, térjen vissza az eszközhöz és próbálja újra.
oauth2DeviceVerificationCompleteHeader=Eszköz hitelesítése sikeres
oauth2DeviceVerificationCompleteMessage=Bezárhatja ezt az oldalt és visszatérhet az eszközhöz.
oauth2DeviceVerificationFailedHeader=Eszköz hitelesítése sikertelen
oauth2DeviceVerificationFailedMessage=Bezárhatja ezt az oldalt. Kérem, térjen vissza az eszközhöz és próbáljon meg csatlakozni újra.
oauth2DeviceConsentDeniedMessage=Jóváhagyó nyilatkozat elutasítva az eszköz számára.
oauth2DeviceAuthorizationGrantDisabledMessage=Az alkalmazás nem indíthat OAuth 2.0 Eszköz Hitelesítést. Ez a folyamat nincsen engedélyezve ezen alkalmazás számára.

emailVerifyInstruction1=A megadott e-mail címre elküldtük az e-mail cím megerősítéséhez szükséges lépéseket tartalmazó üzenetet.
emailVerifyInstruction2=Nem kapott megerősítő kódot tartalmazó e-mail üzenetet?
emailVerifyInstruction3=az ellenőrző kód ismételt kiküldéséhez.

emailLinkIdpTitle={0} összekötés
emailLinkIdp1=A(z) {1} {0} fiók és a(z) {2} fiókjának összekötéséhez szükséges tudnivalókat e-mail üzenetben elküldtük Önnek.
emailLinkIdp2=Nem kapott megerősítő kódot tartalmazó e-mail üzenetet?
emailLinkIdp3=az ellenőrző kód ismételt kiküldéséhez.
emailLinkIdp4=Ha egy másik böngészőben már jóváhagyta az e-mail címét
emailLinkIdp5=a folytatáshoz.

backToLogin=&laquo; Vissza a belépéshez

emailInstruction=Adja meg a felhasználónevét vagy e-mail címét, hogy az új jelszó beállításához szükséges tudnivalókat elküldhessük Önnek.
emailInstructionUsername=Adja meg a felhasználónevét, hogy az új jelszó beállításához szükséges tudnivalókat elküldhessük Önnek.

copyCodeInstruction=Kérem, másolja ki ezt a kódot és illessze be az alkalmazásába:

pageExpiredTitle=A lap érvényessége lejárt
pageExpiredMsg1=Ahhoz, hogy újrakezdje a belépési eljárást
pageExpiredMsg2=Ahhoz, hogy folytassa a belépési eljárást

personalInfo=Személyes adatok:
role_admin=Adminisztrátor
role_realm-admin=Tartomány Adminisztrátor
role_create-realm=Tartomány létrehozása
role_create-client=Kliens létrehozása
role_view-realm=Tartományok megtekintése
role_view-users=Felhasználók megtekintése
role_view-applications=Alkalmazások megtekintése
role_view-clients=Kliensek megtekintése
role_view-events=Események megtekintése
role_view-identity-providers=Személyazonosság-kezelők megtekintése
role_manage-realm=Tartományok kezelése
role_manage-users=Felhasználók kezelése
role_manage-applications=Alkalmazások kezelése
role_manage-identity-providers=Személyazonosság-kezelők karbantartása
role_manage-clients=Kliensek kezelése
role_manage-events=Események kezelése
role_view-profile=Fiók megtekintése
role_manage-account=Fiók kezelése
role_manage-account-links=Fiók összekötések kezelése
role_read-token=Olvasási token
role_offline-access=Offline hozzáférés
client_account=Fiók
client_account-console=Fiók kezelés
client_security-admin-console=Biztonsági, adminisztrátor fiók kezelés
client_admin-cli=Admin CLI
client_realm-management=Tartomány kezelés
client_broker=Ügynök

requiredFields=Kötelezően kitöltendő mezők

invalidUserMessage=Érvénytelen felhasználónév vagy jelszó.
invalidUsernameMessage=Érvénytelen felhasználónév.
invalidUsernameOrEmailMessage=Érvénytelen felhasználónév vagy e-mail cím.
invalidPasswordMessage=Érvénytelen jelszó.
invalidEmailMessage=Érvénytelen e-mail cím.
accountDisabledMessage=Felhasználói fiókja inaktív, kérem, vegye fel a kapcsolatot az alkalmazás adminisztrátorával.
accountTemporarilyDisabledMessage=Érvénytelen felhasználónév vagy jelszó.
accountPermanentlyDisabledMessage=Érvénytelen felhasználónév vagy jelszó.
accountTemporarilyDisabledMessageTotp=Érvénytelen hitelesítő kód.
accountPermanentlyDisabledMessageTotp=Érvénytelen hitelesítő kód.
expiredCodeMessage=Belépési időtúllépés, kérem, lépjen be újra.
expiredActionMessage=A művelet érvényességi ideje lejárt. Kérem, lépjen be újra.
expiredActionTokenNoSessionMessage=A művelet érvényességi ideje lejárt.
expiredActionTokenSessionExistsMessage=A művelet érvényességi ideje lejárt. Kérem, ismételje meg a műveletet.
sessionLimitExceeded=Túl sok az aktív munkamenet

missingFirstNameMessage=Kérem, adja meg a keresztnevet.
missingLastNameMessage=Kérem, adja meg a vezetéknevet.
missingEmailMessage=Kérem, adja meg az e-mail címet.
missingUsernameMessage=Kérem, adja meg a felhasználónevét.
missingPasswordMessage=Kérem, adja meg a jelszót.
missingTotpMessage=Kérem, adja meg a hitelesítő kódot.
missingTotpDeviceNameMessage=Kérem, adja meg az eszköz nevét.
notMatchPasswordMessage=A jelszavak nem egyeznek meg.

error-invalid-value=Érvénytelen érték
error-invalid-blank=Kérem, adja meg a mező értékét.
error-empty=Kérem, adja meg a mező értékét.
error-invalid-length=A hossznak {1} és {2} karakter között kell lennie.
error-invalid-length-too-short=A minimális hossz {1} karakter.
error-invalid-length-too-long=A maximális hossz {2} karakter.
error-invalid-email=Érvénytelen e-mail cím.
error-invalid-number=Érvénytelen szám.
error-number-out-of-range=A szám {1} és {2} közötti értéket vehet fel.
error-number-out-of-range-too-small=A szám minimális értéke: {1}.
error-number-out-of-range-too-big=A szám maximális értéke: {2}.
error-pattern-no-match=Érvénytelen érték.
error-invalid-uri=Érvénytelen URL.
error-invalid-uri-scheme=Érvénytelen URL séma.
error-invalid-uri-fragment=Érvénytelen URL fragmens.
error-user-attribute-required=Kérem, töltse ki ezt a mezőt.
error-invalid-date=Érvénytelen dátum.
error-user-attribute-read-only=Ez a mező csak olvasható.
error-username-invalid-character=A felhasználónév érvénytelen karaktert tartalmaz.
error-person-name-invalid-character=A név érvénytelen karaktert tartalmaz.
error-reset-otp-missing-id=Kérem, válasszon egyszer használatos hitelesítési (OTP) eljárást.

invalidPasswordExistingMessage=Érvénytelen jelenlegi jelszó.
invalidPasswordBlacklistedMessage=Érvénytelen jelszó: a jelszó tiltó listán szerepel.
invalidPasswordConfirmMessage=A jelszavak nem egyeznek meg.
invalidTotpMessage=Érvénytelen hitelesítő kód.

usernameExistsMessage=Ez a felhasználónév már foglalt.
emailExistsMessage=Ez az e-mail cím már foglalt.

federatedIdentityExistsMessage=A megadott {0} {1} felhasználó már létezik. Kérem, lépjen be a Keycloak Fiók Kezelőbe, hogy összeköthesse a fiókokat.
federatedIdentityUnavailableMessage=A(z) {1} személyazonosság-kezelő által hitelesített felhasználó, {0} nem létezik. Kérem, vegye fel a kapcsolatot az alkalmazás adminisztrátorával.
federatedIdentityUnmatchedEssentialClaimMessage=A személyazonosság-kezelő által kiállított ID token nem egyezik az alapvető beállítással. Kérem, vegye fel a kapcsolatot az alkalmazás adminisztrátorával.

confirmLinkIdpTitle=A felhasználói fiók már létezik
federatedIdentityConfirmLinkMessage=A megadott {0} {1} felhasználó már létezik. Hogyan tovább?
federatedIdentityConfirmReauthenticateMessage=Azonosítsa magát, hogy összeköthesse a felhasználói fiókját a következővel: {0}
nestedFirstBrokerFlowMessage=A(z) {0} {1} felhasználó nincs összekötve egyetlen ismert felhasználóval sem.
confirmLinkIdpReviewProfile=Fiók áttekintése
confirmLinkIdpContinue=Hozzáadás meglévő fiókhoz

configureTotpMessage=Fiókja aktiválásához előbb be kell állítania egy mobil hitelesítő eszközt.
configureBackupCodesMessage=Fiókja aktiválásához előbb létre kell hoznia Biztonsági Kódokat.
updateProfileMessage=Fiókja aktiválásához előbb módosítania kell a felhasználói adatait.
updatePasswordMessage=Fiókja aktiválásához előbb le kell cserélnie a jelszavát.
updateEmailMessage=Fiókja aktiválásához előbb módosítania kell e-mail címét.
resetPasswordMessage=Cserélje le jelszavát!
verifyEmailMessage=Fiókja aktiválásához előbb erősítse meg e-mail címét.
linkIdpMessage=Fiókja összekötéséhez előbb erősítse meg e-mail címét a következővel: {0}.

emailSentMessage=Hamarosan e-mail üzenetet küldünk a további tudnivalókról.
emailSendErrorMessage=Az e-mail üzenetet nem tudtuk elküldeni. Kérem, próbálja meg később.

accountUpdatedMessage=A felhasználói fiók adatai megváltoztak.
accountPasswordUpdatedMessage=A jelszava megváltozott.

delegationCompleteHeader=Sikeres belépés
delegationCompleteMessage=Becsukhatja a böngésző ablakot és visszatérhet a konzolos alkalmazásához.
delegationFailedHeader=Sikertelen belépés
delegationFailedMessage=Becsukhatja a böngésző ablakot és visszatérhet a konzolos alkalmazásához, ahol újból megpróbálhat a belépni.

noAccessMessage=Nincs hozzáférés

invalidPasswordMinLengthMessage=Érvénytelen jelszó: minimum hossz: {0}.
invalidPasswordMaxLengthMessage=Érvénytelen jelszó: maximum hossz: {0}.
invalidPasswordMinDigitsMessage=Érvénytelen jelszó: legalább {0} darab számjegyet kell tartalmaznia.
invalidPasswordMinLowerCaseCharsMessage=Érvénytelen jelszó: legalább {0} darab kisbetűt kell tartalmaznia.
invalidPasswordMinUpperCaseCharsMessage=Érvénytelen jelszó: legalább {0} darab nagybetűt kell tartalmaznia.
invalidPasswordMinSpecialCharsMessage=Érvénytelen jelszó: legalább {0} darab speciális karaktert (pl. #!$@ stb.) kell tartalmaznia.
invalidPasswordNotUsernameMessage=Érvénytelen jelszó: nem lehet azonos a felhasználónévvel.
invalidPasswordNotEmailMessage=Érvénytelen jelszó: nem lehet azonos az e-mail címmel.
invalidPasswordRegexPatternMessage=Érvénytelen jelszó: a jelszó nem illeszkedik a megadott reguláris kifejezés mintára.
invalidPasswordHistoryMessage=Érvénytelen jelszó: nem lehet azonos az utolsó {0} darab, korábban alkalmazott jelszóval.
invalidPasswordGenericMessage=Érvénytelen jelszó: az új jelszó nem felel meg a jelszó házirendnek.

failedToProcessResponseMessage=A válasz üzenet feldolgozása nem sikerült.
httpsRequiredMessage=HTTPS protokoll használata kötelező.
realmNotEnabledMessage=A tartomány inaktív.
invalidRequestMessage=Érvénytelen kérés.
successLogout=Sikeres kilépés.
failedLogout=A kilépés sikertelen.
unknownLoginRequesterMessage=A belépést kérelmező ismeretlen.
loginRequesterNotEnabledMessage=A belépést kérelmező inaktív.
bearerOnlyMessage=Bearer-only alkalmazások nem kezdeményezhetnek böngésző alapú beléptetést.
standardFlowDisabledMessage=Ez a kliens nem kezdeményezhet böngésző alapú beléptetést a megadott válasz típussal. A standard belépési eljárás (flow) tiltott a kliensen.
implicitFlowDisabledMessage=Ez a kliens nem kezdeményezhet böngésző alapú beléptetést a megadott válasz típussal. Az implicit belépési eljárás (flow) tiltott a kliensen.
invalidRedirectUriMessage=Érvénytelen átirányítási cím (URI)
unsupportedNameIdFormatMessage=Nem támogatott NameIDFormat
invalidRequesterMessage=Érvénytelen kérelmező
registrationNotAllowedMessage=A felhasználó regisztráció tiltott.
resetCredentialNotAllowedMessage=A jelszó visszaállítás tiltott.

permissionNotApprovedMessage=A jogosultság nincsen jóváhagyva.
noRelayStateInResponseMessage=Nincsen "relay state" a személyazonosság-kezelő válaszüzenetében.
insufficientPermissionMessage=Nincs elég jogosultság a fiókok összekötéséhez.
couldNotProceedWithAuthenticationRequestMessage=A személyazonosság-kezelő felé indított hitelesítési kérés sikertelen.
couldNotObtainTokenMessage=Nem sikerült tokent igényelni a személyazonosság-kezelőtől.
unexpectedErrorRetrievingTokenMessage=Váratlan hiba történt a személyazonosság-kezelő token igénylése közben.
unexpectedErrorHandlingResponseMessage=Váratlan hiba történt a személyazonosság-kezelő válaszüzenetének feldolgozása közben.
identityProviderAuthenticationFailedMessage=Nem sikerült a személyazonosság-kezelőn keresztül intézett hitelesítés.
couldNotSendAuthenticationRequestMessage=Nem sikerült a személyazonosság-kezelőhöz intézett hitelesítés kérés elküldése.
unexpectedErrorHandlingRequestMessage=Váratlan hiba történt a személyazonosság-kezelő hitelesítés kérés feldolgozása közben.
invalidAccessCodeMessage=Érvénytelen hozzáférési kód.
sessionNotActiveMessage=A munkamenet inaktív.
invalidCodeMessage=Hiba történt, kérem, lépjen be újra az alkalmazásán keresztül.
cookieNotFoundMessage=Süti nem található. Kérem, ellenőrizze, hogy a böngészőjében engedélyezve vannak-e a sütik.
insufficientLevelOfAuthentication=A kért hitelesítési szint nem teljesült
identityProviderUnexpectedErrorMessage=Váratlan hiba történt a személyazonosság-kezelőn keresztül intézett hitelesítés során.
identityProviderMissingStateMessage=Nincsen "state" a személyazonosság-kezelő válaszüzenetében.
identityProviderInvalidResponseMessage=Érvénytelen válasz a személyazonosság-kezelőtől.
identityProviderInvalidSignatureMessage=Érvénytelen aláírás a személyazonosság-kezelő válaszüzenetében.
identityProviderNotFoundMessage=A megadott azonosítóval személyazonosság-kezelő nem található.
identityProviderLinkSuccess=Sikeresen megerősítette e-mail címét. Kérem, térjen vissza az eredeti böngészőjébe, és onnan folytassa a belépési eljárást.
staleCodeMessage=Ez a lap már nem érvényes, kérem, térjen vissza az alkalmazásába és lépjen be ismét.
realmSupportsNoCredentialsMessage=Ez a tartomány nem támogat jelszó alapú azonosítást.
credentialSetupRequired=Belépés sikertelen, jelszó beállítás szükséges.
identityProviderNotUniqueMessage=Ez a tartomány több személyazonosság-kezelőt támogat. Nem sikerült meghatározni, hogy melyik személyazonosság-kezelőt kellene a hitelesítéshez alkalmazni.
emailVerifiedMessage=Az e-mail címét megerősítettük.
staleEmailVerificationLink=Az a hivatkozás, amelyikre rákattintott elévült és érvényét vesztette. Talán már korábban megerősítette az e-mail címét?
identityProviderAlreadyLinkedMessage=A(z) {0}-tól/től visszakapott összekapcsolt személyazonosság már össze van kötve egy másik felhasználói fiókkal.
confirmAccountLinking=Erősítse meg a(z) {0} személyazonosság-kezelő {1} fiókjának összekötését a felhasználói fiókjával.
confirmEmailAddressVerification=Erősítse meg a(z) {0} e-mail cím érvényességét.
confirmExecutionOfActions=Hajtsa végre a következő művelet(ek)et

backToApplication=&laquo; Vissza az alkalmazásba
missingParameterMessage=Hiányzó paraméterek\: {0}
clientNotFoundMessage=A kliens nem található.
clientDisabledMessage=A kliens inaktív.
invalidParameterMessage=Érvénytelen paraméter\: {0}
alreadyLoggedIn=Már korábban belépett.
differentUserAuthenticated=Ebben a munkamenetben már korábban belépett ''{0}'' felhasználónévvel. Kérem, előbb lépjen ki a munkamenetből.
brokerLinkingSessionExpired=Ügynök fiók összekötést kezdeményezett, de az aktuális munkamenete már érvénytelen.
proceedWithAction=&raquo; Kattintson ide a folytatáshoz
acrNotFulfilled=A hitelesítési követelmények nem teljesültek

requiredAction.CONFIGURE_TOTP=Egyszer használatos jelszó (OTP) beállítása
requiredAction.TERMS_AND_CONDITIONS=Felhasználási feltételek
requiredAction.UPDATE_PASSWORD=Jelszó csere
requiredAction.UPDATE_PROFILE=Fiók adatok módosítása
requiredAction.VERIFY_EMAIL=E-mail cím megerősítése
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Biztonsági kódok generálása
requiredAction.webauthn-register-passwordless=Jelszó nélküli WebAuthn regisztrálása

invalidTokenRequiredActions=A linkben található szükséges műveletek érvénytelenek.

doX509Login=Beléptetés mint\:
clientCertificate=X509 kliens tanúsítvány\:
noCertificate=[Nincs tanúsítvány]


pageNotFound=A kért lap nem található
internalServerError=Belső hiba történt

console-username=Felhasználónév:
console-password=Jelszó:
console-otp=Egyszer használatos jelszó (OTP):
console-new-password=Új jelszó:
console-confirm-password=Jelszó megerősítés:
console-update-password=Cserélje le jelszavát.
console-verify-email=Meg kell erősítenie az e-mail címét. Egy e-mail üzenetet küldtünk a(z) {0} e-mail címre amely egy megerősítő kódot tartalmaz. Kérem, írja be a kapott kódot a lenti beviteli mezőbe.
console-email-code=e-mail  üzenetben kapott ellenőrző kód:
console-accept-terms=Elfogadja a felhasználási feltételeket? [i/n]:
console-accept=i

# Openshift messages
openshift.scope.user_info=Felhasználó adatok
openshift.scope.user_check-access=Felhasználó hozzáférés adatok
openshift.scope.user_full=Teljes hozzáférés
openshift.scope.list-projects=Projektek listája

# SAML authentication
saml.post-form.title=Hitelesítés átirányítás
saml.post-form.message=Átirányítás folyamatban, kérem, várjon.
saml.post-form.js-disabled=A JavaScript nincs engedélyezve. A folytatás előtt ajánlott bekapcsolni a JavaScript támogatást. Kattintson a lenti gombra a folytatáshoz. 
saml.artifactResolutionServiceInvalidResponse=Az artifact nem található.

#authenticators
otp-display-name=Hitelesítő alkalmazás
otp-help-text=Adja meg az ellenőrző kódot a hitelesítő alkalmazásból
otp-reset-description=Melyik OTP beállítást kell eltávolítani?
password-display-name=Jelszó
password-help-text=Lépjen be a jelszava megadásával
auth-username-form-display-name=Felhasználónév
auth-username-form-help-text=Kezdje meg a belépést a felhasználónevének megadásával
auth-username-password-form-display-name=Felhasználónév és jelszó
auth-username-password-form-help-text=Lépjen be a felhasználóneve és jelszava megadásával.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Biztonsági hitelesítő kód
auth-recovery-authn-code-form-help-text=Írjon be egy biztonsági kódot a korábban generáltak közül.
auth-recovery-code-info-message=Írja be a használni kívánt biztonsági kódot
auth-recovery-code-prompt=Biztonsági kód #{0}
auth-recovery-code-header=Bejelentkezés egy biztonsági kód segítségével
recovery-codes-error-invalid=Érvénytelen biztonsági kód
recovery-code-config-header=Biztonsági hitelesítő kódok
recovery-code-config-warning-title=Ezen kódok megtekintésére nincs lehetőség az oldal elhagyása után
recovery-code-config-warning-message=Nyomtassa ki, töltse le vagy mentse el a kódokat egy biztonságos helyre. A beállítás megszakítása törli ezeket a kódokat a felhasználói fiókjából.
recovery-codes-print=Nyomtatás
recovery-codes-download=Letöltés
recovery-codes-copy=Másolás
recovery-codes-copied=Vágólapra másolva
recovery-codes-confirmation-message=Elmentettem a biztonsági kódokat egy biztonságos helyre
recovery-codes-action-complete=Beállítás befejezése
recovery-codes-action-cancel=Beállítás megszakítása
recovery-codes-download-file-header=Tartsa a biztonsági kódokat egy biztonságos helyen
recovery-codes-download-file-description=A biztonsági kódok egyszer használatos jelkódok, melyek segítségével bejelentkezhet a fiókjába, ha nem fér hozzá a hitelesítő alkalmazáshoz.
recovery-codes-download-file-date=Biztonsági kódok generálva
recovery-codes-label-default=Biztonsági kódok

# WebAuthn
webauthn-display-name=Biztonsági kulcs
webauthn-help-text=Használja a biztonsági kulcsát a belépéshez.
webauthn-passwordless-display-name=Biztonsági kulcs
webauthn-passwordless-help-text=Használja a biztonsági kulcsát a jelszómentes belépéshez.
webauthn-login-title=Biztonsági kulcs alapú belépés
webauthn-registration-title=Biztonsági kulcs regisztráció
webauthn-available-authenticators=Elérhető hitelesítő alkalmazások
webauthn-unsupported-browser-text=A WebAuthn protokoll a böngésző által nem támogatott. Próbálja egy másikkal, vagy vegye fel a kapcsolatot az alkalmazás adminisztrátorával.
webauthn-doAuthenticate=Bejelentkezés biztonsági kulccsal
webauthn-createdAt-label=Létrehozva

# WebAuthn Error
webauthn-error-title=Biztonsági kulcs hiba
webauthn-error-registration=Nem sikerült regisztrálni a biztonsági kulcsot.
webauthn-error-api-get=Nem sikerült a hitelesítés a biztonsági kulccsal.
webauthn-error-different-user=Az először hitelesített felhasználó nem az, akit a biztonsági kulccsal azonosítottunk.
webauthn-error-auth-verification=A biztonsági kulcs alapú hitelesítés eredménye érvénytelen.
webauthn-error-register-verification=A biztonsági kulcs alapú regisztráció eredménye érvénytelen.
webauthn-error-user-not-found=Ismeretlen felhasználót hitelesítettünk a biztonsági kulcs alapján.

# Identity provider
identity-provider-redirector=Összekötés másik személyazonosság-kezelővel
identity-provider-login-label=Egyéb bejelentkezési módok
idp-email-verification-display-name=E-mail megerősítés
idp-email-verification-help-text=Felhasználói fiók összekapcsolása az e-mail cím megerősítésével.
idp-username-password-form-display-name=Felhasználónév és jelszó
idp-username-password-form-help-text=Felhasználói fiók összekapcsolása bejelentkezéssel.

finalDeletionConfirmation=A felhasználói fiók törlése után annak visszaállítására nincs mód. A fiókja megtartásához kattintson a Mégse gombra.
irreversibleAction=Ez a művelet visszavonhatatlan
deleteAccountConfirm=Felhasználói fiók törlésének megerősítése

deletingImplies=A felhasználói fiókjának törlésével jár:
errasingData=Összes adatának törlése
loggingOutImmediately=Azonnali kijelentkezés
accountUnusable=Az alkalmazás további használata nem lesz lehetséges ezzel a felhasználói fiókkal
userDeletedSuccessfully=Felhasználói fiók törlése sikeres

access-denied=Hozzáférés megtagadva
access-denied-when-idp-auth=Hozzáférés megtagadva hitelesítés során: {0}

frontchannel-logout.title=Kijelentkezés
frontchannel-logout.message=A következő alkalmazásokból jelentkezik ki
logoutConfirmTitle=Kijelentkezés
logoutConfirmHeader=Valóban ki szeretne jelentkezni?
doLogout=Kijelentkezés

readOnlyUsernameMessage=A felhasználónév nem módosítható.
