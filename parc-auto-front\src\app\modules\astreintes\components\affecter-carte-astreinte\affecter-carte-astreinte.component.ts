import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { ToastrService } from 'ngx-toastr';
import { AstreinteDto } from '../../models/astreinte.model';

interface CarteCarburant {
  id: number;
  numero: string;
  solde: number;
  statut: string;
  typeCarburant: string;
}

@Component({
  selector: 'app-affecter-carte-astreinte',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatRadioModule
  ],
  templateUrl: './affecter-carte-astreinte.component.html',
  styleUrls: ['./affecter-carte-astreinte.component.scss']
})
export class AffecterCarteAstreinteComponent implements OnInit {
  carburantForm: FormGroup;
  cartesDisponibles: CarteCarburant[] = [];
  loading = false;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AffecterCarteAstreinteComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { astreinte: AstreinteDto },
    private toastr: ToastrService
  ) {
    this.carburantForm = this.fb.group({
      typeConsommation: ['carte', Validators.required],
      carteId: [''],
      montantEstime: ['', [Validators.min(0)]]
    });
  }

  ngOnInit(): void {
    this.loadCartesDisponibles();
    this.setupFormValidation();
  }

  setupFormValidation(): void {
    this.carburantForm.get('typeConsommation')?.valueChanges.subscribe(type => {
      const carteControl = this.carburantForm.get('carteId');
      const montantControl = this.carburantForm.get('montantEstime');

      if (type === 'carte') {
        carteControl?.setValidators([Validators.required]);
        montantControl?.clearValidators();
      } else {
        carteControl?.clearValidators();
        carteControl?.setValue('');
        montantControl?.setValidators([Validators.required, Validators.min(0)]);
      }
      carteControl?.updateValueAndValidity();
      montantControl?.updateValueAndValidity();
    });
  }

  loadCartesDisponibles(): void {
    // Données mockées pour les cartes carburant
    this.cartesDisponibles = [
      {
        id: 1,
        numero: 'CC001',
        solde: 250.50,
        statut: 'ACTIVE',
        typeCarburant: 'Essence'
      },
      {
        id: 2,
        numero: 'CC002',
        solde: 180.75,
        statut: 'ACTIVE',
        typeCarburant: 'Diesel'
      },
      {
        id: 3,
        numero: 'CC003',
        solde: 320.00,
        statut: 'ACTIVE',
        typeCarburant: 'Essence'
      },
      {
        id: 4,
        numero: 'CC004',
        solde: 95.25,
        statut: 'ACTIVE',
        typeCarburant: 'Diesel'
      },
      {
        id: 5,
        numero: 'CC005',
        solde: 450.00,
        statut: 'ACTIVE',
        typeCarburant: 'Essence'
      }
    ];
  }

  onSubmit(): void {
    if (this.carburantForm.valid) {
      this.loading = true;

      const formValue = this.carburantForm.value;
      let selectedCarte = null;

      if (formValue.typeConsommation === 'carte') {
        selectedCarte = this.cartesDisponibles.find(c => c.id === formValue.carteId);
      }

      // Simulation d'une requête API
      setTimeout(() => {
        this.loading = false;

        let message = '';
        if (formValue.typeConsommation === 'carte') {
          message = `Carte ${selectedCarte?.numero} attribuée avec succès`;
        } else {
          message = `Frais de ${formValue.montantEstime} DT enregistrés`;
        }

        this.toastr.success(message, 'Attribution réussie');

        this.dialogRef.close({
          success: true,
          type: formValue.typeConsommation,
          carte: selectedCarte,
          montant: formValue.montantEstime
        });
      }, 1000);
    } else {
      this.markFormGroupTouched();
    }
  }

  onCheckboxChange(carteId: number): void {
    const currentValue = this.carburantForm.get('carteId')?.value;
    if (currentValue === carteId) {
      this.carburantForm.patchValue({ carteId: null });
    } else {
      this.carburantForm.patchValue({ carteId: carteId });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.carburantForm.controls).forEach(key => {
      const control = this.carburantForm.get(key);
      control?.markAsTouched();
    });
  }

  getCarteDisplayText(carte: CarteCarburant): string {
    return `${carte.numero} - ${carte.typeCarburant} (Solde: ${carte.solde} DT)`;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.carburantForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.carburantForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        switch (fieldName) {
          case 'carteId': return 'Carte carburant est requise';
          case 'montantEstime': return 'Montant estimé est requis';
          default: return 'Ce champ est requis';
        }
      }
      if (field.errors['min']) {
        return 'Le montant doit être supérieur à 0';
      }
    }
    return '';
  }

  get isCarteType(): boolean {
    return this.carburantForm.get('typeConsommation')?.value === 'carte';
  }

  get isFraisType(): boolean {
    return this.carburantForm.get('typeConsommation')?.value === 'frais';
  }

  get isCashType(): boolean {
    return this.carburantForm.get('typeConsommation')?.value === 'cash';
  }
}
