spring.application.name=ms-reservation

# Server port
server.port=9006

# Eureka Client Configuration
eureka.client.service-url.defaultZone=http://localhost:9102/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true

# DataSource Configuration
spring.datasource.url=*****************************************
spring.datasource.username=parcauto
spring.datasource.password=parcauto
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=postgresql

# Springdoc OpenAPI Configuration
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true

# OAuth2 Security Configuration for the REST API
spring.security.oauth2.resourceserver.jwt.issuer-uri: http://localhost:8080/realms/parc-auto
spring.security.oauth2.resourceserver.jwt.jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs
jwt.auth.converter.resource-id= ms_reservation
jwt.auth.converter.principle-attribute= preferred_username

# OAuth2 Parameters Configuration for Swagger UI
springdoc.swagger-ui.oauth.client-id=ms_reservation
springdoc.swagger-ui.oauth.client-secret=LFyxxXmdvDvbNxWuLU8jvcu99T0AnSzQ

# NFS File Storage Path Configuration
storage.path=/app/storage

vehicule.url=http://localhost:9003/api/v1/vehicule
mission.url = http://localhost:9005/api/missions
user.url = http://localhost:9005/api/keycloak/users
astreinte.url=${ASTREINTE_URL:http://localhost:9007}

